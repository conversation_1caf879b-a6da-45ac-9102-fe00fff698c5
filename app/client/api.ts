import { ACCESS_CODE_PREFIX, Model<PERSON><PERSON>ider, ServiceProvider } from "../constant";
import { ChatMessageTool, ModelType, useAccessStore, useChatStore } from "../store";

import { DeepSeekApi } from "./platforms/deepseek";

export const ROLES = ["system", "user", "assistant"] as const;
export type MessageRole = (typeof ROLES)[number];

export const Models = ["gpt-3.5-turbo", "gpt-4"] as const;
export const TTSModels = ["tts-1", "tts-1-hd"] as const;
export type ChatModel = ModelType;

export interface MultimodalContent {
    type: "text" | "image_url";
    text?: string;
    image_url?: {
        url: string;
    };
}

export interface RequestMessage {
    role: MessageRole;
    content: string | MultimodalContent[];
}

export interface LLMConfig {
    model: string;
    temperature?: number;
    top_p?: number;
    stream?: boolean;
    presence_penalty?: number;
    frequency_penalty?: number;
    max_tokens?: number;
    providerName?: string;
}

export interface SpeechOptions {
    model: string;
    input: string;
    voice: string;
    response_format?: string;
    speed?: number;
    onController?: (controller: AbortController) => void;
}

export interface ChatOptions {
    messages: RequestMessage[];
    config: LLMConfig;

    onUpdate?: (message: string, chunk: string) => void;
    onFinish: (message: string, responseRes: Response) => void;
    onError?: (err: Error) => void;
    onController?: (controller: AbortController) => void;
    onBeforeTool?: (tool: ChatMessageTool) => void;
    onAfterTool?: (tool: ChatMessageTool) => void;
}

export interface LLMUsage {
    used: number;
    total: number;
}

export interface LLMModel {
    name: string;
    displayName?: string;
    available: boolean;
    provider: LLMModelProvider;
    sorted: number;
}

export interface LLMModelProvider {
    id: string;
    providerName: string;
    providerType: string;
    sorted: number;
}

export abstract class LLMApi {
    abstract chat(options: ChatOptions): Promise<void>;
    abstract speech(options: SpeechOptions): Promise<ArrayBuffer>;
    abstract usage(): Promise<LLMUsage>;
    abstract models(): Promise<LLMModel[]>;
}

export class ClientApi {
    public llm: LLMApi;

    constructor(provider: ModelProvider = ModelProvider.DeepSeek) {
        switch (provider) {
            case ModelProvider.DeepSeek:
                this.llm = new DeepSeekApi();
                break;
            default:
                this.llm = new DeepSeekApi();
        }
    }

    config() {}

    prompts() {}

    masks() {}
}

export function getBearerToken(apiKey: string, noBearer: boolean = false): string {
    return validString(apiKey) ? `${noBearer ? "" : "Bearer "}${apiKey.trim()}` : "";
}

export function validString(value: string): boolean {
    return value?.length > 0;
}

export function getHeaders(ignoreHeaders: boolean = false) {
    const accessStore = useAccessStore.getState();
    const chatStore = useChatStore.getState();
    let headers: Record<string, string> = {};
    if (!ignoreHeaders) {
        headers = {
            "Content-Type": "application/json",
            Accept: "application/json",
        };
    }

    function getConfig() {
        const modelConfig = chatStore.currentSession().modelConfig ?? {};
        const isDeepSeek = modelConfig.providerName === ServiceProvider.DeepSeek;
        const isEnabledAccessControl = accessStore.enabledAccessControl();
        const apiKey = isDeepSeek ? accessStore.deepseekApiKey : "";

        return {
            isDeepSeek,
            apiKey,
            isEnabledAccessControl,
        };
    }

    const { apiKey, isEnabledAccessControl } = getConfig();

    const bearerToken = getBearerToken(apiKey);

    if (bearerToken) {
        headers["Authorization"] = bearerToken;
    } else if (isEnabledAccessControl && validString(accessStore.accessCode)) {
        headers["Authorization"] = getBearerToken(ACCESS_CODE_PREFIX + accessStore.accessCode);
    }

    return headers;
}

export function getClientApi(provider: ServiceProvider): ClientApi {
    switch (provider) {
        case ServiceProvider.DeepSeek:
            return new ClientApi(ModelProvider.DeepSeek);

        default:
            return new ClientApi(ModelProvider.DeepSeek);
    }
}
