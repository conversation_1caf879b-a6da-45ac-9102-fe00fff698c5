import http from "@/app/request";
import { StreamMessage } from "@/app/typing";
import {
    DeprecateJobFlowReq,
    FlowItemParams,
    JobFlowReq,
    ProcessFlowReq,
    TodoItemReq,
} from "@/app/store/modules/dashboard/jobFlow";

/**
 *
 * @param params { pageNum: number; pageSize: number } 请求参数
 * @returns 当前用户的流程列表
 */
export const getFlowListApi = (params: { pageNum: number; pageSize: number }) => {
    return http.get("iflow/flow/my/list", params);
};

/**
 *
 * @param flowInstanceId 流程实例id
 * @returns 流程详情，所有流程节点，以及当前正在进行中的流程信息
 */
export const getFlowDetailApi = (flowInstanceId: string) => {
    return http.get("iflow/flow/detail", { flowInstanceId });
};

/**
 *
 * @param params
 * @returns 获取代办列表
 */
export const getTodoListApi = (params: TodoItemReq) => {
    return http.get("todo/list", params);
};

/**
 *
 * @param params FlowItemParams 请求参数
 * @param onMessage 流式接受消息
 * @param onError 错误后回调
 * @param onComplete 完成后回调
 * @returns
 */
export const getAIMessageApi = async (
    params: FlowItemParams,
    onMessage: (message: StreamMessage) => void,
    onError: () => void,
    onComplete: () => void
) => {
    return http.stream(
        "jobSpec/ai/generate",
        params,
        (message) => {
            // 更新UI显示消息
            onMessage(message);
        },
        (error) => {
            // 处理错误
            console.error("流式请求错误:", error);
            onError();
        },
        () => {
            // 请求完成
            console.log("finish: 流式请求完成");
            onComplete();
        }
    );
};

/**
 *
 * @param jobJd 职位jd
 * @returns 生成AI生成的岗位标签
 */
export const getAITagsApi = async (jobJd: string) => {
    return http.post("jobTag/generate-tags", { jobJd });
};

/**
 *
 * @param param { layer: string; label: string } 层级；标签类别
 * @returns 获取所有标签
 */
export const getAllTagsApi = async ({ layer, label }: { layer: string; label: string }) => {
    return http.get("jobSpec/tags/list", { layer, label });
};

/**
 *
 * @param {} params
 * @returns 保存招聘需求，发起流程
 */
export const submitJobFlow = (params: JobFlowReq[]) => {
    return http.post("jobSpec/save", params);
};

/**
 *
 * @param params
 * @returns 更新招聘需求
 */
export const updateJobFlowApi = (params: JobFlowReq[]) => {
    return http.post("jobSpec/update", params);
};

/**
 *
 * @param flowId 招聘审批id
 * @returns 返回招聘审批详情
 */
export const getJobFlowDetailApi = (flowId: string) => {
    return http.get(`jobSpec/job/${flowId}`);
};

/**
 *
 * @param params {nodeInstanceId: string; status: string; comment: string} 节点实例id；状态；备注
 * @returns 流程处理
 */
export const processFlowApi = (params: ProcessFlowReq) => {
    return http.post("iflow/flow/handle", params);
};

/**
 *
 * @returns 下载招聘需求模板
 */
export const downloadJobFlowTemplateApi = () => {
    return http.downloadFile("jobSpec/download/excelTemplate", "GET", "招聘需求模板.xlsx");
};

/**
 *
 * @param params FormData excel文件
 * @returns 上传招聘需求excel
 */
export const uploadJobFlowExcelApi = (params: FormData) => {
    return http.post("jobSpec/import/excel", params);
};

/**
 *
 * @param type 1-发起审批权限；2-职位需求查看权限
 * @returns 校验当前用户是否存在权限
 */
export const checkRolePermissionApi = (type: number) => {
    return http.get("builtin/checkRole", { type });
};

export const checkSensitiveWordsApi = async (
    params: {
        text: string;
    },
    onMessage: (message: StreamMessage) => void,
    onError: () => void,
    onComplete: () => void
) => {
    return http.stream(
        "util/sensitive-word/check",
        params,
        (message) => {
            onMessage(message);
        },
        (error) => {
            console.error("流式请求错误:", error);
            onError();
        },
        () => {
            console.log("finish: 流式请求完成");
            onComplete();
        }
    );
};

/**
 *
 * @param params { category: 分类; content: 保存的json数据 }
 * @returns 缓存表单数据
 */
export const saveTempJobFlowDataApi = (params: { category: string; content: string }) => {
    return http.post("util/shelved/save", params);
};

/**
 *
 * @param category 分类
 * @returns 获取缓存数据
 */
export const getTempJobFlowDataApi = (category: string) => {
    return http.get("util/shelved/get", { category });
};

/**
 * @param category 分类
 * @returns 删除缓存数据
 */
export const deleteTempJobFlowDataApi = (category: string) => {
    return http.post(`util/shelved/delete?category=${category}`);
};

/**
 *
 * @param params { type: 类型 1-大需求，2-小需求; ids: 需求ids }
 * @returns 废弃需求流程
 */
export const deprecateJobFlowApi = (params: DeprecateJobFlowReq) => {
    return http.post("jobSpec/deprecatedJob", params);
};

/**
 *
 * @param jobSpecId 职位Id
 * @returns 按照小需求id查询大需求信息
 */
export const getJobPublishByJobSpecsApi = (jobSpecId: number) => {
    return http.get(`jobSpec/find/jobPublish/bySpec/${jobSpecId}`);
};

/**
 *
 * @param params
 * @returns 导出职位信息
 */
export const exportJobSpecsApi = (params: {
    orgId?: string;
    position?: string;
    outPosition?: string;
    status?: number | string;
    fileName: string;
}) => {
    return http.downloadFile("jobSpec/exportSpecs", "GET", params.fileName, {
        orgId: params?.orgId,
        position: params?.position,
        status: params?.status,
        outPosition: params?.outPosition,
    });
};

/**
 *
 * @param params
 * @returns 导出职位对应面试流程
 */
export const exportInterviewFlowByJobSpecApi = (params: { ids: number[]; fileName: string }) => {
    return http.downloadFile("jobSpec/download/trackHandlerTemplate", "POST", params.fileName, {
        ids: params?.ids,
    });
};
