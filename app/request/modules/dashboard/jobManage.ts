import http from "@/app/request";
import { InterviewReq, UpdateInterviewItemReq } from "@/app/store/modules/dashboard/jobDetail";

/**
 *
 * @param jobSpecId 职位ID
 * @returns 查询岗位对应面试流程
 */
export const getInterviewFlowByJobIdApi = (jobSpecId: number) => {
    return http.get("interview/searchProcess", { jobSpecId });
};

/**
 *
 * @param params 新增面试流程参数
 * @returns 新增面试流程
 */
export const addInterviewApi = (params: InterviewReq) => {
    return http.post("interview/saveProcess", params);
};

/**
 *
 * @param params 更新面试流程参数
 * @returns 更新面试流程
 */
export const updateInterviewApi = (params: UpdateInterviewItemReq) => {
    return http.post("interview/updateStages", params);
};

/**
 *
 * @param ids 面试流程ids
 * @returns 删除面试流程
 */
export const deleteInterviewApi = (ids: number[]) => {
    return http.delete(`interview/deleteStages`, ids);
};

/**
 *
 * @param ids 面试流程ids
 * @returns 校验选中的多个岗位需求是否存在相同面试流程，存在则返回对应的面试流程id数组
 */
export const checkIsSameInterviewApi = (ids: number[]) => {
    return http.post("interview/checkConsistency", ids);
};

/**
 *
 * @param orders 排序后的流程id数组
 * @returns 更新流程顺序
 */
export const updateInterviewOrderApi = (orders: string[]) => {
    return http.post("interview/updateStagesOrder", orders);
};

/**
 * 获取阶段列表
 * @returns 获取阶段列表
 */
export const getFlowStageListApi = () => {
    return http.get(`track/dict/stage/list`);
};

/**
 * 获取状态列表
 * @returns 获取状态列表
 */
export const getFlowStatusApi = () => {
    return http.get(`track/dict/state/list`);
};

/**
 *
 * @param jobSpecId 岗位id
 * @returns 获取岗位详情
 */
export const getJobFlowDetailByChildrenJobIdApi = (jobSpecId: number) => {
    return http.get(`track/process/instance/get`, {
        jobSpecId,
    });
};

/**
 *
 * @param data 流程详情
 * @returns 保存面试流程
 */
export const saveInterviewFlowApi = (data: any) => {
    return http.post("track/process/instance/save", data);
};

/**
 *
 * @param jobSpecIds 岗位id数组
 * @returns 批量更新当前职位的面试流程
 */
export const saveBatchInterviewFlowApi = (data: any) => {
    return http.post("track/process/instance/save/batch", data);
};

/**
 * 获取面试模板列表
 * @param params 查询参数，包含分页信息和关键词
 * @returns 面试模板列表数据
 */
// 原接口暂时注释
export const getInterviewTemplateListApi = (params?: {
    page?: number;
    size?: number;
    title?: string;
    orgId?: string;
}) => {
    return http.get(`interview/searchTemplates`, params);
};
