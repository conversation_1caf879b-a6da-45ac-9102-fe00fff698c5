import http from "@/app/request";
import { MyFlowReq, PublishMyFlowReq } from "@/app/store/modules/dashboard/jobFlow";

/**
 *
 * @param params {orgId: 部门id; status: 状态(0=需求审批中 1=未发布 2=发布审批中 3=进行中 4=已完成 5=已超额 6=已关闭); page: 页码; size: 每页条数}
 * @returns 获取招聘需求发布前列表
 */
export const getMyJobListApi = (params: MyFlowReq) => {
    return http.get("jobSpec/searchRecruitments", params);
};

/**
 *
 * @param ids 招聘需求id数组
 * @returns 返回校验和结果，聚合数据后返回整体信息
 */
export const getJobFlowAggregationApi = (ids: number[]) => {
    return http.post("jobSpec/batchRecruitments", ids);
};

/**
 *
 * @param params
 * @returns 发布汇总后的招聘需求
 */
export const publishJobFlowApi = (params: PublishMyFlowReq) => {
    return http.post("jobSpec/save/jobPublish", params);
};

/**
 *
 * @param id 汇总招聘需求id
 * @returns 查询汇总招聘需求详情
 */
export const getJobFlowChildrenByIdApi = (id: number) => {
    return http.get(`jobSpec/find/jobPublish/${id}`);
};

/**
 *
 * @param params {orgId: 部门id; status: 状态; position: 职位名称; page: 页码; size: 每页条数}
 * @returns 列表查询汇总招聘需求
 */
export const getMyFlowListApi = (params: MyFlowReq) => {
    return http.get("jobSpec/searchPublishes", params);
};

/**
 *
 * @param params {orgId: 部门id; status: 状态; position: 职位名称;}
 * @returns
 */
export const exportDataApi = (params: {
    orgId?: string;
    position?: string;
    outPosition?: string;
    status?: number | string;
    fileName: string;
}) => {
    return http.downloadFile("jobSpec/exportPublishes", "GET", params.fileName, {
        orgId: params?.orgId,
        position: params?.position,
        status: params?.status,
        outPosition: params?.outPosition,
    });
};
