import { ReqUserList } from "@/app/components/typing";
import http from "@/app/request";
import { getFileBlobReq } from "@/app/store/modules/candidate";

/**
 *
 * @returns 获取部门列表树
 */
export const getAllDepartmentListApi = () => {
    return http.get("department/struct/tree");
};

/**
 *
 * @param params {empName: 工号/姓名; deptCodePath: 部门路径; page: 页码; size: 每页条数}
 * @returns 获取所有用户列表
 */
export const getAllUserListApi = (params: ReqUserList) => {
    return http.get("builtin/searchUsers", params);
};

/**
 *
 * @returns 获取部门列表树(包含权限)
 */
export const getDepartmentListApi = () => {
    return http.get("department/struct/my");
};

/**
 * @param orgNo 组织code
 * @returns 获取岗位列表树
 */
export const getJobListApi = (params?: { orgNo?: string }) => {
    return http.get("builtin/getPositionHierarchy", params);
};

/**
 *
 * @returns 退出登录
 */
export const logoutApi = () => {
    return http.get("auth/logout");
};

/**
 *
 * @param id 文件id
 * @returns
 */
export const previewFileApi = (id: string) => {
    return http.downloadFile("talent/cv/download", "GET", undefined, { id });
};

/**
 *
 * @param dictCode 字典编码
 * @returns 返回字典数据
 */
export const getDictDataByCodeApi = (dictCode: string) => {
    return http.get(`dict/item/${dictCode}`);
};

/**
 *
 * @param params {applicantId: 简历id; jobId: 职位id; isOrigin: 是否原始文件; isManualDistribution: 是否手动分配}
 * @returns 获取文件流
 */
export const getFileApi = async (params: getFileBlobReq) => {
    return http.downloadFile("beisen/previewCV", "GET", undefined, params);
};
