import ky, { HTTPError, KyInstance, KyRequest, Options } from "ky";
import { DefaultParams, RespParams } from "../typing";
import { safeLocalStorage } from "../utils";
import MessageService from "../lib/message";

const prefixUrl = process.env.NEXT_PUBLIC_FETCH_BASE_API;

class Request {
    private keyInstance: KyInstance;
    private requestControllers: Map<string, AbortController>;

    constructor() {
        this.requestControllers = new Map();
        this.keyInstance = ky.create({
            prefixUrl,
            retry: 0,
            timeout: false,
            hooks: {
                // 请求前事件
                beforeRequest: [
                    (request: KyRequest) => {
                        const token = JSON.parse(safeLocalStorage().getItem("chat-userinfo-store") ?? "{}")?.state
                            ?.token;

                        // TODO 后续接入权限token
                        if (token && !request.headers.get("token")) {
                            request.headers.set("Authorization", token);
                        } else {
                            request.headers.delete("Authorization");
                        }
                    },
                ],
                // 错误事件
                beforeError: [
                    async (error: HTTPError) => {
                        console.error("HTTPError", error);
                        MessageService.error(error?.message ?? "未知错误！");
                        // 返回要抛出的 HTTPError 对象
                        return error;
                    },
                ],
                afterResponse: [
                    async (_request, _options, response) => {
                        // 返回数据为流数据后，不校验code
                        if (response.headers.get("Content-Type") !== "application/json") {
                            return;
                        }

                        const responseClone = response.clone();
                        const res: RespParams<any> = await responseClone.json();

                        // 根据服务端返回的 code 进行不同的处理
                        if (response.status === 200) {
                            return;
                        }
                        if (response.status === 401) {
                            const userInfoStore = JSON.parse(safeLocalStorage().getItem("chat-userinfo-store") ?? "{}");
                            userInfoStore.state.token = "";
                            userInfoStore.state.user = {
                                avatar: "",
                                emp_id: "",
                                emp_name: "",
                                gender: "",
                                perms: [],
                                permGroups: [],
                            };

                            safeLocalStorage().setItem("chat-userinfo-store", JSON.stringify(userInfoStore));

                            MessageService.error("登录已过期，请重新登录！");
                            window.location.reload();
                            throw new Error('"登录已过期，请重新登录！');
                        }
                        // 其他错误
                        const errMsg = res?.msg ?? "请求出错";
                        // 提示错误信息
                        MessageService.error(errMsg);
                        // 抛出错误 请求发送方法调用的位置可以捕获到此错误
                        throw new Error(errMsg);
                    },
                ],
            },
        });
    }

    /**
     * 取消指定请求
     * @param requestId 请求ID
     * @returns 是否成功取消
     */
    cancelRequest(requestId: string): boolean {
        const controller = this.requestControllers.get(requestId);
        if (controller) {
            controller.abort();
            this.requestControllers.delete(requestId);
            return true;
        }
        return false;
    }

    /**
     * 取消所有请求
     */
    cancelAllRequests(): void {
        this.requestControllers.forEach((controller) => {
            controller.abort();
        });
        this.requestControllers.clear();
    }

    get(url: string, params?: DefaultParams, options?: DefaultParams) {
        return this.keyInstance(url, {
            searchParams: params,
            method: "GET",
            ...options,
        });
    }

    post(url: string, params?: DefaultParams, options?: DefaultParams) {
        return this.keyInstance(url, { json: params, method: "POST", ...options });
    }

    patch(url: string, params?: DefaultParams, options?: DefaultParams) {
        return this.keyInstance(url, { json: params, method: "PATCH", ...options });
    }

    put(url: string, params?: DefaultParams, options?: DefaultParams) {
        return this.keyInstance(url, { json: params, method: "PUT", ...options });
    }

    delete(url: string, params?: DefaultParams, options?: DefaultParams) {
        return this.keyInstance(url, { json: params, method: "DELETE", ...options });
    }

    async downloadFile(
        url: string,
        method: string = "POST",
        filename?: string,
        params?: DefaultParams,
        options?: DefaultParams
    ): Promise<Blob> {
        const fetchOptions: Options = {
            method,
            ...options,
        };

        if (method === "POST") {
            fetchOptions["json"] = params;
        }
        if (method === "GET") {
            fetchOptions["searchParams"] = params;
        }

        try {
            const response = await this.keyInstance(url, fetchOptions);

            if (!response.ok) {
                throw new Error(`下载失败: ${response.status} ${response.statusText}`);
            }

            const blob = await response.blob();

            // 如果提供了文件名，自动触发下载
            if (filename) {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement("a");
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            }

            return blob;
        } catch (error) {
            if (error instanceof Error) {
                MessageService.error(`文件下载失败: ${error.message}`);
            } else {
                MessageService.error("文件下载失败");
            }
            throw error;
        }
    }

    stream(
        url: string,
        params?: DefaultParams,
        onMessage?: (message: any) => void,
        onError?: (error: any) => void,
        onComplete?: () => void,
        options?: DefaultParams
    ) {
        const MAX_RETRIES = 3;
        const INITIAL_TIMEOUT = 30000;
        const INACTIVITY_TIMEOUT = 30000;
        let retryCount = 0;
        let timeoutId: NodeJS.Timeout | null = null;
        let lastDataTime = Date.now();

        const setupTimeout = (duration: number, message: string) => {
            if (timeoutId) clearTimeout(timeoutId);
            timeoutId = setTimeout(() => {
                const timeSinceLastData = Date.now() - lastDataTime;
                if (timeSinceLastData >= duration) {
                    if (onError) onError(new Error(message));
                }
            }, duration);
        };

        const startStream = async () => {
            let reader: ReadableStreamDefaultReader<Uint8Array> | null = null;

            // 消息队列和打字机效果控制
            const messageQueue: string[] = [];
            let isProcessingQueue = false;
            const TYPEWRITER_DELAY = 10; // 打字机效果延迟（毫秒）
            const BATCH_SIZE = 10; // 每批处理的消息数量

            // 处理消息队列的函数
            const processMessageQueue = async () => {
                if (isProcessingQueue || messageQueue.length === 0) return;

                isProcessingQueue = true;

                while (messageQueue.length > 0) {
                    // 分批处理消息
                    const batch = messageQueue.splice(0, Math.min(BATCH_SIZE, messageQueue.length));

                    for (const message of batch) {
                        await processMessageWithDelay(message);

                        // 如果队列被中断（比如用户取消），则停止处理
                        // if (controller.signal.aborted) {
                        //     isProcessingQueue = false;
                        //     return;
                        // }
                    }

                    // 批次之间的小延迟，避免阻塞主线程
                    if (messageQueue.length > 0) {
                        await new Promise((resolve) => setTimeout(resolve, 10));
                    }
                }

                isProcessingQueue = false;
            };

            // 带延迟的消息处理函数
            const processMessageWithDelay = async (message: string) => {
                return new Promise<void>((resolve) => {
                    setTimeout(() => {
                        processMessage(message);
                        resolve();
                    }, TYPEWRITER_DELAY);
                });
            };

            try {
                // 设置初始连接超时
                setupTimeout(INITIAL_TIMEOUT, "Initial connection timeout");
                const response = await this.keyInstance(url, {
                    json: params,
                    method: "POST",
                    ...options,
                    headers: {
                        Accept: "text/event-stream",
                        "Content-Type": "application/json",
                        Connection: "keep-alive",
                        "Cache-Control": "no-cache",
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}, statusText: ${response.statusText}`);
                }

                if (!response.body) {
                    throw new Error("Response body is null");
                }

                reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = "";

                // 切换到数据接收超时
                setupTimeout(INACTIVITY_TIMEOUT, "Data reception timeout");

                const condition = true;
                while (condition) {
                    const { done, value } = await reader.read();

                    if (done) {
                        if (buffer.trim()) {
                            messageQueue.push(buffer);
                        }
                        // 确保处理完所有剩余消息
                        await processMessageQueue();
                        break;
                    }

                    // 更新最后接收数据的时间
                    lastDataTime = Date.now();
                    // 重置不活动超时
                    setupTimeout(INACTIVITY_TIMEOUT, "Data reception timeout");

                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;

                    const messages = buffer.split("\n\n");
                    buffer = messages.pop() || "";

                    // 将消息添加到队列而不是立即处理
                    for (const message of messages) {
                        if (message.trim()) {
                            messageQueue.push(message);
                        }
                    }

                    // 如果队列不在处理中，启动处理
                    if (!isProcessingQueue) {
                        processMessageQueue();
                    }
                }

                if (onComplete) onComplete();
            } catch (error) {
                if (timeoutId) {
                    clearTimeout(timeoutId);
                    timeoutId = null;
                }

                const isRetryableError =
                    error instanceof Error &&
                    (error.message.includes("network") ||
                        error.message.includes("connection") ||
                        error.message.includes("timeout") ||
                        error.message.includes("abort") ||
                        (error as any).code === "ECONNRESET");

                if (retryCount < MAX_RETRIES && isRetryableError) {
                    retryCount++;
                    const delay = Math.min(1000 * Math.pow(2, retryCount), 5000);
                    console.warn(`Retrying stream connection (${retryCount}/${MAX_RETRIES}) after ${delay}ms`);
                    await new Promise((resolve) => setTimeout(resolve, delay));
                    return;
                }

                if (onError) {
                    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
                    onError(new Error(`Stream error: ${errorMessage}`));
                }
            } finally {
                if (timeoutId) {
                    clearTimeout(timeoutId);
                    timeoutId = null;
                }
                if (reader) {
                    try {
                        reader.releaseLock();
                    } catch (e) {
                        console.warn("Error releasing reader lock:", e);
                    }
                }
            }
        };

        function processMessage(message: string) {
            if (!message.trim()) return;

            const lines = message.split("\n");
            let data = "";

            // 首先尝试查找并处理 data: 前缀的行
            for (const line of lines) {
                const trimmedLine = line.trim();
                if (trimmedLine.startsWith("data:")) {
                    // 处理可能的转义字符
                    data = trimmedLine.slice(5).trim();
                    break;
                }
            }

            // 如果没有找到 data: 前缀的行，使用整个消息
            if (!data) {
                data = message.trim();
            }

            // 确保有数据要处理
            if (!data) return;

            if (onMessage) {
                try {
                    // 检查是否是有效的 JSON 格式
                    if ((data.startsWith("{") && data.endsWith("}")) || (data.startsWith("[") && data.endsWith("]"))) {
                        // 尝试解析 JSON
                        const parsedData = JSON.parse(data);
                        onMessage(parsedData);
                    } else {
                        // 不是 JSON 格式，直接返回文本
                        onMessage(data);
                    }
                } catch (e) {
                    // JSON 解析失败时，记录错误并返回原始文本
                    console.warn("Failed to parse message as JSON:", {
                        error: e,
                        data: data.slice(0, 100) + (data.length > 100 ? "..." : ""), // 限制日志长度
                    });
                    onMessage(data);
                }
            }
        }

        // 启动流
        startStream();

        // 返回取消函数
        return () => {
            if (timeoutId) {
                clearTimeout(timeoutId);
                timeoutId = null;
            }
        };
    }
}

const http = new Request();

export default http;
