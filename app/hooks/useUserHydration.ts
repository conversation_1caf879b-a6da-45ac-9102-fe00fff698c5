// 用于处理数据水合状态 hook
// 专门用于处理SSR环境下用户数据的水合问题
//确保在客户端数据完全加载后才显示真实的用户信息

import { useState, useEffect, useCallback } from "react";
import useUserInfoStore from "@/app/store/userInfo";

export function useUserHydration() {
    const user = useUserInfoStore((state) => state.user);
    const [isHydrated, setIsHydrated] = useState(false);
    const [hasUserData, setHasUserData] = useState(false);
    const [forceUpdate, setForceUpdate] = useState(0);

    const triggerUpdate = useCallback(() => {
        setForceUpdate((prev) => prev + 1);
    }, []);

    useEffect(() => {
        // 在客户端环境下，检查是否有持久化的用户数据
        if (typeof window !== "undefined") {
            const checkStoreData = async () => {
                try {
                    const storeData = localStorage.getItem("chat-userinfo-store");
                    if (storeData) {
                        const parsedData = JSON.parse(storeData);
                        const userData = parsedData.user;
                        if (userData?.emp_name && userData?.emp_id) {
                            setHasUserData(true);
                            triggerUpdate();
                        }
                    }
                } catch (error) {
                    console.warn("水合处理error", error);
                }
            };
            checkStoreData();
            // 设置检查点，确保数据能够及时更新
            const timer = [
                setTimeout(() => {
                    setIsHydrated(true);
                    triggerUpdate();
                    checkStoreData();
                }, 100),
                setTimeout(() => {
                    setIsHydrated(true);
                    triggerUpdate();
                }, 300),
            ];
            return () => timer.forEach((t) => clearTimeout(t));
        }
    }, [triggerUpdate]);

    // 监听用户数据变化
    useEffect(() => {
        if (user?.emp_name && user?.emp_id) {
            setHasUserData(true);
            setIsHydrated(true);
            triggerUpdate();
        }
    }, [user, triggerUpdate]);

    useEffect(() => {
        const unsubscribe = useUserInfoStore.subscribe((state) => {
            if (state.user?.emp_name && state.user?.emp_id) {
                setHasUserData(true);
                setIsHydrated(true);
                triggerUpdate();
            }
        });
        return () => unsubscribe();
    }, [triggerUpdate]);

    return {
        user,
        isHydrated,
        hasUserData,
        forceUpdate,
        shouldShowUserInfo: isHydrated && hasUserData && user?.emp_name && user?.emp_id,
    };
}
