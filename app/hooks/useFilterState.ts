import { useState, useCallback, useMemo } from "react";
import type { Dayjs } from "dayjs";
import { PaginationInfo } from "@/app/typing";

/** 筛选条件接口 */
export interface FilterState {
    /** 学历 */
    education?: string;
    /** 工作年限 */
    workExperience?: string;
    /** 公司 */
    company?: string;
    /** 任职职位 */
    position?: string;
    /** 学校 */
    school?: string;
    /** 技能标签 */
    skills?: string[];
    /** 毕业时间范围 */
    graduationDate?: [Dayjs | null, Dayjs | null] | null;
    /** 搜索关键词 */
    keyword?: string;
}

/** 加载状态接口 */
export interface LoadingState {
    /** 是否正在加载 */
    loading: boolean;
    /** 是否正在搜索 */
    searching: boolean;
}

/** Hook配置接口 */
export interface UseFilterStateConfig {
    /** 初始筛选条件 */
    initialFilters?: FilterState;
    /** 初始分页信息 */
    initialPagination?: Partial<PaginationInfo>;
    /** 默认每页大小 */
    defaultPageSize?: number;
}

/** 筛选Hook返回值接口 */
export interface UseFilterStateReturn {
    /** 当前筛选条件 */
    filters: FilterState;
    /** 分页信息 */
    pagination: PaginationInfo;
    /** 加载状态 */
    loadingState: LoadingState;
    /** 更新单个筛选条件 */
    updateFilter: <K extends keyof FilterState>(key: K, value: FilterState[K]) => void;
    /** 批量更新筛选条件 */
    updateFilters: (newFilters: Partial<FilterState>) => void;
    /** 重置筛选条件 */
    resetFilters: () => void;
    /** 更新分页信息 */
    updatePagination: (pagination: Partial<PaginationInfo>) => void;
    /** 跳转到指定页 */
    goToPage: (page: number) => void;
    /** 改变每页大小 */
    changePageSize: (pageSize: number) => void;
    /** 设置加载状态 */
    setLoading: (loading: boolean) => void;
    /** 设置搜索状态 */
    setSearching: (searching: boolean) => void;
    /** 获取激活的筛选条件数量 */
    activeFilterCount: number;
    /** 检查是否有筛选条件 */
    hasFilters: boolean;
    /** 重置所有状态 */
    resetAll: () => void;
}

/**
 * 筛选状态管理Hook
 * 统一管理筛选条件、分页信息和加载状态
 */
export const useFilterState = (config: UseFilterStateConfig = {}): UseFilterStateReturn => {
    const { initialFilters = {}, initialPagination = {}, defaultPageSize = 20 } = config;

    // 筛选条件状态
    const [filters, setFilters] = useState<FilterState>(initialFilters);

    // 分页状态
    const [pagination, setPagination] = useState<PaginationInfo>({
        pageNum: 0,
        pageSize: defaultPageSize,
        total: 0,
        ...initialPagination,
    });

    // 加载状态
    const [loadingState, setLoadingState] = useState<LoadingState>({
        loading: false,
        searching: false,
    });

    /**
     * 更新单个筛选条件
     * 筛选条件变化时自动重置到第一页
     */
    const updateFilter = useCallback(<K extends keyof FilterState>(key: K, value: FilterState[K]) => {
        setFilters((prev) => ({
            ...prev,
            [key]: value,
        }));
        // 筛选条件变化时重置到第一页
        setPagination((prev) => ({
            ...prev,
            pageSize: initialPagination.pageSize ?? defaultPageSize,
        }));
    }, []);

    /**
     * 批量更新筛选条件
     * 筛选条件变化时自动重置到第一页
     */
    const updateFilters = useCallback((newFilters: Partial<FilterState>) => {
        setFilters((prev) => ({
            ...prev,
            ...newFilters,
        }));
        // 筛选条件变化时重置到第一页
        setPagination((prev) => ({
            ...prev,
            pageNum: 0,
        }));
    }, []);

    /**
     * 重置筛选条件
     */
    const resetFilters = useCallback(() => {
        setFilters({});
        setPagination((prev) => ({
            ...prev,
            pageNum: 0,
        }));
    }, []);

    /**
     * 更新分页信息
     */
    const updatePagination = useCallback((newPagination: Partial<PaginationInfo>) => {
        setPagination((prev) => ({
            ...prev,
            ...newPagination,
        }));
    }, []);

    /**
     * 跳转到指定页
     */
    const goToPage = useCallback((page: number) => {
        setPagination((prev) => ({
            ...prev,
            pageNum: page,
        }));
    }, []);

    /**
     * 改变每页大小
     */
    const changePageSize = useCallback((pageSize: number) => {
        setPagination((prev) => ({
            ...prev,
            pageSize,
            pageNum: 0, // 改变每页大小时重置到第一页
        }));
    }, []);

    /**
     * 设置加载状态
     */
    const setLoading = useCallback((loading: boolean) => {
        setLoadingState((prev) => ({
            ...prev,
            loading,
        }));
    }, []);

    /**
     * 设置搜索状态
     */
    const setSearching = useCallback((searching: boolean) => {
        setLoadingState((prev) => ({
            ...prev,
            searching,
        }));
    }, []);

    /**
     * 重置所有状态
     */
    const resetAll = useCallback(() => {
        setFilters({});
        setPagination({
            pageNum: 0,
            pageSize: defaultPageSize,
            total: 0,
        });
        setLoadingState({
            loading: false,
            searching: false,
        });
    }, [defaultPageSize]);

    /**
     * 计算激活的筛选条件数量
     */
    const activeFilterCount = useMemo(() => {
        return Object.values(filters).filter((value) => {
            if (Array.isArray(value)) {
                return value.length > 0;
            }
            return value !== undefined && value !== null && value !== "";
        }).length;
    }, [filters]);

    /**
     * 检查是否有筛选条件
     */
    const hasFilters = useMemo(() => {
        return activeFilterCount > 0;
    }, [activeFilterCount]);

    return {
        filters,
        pagination,
        loadingState,
        updateFilter,
        updateFilters,
        resetFilters,
        updatePagination,
        goToPage,
        changePageSize,
        setLoading,
        setSearching,
        activeFilterCount,
        hasFilters,
        resetAll,
    };
};
