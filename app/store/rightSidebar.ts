import { create } from "zustand";
import { Path } from "../constant";

export interface RightSidebarState {
    isVisible: boolean;
    setVisible: (val: boolean, currentPath?: string) => void;
    header: string | JSX.Element;
    setHeader: (val: string | JSX.Element) => void;
    content: string | JSX.Element;
    setContent: (val: string | JSX.Element) => void;
    closeCallback?: () => void;
    setCloseCallback: (val?: () => void) => void;
}

const useRightSidebarStore = create<RightSidebarState>((set) => {
    return {
        isVisible: false,
        header: "title",
        content: "",
        setVisible: (isVisible: boolean, currentPath?: string) =>
            set((state) => {
                if (state.isVisible !== isVisible) {
                    if (currentPath === Path.Chat) {
                        document.documentElement.style.setProperty(
                            "--right-sidebar-width",
                            isVisible ? "920px" : "0px"
                        );
                    } else {
                        document.documentElement.style.setProperty(
                            "--right-sidebar-width",
                            isVisible ? "600px" : "0px"
                        );
                    }
                }

                return { isVisible };
            }),
        setHeader: (header: string | JSX.Element) => set(() => ({ header })),
        setContent: (content: string | JSX.Element) => set(() => ({ content })),
        setCloseCallback: (closeCallback?: () => void) => set(() => ({ closeCallback })),
    };
});

export default useRightSidebarStore;
