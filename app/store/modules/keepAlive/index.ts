import { create } from "zustand";

export interface KeepAliveState<T = any> {
    // 按路由存储的缓存数据
    cachedData: Record<string, T>;
    // 为特定路由添加/更新缓存数据
    setCachedData: (router: string, data: T) => void;
    // 获取特定路由的缓存数据
    getCachedData: (router: string) => T | undefined;
    // 清除特定路由的缓存数据
    clearCachedData: (router: string) => void;
    // 清除所有缓存数据
    clearAllCachedData: () => void;
}

const useKeepAliveStore = create<KeepAliveState>()((set, get) => ({
    cachedData: {},
    // 为特定路由添加/更新缓存数据
    setCachedData: (router: string, data: any) =>
        set((state) => ({
            cachedData: {
                ...state.cachedData,
                [router]: data,
            },
        })),
    // 获取特定路由的缓存数据
    getCachedData: (router: string) => {
        const state = get();
        console.log("aabcd", state.cachedData);
        return state.cachedData[router];
    },
    // 清除特定路由的缓存数据
    clearCachedData: (router: string) =>
        set((state) => {
            const newCachedData = { ...state.cachedData };
            delete newCachedData[router];
            return {
                cachedData: newCachedData,
            };
        }),
    // 清除所有缓存数据
    clearAllCachedData: () => set({ cachedData: {} }),
}));

export default useKeepAliveStore;
