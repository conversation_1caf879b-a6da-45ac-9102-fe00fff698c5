import { create } from "zustand";
import http from "@/app/request";
import { RespParams } from "@/app/typing";
import { InterviewStatus } from "../interview-time";

interface GetInterviewerListRequest {
    gmtStart?: string;
    gmtEnd?: string;
}

export interface AssignerInfo {
    empId: string;
    oprId: string;
    name: string;
    avatar: string;
}

export interface MetaData {
    isVideo: boolean;
    interviewState: InterviewStatus;
    stageName: string;
    stageCode: string;
    stageId: string;
    stateName: string;
    stateCode: string;
    stateId: string;
}

export interface InterviewerItem {
    title: string;
    jobId: string;
    jobName: string;
    candidateId: string;
    gmtStart: string;
    gmtEnd: string;
    assigner: AssignerInfo;
    metadata: MetaData;
}

export interface StatisticItem {
    resumeScreeningCnt: number; // 简历筛选数量
    resumeScreeningOkCnt: number; // 简历筛选通过数量
    interviewSessionCnt: number; // 面试场次
    offerCnt: number; // offer人数
    awaitJoinEmployeeCnt: number; // 待入职人数
    interviewPassRate: number; // 面试通过率
}

export interface AllInterview {
    statistic: StatisticItem;
    interviews: InterviewerItem[];
}

export interface InterviewerStore {
    id: string;
    getInterviewerList: (data: GetInterviewerListRequest) => Promise<AllInterview>;
}

const useInterviewerStore = create<InterviewerStore>((set) => ({
    id: "", // id 的初始状态
    getInterviewerList: async (data: GetInterviewerListRequest) => {
        try {
            const response = await http.post("interviewer/fetch/mine", data);
            const result = (await response.json()) as RespParams<AllInterview>;
            return result.data;
        } catch (error) {
            console.error("获取面试列表时发生错误:", error);
            throw error;
        }
    },
}));

export default useInterviewerStore;
