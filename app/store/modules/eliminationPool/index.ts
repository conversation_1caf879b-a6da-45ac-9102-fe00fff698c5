import { create } from "zustand";
import http from "@/app/request";
import { RespParams } from "@/app/typing";
import { CandidateListResp } from "../candidate";
import { FlowItemParams } from "../dashboard/jobFlow";

// 获取淘汰池列表请求体
export interface GetEliminationPoolListRequest {
    pageNum: number; // 页码
    pageSize: number; // 每页数量
}

// 淘汰池列表响应体
export interface GetEliminationPoolListResponse {
    records: CandidateListResp[];
    pageNum: number;
    pageSize: number;
    total: number;
}

// 部门树节点类型
export interface DeptTreeNode {
    dept_code: string;
    dept_name: string;
    parent_dept_code: string;
    children?: DeptTreeNode[];
}

// 职位响应体 - 继承自FlowItemParams以保持类型一致性
export interface JobResp extends FlowItemParams {
    formatStr?: string;
    allocationMethod: number;
    jobSpecs?: FlowItemParams[];
}

/**
 * 淘汰池相关的状态管理
 */
export interface EliminationPoolStore {
    // 状态
    candidateList: CandidateListResp[];
    loading: boolean;

    // 部门相关状态
    defaultTreeData: DeptTreeNode[];
    defaultFlatTreeData: DeptTreeNode[];
    currentDept: DeptTreeNode | undefined;

    // 职位相关状态
    currentJob: JobResp | undefined;

    // 方法
    /**
     * 获取淘汰池列表
     * @param data 获取淘汰池列表所需的参数
     * @returns 获取淘汰池列表成功后的数据
     */
    fetchCandidateList: (data: GetEliminationPoolListRequest) => Promise<GetEliminationPoolListResponse | null>;

    // 部门相关方法
    setDefaultTreeData: (data: DeptTreeNode[]) => void;
    setDefaultFlatTreeData: (data: DeptTreeNode[]) => void;
    setCurrentDept: (dept: DeptTreeNode | undefined) => void;

    // 职位相关方法
    setCurrentJob: (job: JobResp | undefined) => void;
}

const useEliminationPoolStore = create<EliminationPoolStore>((set, get) => ({
    // 初始状态
    candidateList: [],
    loading: false,

    // 部门相关初始状态
    defaultTreeData: [],
    defaultFlatTreeData: [],
    currentDept: undefined,

    // 职位相关初始状态
    currentJob: undefined,

    // 获取淘汰池列表
    fetchCandidateList: async (data: GetEliminationPoolListRequest) => {
        set({ loading: true });
        try {
            const response = await http.post("hrWorkbench/common/list/rejected", data);
            const result = (await response.json()) as RespParams<GetEliminationPoolListResponse>;

            if (result && result.data) {
                set({
                    candidateList: result.data.records,
                    loading: false,
                });
                return result.data;
            } else {
                set({ loading: false });
                console.error("获取淘汰池列表失败:", result);
                return null;
            }
        } catch (error) {
            set({ loading: false });
            console.error("获取淘汰池列表时发生错误:", error);
            return null;
        }
    },

    // 部门相关方法
    setDefaultTreeData: (data: DeptTreeNode[]) => set({ defaultTreeData: data }),
    setDefaultFlatTreeData: (data: DeptTreeNode[]) => set({ defaultFlatTreeData: data }),
    setCurrentDept: (dept: DeptTreeNode | undefined) => set({ currentDept: dept }),

    // 职位相关方法
    setCurrentJob: (job: JobResp | undefined) => set({ currentJob: job }),
}));

export { useEliminationPoolStore };
export default useEliminationPoolStore;
