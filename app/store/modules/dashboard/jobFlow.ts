import { create } from "zustand";
import { UserResp } from "../../userInfo";
import { FormInstance } from "antd";
import { RefObject } from "react";
import { RichTextEditorRef } from "@/app/components/RichTextEditor";
import { ActionType } from "@ant-design/pro-table/es/typing";
import { PaginationInfo, ReqPaginationParams } from "@/app/typing";

export interface MyFlowReq extends ReqPaginationParams {
    orgId?: string;
    position?: string;
    status?: number;
    outPosition?: string;
}

export interface CreateUser {
    empId: string;
    empName: string;
    avatar: string;
    email: string;
}

export interface MyFlowResp extends JobFlowReq {
    status: number;
    assignment: number;
    createUserId: string;
    specIds?: string;
    children?: MyFlowResp[];
    jobSpecs?: JobFlowReq[];
    createUser: CreateUser;
    stageNames?: string[];
    flowInstanceId: string;
}

export interface PublishMyFlowReq extends JobFlowReq {
    allocationMethod: number;
    proportionValues?: { [key: string]: number };
    specIds: string;
}

export interface JobFlowAggregationResp {
    orgId: string;
    orgName: string;
    hiringPersonNum: number;
    offerTarget: number;
    jobLocation: string;
    educational: string;
    topTalents: number;
    excellentTalents: number;
    regularTalents: number;
}

export interface FlowTableExposeHandle {
    tableRef: ActionType | null;
    selectedData: MyFlowResp[];
}

export interface FlowItemParams {
    id?: number;
    jobId: string;
    jobNameInnerPath: string[] | string; // 岗位名称-对内id
    jobNameInner: string; // 岗位名称-对内
    jobNameOuter: string; // 岗位名称-对外
    orgId: string[] | string; // 组织id
    orgName: string; // 组织名称
    hiringBatch: string | null; // 招聘批次
    hiringPersonNum: number | null; // 目标人数
    offerTarget: number | null; // offer目标
    jobType: string | null; // 职位类别
    topTalents: number | null; // 拔尖人才
    excellentTalents: number | null; // 优秀人才
    regularTalents: number | null; // 普通人才
    jobLocation: string[] | string; // 工作地点
    educational: string[] | string; // 学历要求
    jobJD: string; // 职位jd
    jobJDByAI?: string; // AI生成的职位jd
    language?: string; // 语言要求
    reason?: string; // 招聘原因
    jobTags?: TagsItem[]; // 岗位标签
    jobList?: Job[];
    assignment?: number; // 分配方式
}

export interface Department {
    id: string;
    parent_dept_code: string;
    dept_code: string;
    dept_name: string;
    dept_level: number;
    supv_emp_id: string;
    supv_opr_id: string;
    supv_name: string;
    supv_pinyin: string;
    leader_emp_id: string;
    leader_opr_id: string;
    dept_code_path: string;
    dept_name_path: string;
    dept_type: string;
    isDisabled?: boolean;
}

export interface Job {
    code: string;
    desc: string;
    children: Job[];
}

export interface FlowStep {
    title: string;
    description?: string;
    status?: "wait" | "process" | "finish" | "error";
}

export interface FlowRecord {
    key: string;
    time: string;
    node: string;
    operator: string;
    action: string;
    remark: string;
    result: string;
}

export interface FlowItemResp extends FlowItemParams {
    tagTree: TagsResp[];
}

export interface JobFlowReq extends FlowItemParams {
    tags: number[];
    editMode?: boolean;
    allocationMethod?: number;
    jobSpecs?: FlowItemParams[];
    flowInstanceId: string;
    createUser?: CreateUser;
    createTime?: string;
}

export interface JobFlowState {
    flowItems: FlowItemParams[];
    departmentList: Department[];
    jobList: Job[];
    educationList: string[];
    setFlowItems: (flowItems: FlowItemParams[]) => void;
    setDepartmentList: (departmentList: Department[]) => void;
    setJobList: (jobList: Job[]) => void;
    setEducationList: (educationList: string[]) => void;
}
export interface FlowHistoryRecord {
    id: string;
    flowType: string;
    flowDefinition?: {
        name: string;
        [key: string]: unknown;
    };
    currentNodeInstance?: {
        nodeDefinition?: {
            name: string;
            [key: string]: unknown;
        };
        approver?: {
            emp_name: string;
            [key: string]: unknown;
        };
        [key: string]: unknown;
    };
    startedAt: string;
    status: string;
    statusStr: string;
    initiator?: string;
    currentNodeName?: string;
    currentNodeApprover?: string;
    ref_id: string;
    [key: string]: unknown;
}

export interface TodoItemReq extends PaginationInfo {
    status: string;
    keyword?: string;
}

export interface TodoItem {
    id: number;
    content: string;
    start_time: string;
    title: string;
    type: number;
    deadline: string;
    ref_id: string;
    senderUser: UserResp;
    targetUser: UserResp & {
        empId: string;
        empName: string;
    };
}

// 定义暴露给父组件的方法接口
export interface AddFlowExposeHandle {
    formRefs: { [key: string]: AddFlowItemExposeHandle };
    onOk: () => Promise<boolean>;
    onCancel: () => boolean;
    setActiveKey: (key: string[]) => void;
}

export interface AddFlowItemExposeHandle {
    getForm: () => FormInstance;
    richTextRef: RefObject<RichTextEditorRef>;
}

export interface TagsItem {
    label: string;
    children?: TagsItem[];
    value?: TagsResp[];
}

export interface TagsResp {
    id: number;
    name: string;
    layer: string;
    label: string;
}

export interface ProcessFlowReq {
    flowInstanceId: string;
    status: string;
    comment: string;
    showDelegate?: boolean;
    targetApprover?: string;
}

// User/Employee interface
interface Employee {
    permGroups: any[];
    perms: any[];
    avatar: string;
    emp_id: string;
    emp_name: string;
    opr_id: string;
    supv_emp_id: string;
    supv_opr_id: string;
    gender: string;
    email: string;
    dept_code: string;
    position_code: string;
    is_deleted: boolean;
    gmt_join_comp: string;
    gmt_create: string;
    gmt_modified: string;
}

// Flow definition interface
interface FlowDefinition {
    id: number;
    name: string;
    description: string;
    firstNode: number;
    enabled: boolean;
}

// Node type enum
type NodeType = "SINGLE_SIGN" | "OR_SIGN" | "SERVICE";

// Assignment type enum
type AssignmentType = "INITIATOR" | "ORG_BASED" | "ROLE_BASED" | "FIXED_USER" | "NONE";

// Flow node definition interface
export interface FlowNodeDefinition {
    id: number;
    name: string;
    description: string;
    flowId: number;
    nodeType: NodeType;
    requiredCount: number;
    assignmentType: AssignmentType;
    assignmentTag?: string;
    nextNodeId: number;
    fallbackNodeId: number;
}

// Node instance interface
export interface NodeInstance {
    id: number;
    flowInstanceId: number;
    nodeId: number;
    nodeDefinition: FlowNodeDefinition;
    requiredCount: number;
    approverId: string;
    approver: Employee;
    startedAt: string;
    status: number;
    statusStr: string;
    parentNodeInstanceId?: string;
}

// Main flow instance interface
export interface FlowDetailResp {
    id: number;
    flowId: number;
    flowDefinition: FlowDefinition;
    flowNodeDefinitions: FlowNodeDefinition[];
    currentNodeInstanceId: number;
    currentNodeInstance: NodeInstance;
    currentHandlerId: string;
    status: number;
    statusStr: string;
    initiatorId: string;
    initiator: Employee;
    initiatorOrgId: string;
    relatedForm: string;
    startedAt: string;
    nodeInstanceList: NodeInstance[];
}

export interface ApprovalModalRef {
    open: (params: ProcessFlowReq) => Promise<boolean>;
}

export interface FlowDetailExposeHandle {
    currentFlow: NodeInstance | null;
    currentJobForm: JobFormReq | null;
    isEditMode: boolean;
    refresh: () => void;
}

export interface JobFormReq {
    flowId: number;
    relatedForm: string;
}

export interface DeprecateJobFlowReq {
    type: number; // 1-小需求 2-大需求
    ids: number[];
}

const useJobFlowStore = create<JobFlowState>((set, get) => {
    return {
        flowItems: [],
        departmentList: [],
        jobList: [],
        educationList: [],
        setFlowItems: (flowItems: FlowItemParams[]) => set(() => ({ flowItems })),
        setDepartmentList: (departmentList: Department[]) => set(() => ({ departmentList })),
        setJobList: (jobList: Job[]) => set(() => ({ jobList })),
        setEducationList: (educationList: string[]) => set(() => ({ educationList })),
    };
});

export default useJobFlowStore;
