@mixin container {
    border: var(--border-in-light);
    //border-radius: 20px;
    box-shadow: var(--shadow);
    color: var(--black);
    background-color: var(--white);
    min-width: 600px;
    min-height: 370px;
    // max-width: 1200px;
    // 一开始是可以切换小屏和大屏，这块设计切换，现在默认大屏
    // 默认全屏相关设计，会出现刷新时先显示小屏，再显示大屏,涉及注释的都是更改项，后面看是否有需求改回去,现在定死了首先是切换不能用，后续注意其他板块
    max-width: 100vw;
    max-height: 100vh;

    display: flex;
    overflow: hidden;
    box-sizing: border-box;

    // width: var(--window-width);
    // height: var(--window-height);

    width: 100vw;
    height: 100vh;
}

.container {
    @include container();
}

@media only screen and (min-width: 600px) {
    .tight-container {
        --window-width: 100vw;
        --window-height: var(--full-height);
        --window-content-width: calc(100% - var(--sidebar-width));

        @include container();

        max-width: 100vw;
        max-height: var(--full-height);

        border-radius: 0;
        border: 0;
    }
}

.window-content {
    width: var(--window-content-width);
    // flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    // user-select: none;
    // -webkit-user-select: none;
    // -moz-user-select: none;
    // -ms-user-select: none;
}

.sidebar {
    width: var(--sidebar-width);
    height: var(--full-height);
    background-color: var(--white);
    border-right: var(--border-in-light);
    box-sizing: border-box;
    position: relative;
}

.mobile {
    display: none;
}

@media only screen and (max-width: 600px) {
    .container {
        min-height: unset;
        min-width: unset;
        max-height: unset;
        min-width: unset;
        border: 0;
        border-radius: 0;
    }

    .sidebar {
        position: absolute;
        left: -100%;
        z-index: 1000;
        height: var(--full-height);
        transition: all ease 0.3s;
        box-shadow: none;
    }

    .sidebar-show {
        left: 0;
    }

    .mobile {
        display: block;
    }
}

.chat-item {
    padding: 8px 14px;
    border-radius: 10px;
    cursor: pointer;
    user-select: none;
    border: 2px solid transparent;
    position: relative;
    content-visibility: auto;
}

.chat-item:hover {
    background-color: var(--hover-color);
}

.chat-item-selected {
    //   border-color: var(--primary);
}

.chat-item-content {
    font-family: var(--font-family);
    font-weight: 400;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    line-height: 22px;
    font-weight: bolder;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.chat-item-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    animation: slide-in ease 0.3s;
}

.chat-item:hover > .chat-item-delete {
    opacity: 0.5;
    transform: translateX(-4px);
}

.chat-item:hover > .chat-item-delete:hover {
    opacity: 1;
}

.chat-item-info {
    display: flex;
    justify-content: space-between;
    color: rgb(166, 166, 166);
    font-size: 12px;
    margin-top: 8px;
    animation: slide-in ease 0.3s;
}

.chat-item-count,
.chat-item-date {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.chat-item-btn {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.chat-item:hover .chat-item-btn {
    opacity: 1;
}

.narrow-sidebar {
    .sidebar-title,
    .sidebar-sub-title {
        display: none;
    }
    .sidebar-logo {
        position: relative;
        display: flex;
        justify-content: center;
    }

    .sidebar-header-bar {
        flex-direction: column;

        .sidebar-bar-button {
            &:not(:last-child) {
                margin-right: 0;
                margin-bottom: 10px;
            }
        }
    }

    .chat-item {
        padding: 0;
        min-height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: all ease 0.3s;
        overflow: hidden;

        &:hover {
            .chat-item-narrow {
                transform: scale(0.7) translateX(-50%);
            }
        }
    }

    .chat-item-narrow {
        line-height: 0;
        font-weight: lighter;
        color: var(--black);
        transform: translateX(0);
        transition: all ease 0.3s;
        padding: 4px;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .chat-item-avatar {
            display: flex;
            justify-content: center;
            opacity: 0.2;
            position: absolute;
            transform: scale(4);
        }

        .chat-item-narrow-count {
            font-size: 24px;
            font-weight: bolder;
            text-align: center;
            color: var(--primary);
            opacity: 0.6;
        }
    }

    .sidebar-tail {
        flex-direction: column-reverse;
        align-items: center;

        .sidebar-actions {
            flex-direction: column-reverse;
            align-items: center;

            .sidebar-action {
                margin-right: 0;
                margin-top: 15px;
            }
        }
    }
}

.sidebar-tail {
    display: flex;
    justify-content: space-between;
    padding-top: 20px;
}

.sidebar-actions {
    display: inline-flex;
}

.sidebar-action:not(:last-child) {
    margin-right: 15px;
}

.loading-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
}
