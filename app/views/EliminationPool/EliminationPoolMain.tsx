import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Button, Checkbox, Empty, Flex, Pagination, Skeleton } from "antd";
import { useNavigate } from "react-router-dom";
import styles from "./index.module.scss";
import TalentCard from "../TalentPoolNew/MainWrapper/TalentCard";
import FilterBar, { FilterOption, SortOption } from "@/app/components/FilterBar";
import BottomMultiple from "@/app/components/BottomMultiple";
import { FilterState, useFilterState } from "../../hooks/useFilterState";
import { Path } from "@/app/constant";
import { CandidateListResp } from "@/app/store/modules/candidate";
import useEliminationPoolStore from "@/app/store/modules/eliminationPool";
import useCandidateDetailStore from "@/app/store/modules/candidate/detail";

interface EliminationPoolMainProps {
    mode?: string;
}

const EliminationPoolMain: React.FC<EliminationPoolMainProps> = ({ mode }) => {
    const navigate = useNavigate();
    const { candidateList, loading, fetchCandidateList } = useEliminationPoolStore();
    const clear = useCandidateDetailStore((state) => state.clear);

    // 状态管理
    const [selectedCandidate, setSelectedCandidate] = useState<CandidateListResp[]>([]);
    const [contentHeight, setContentHeight] = useState(0);

    // 分页状态
    const [pagination, setPagination] = useState({
        pageNum: 0,
        pageSize: 20,
        total: 0,
    });

    const mainContentRef = useRef<HTMLDivElement>(null);

    // 使用筛选状态Hook
    const { filters, updateFilter, updateFilters, resetFilters, hasFilters } = useFilterState();

    // 筛选选项配置
    const [filterOptions, setFilterOptions] = useState<FilterOption[]>([
        {
            key: "education",
            label: "学历",
            type: "select",
            enabled: true,
            options: [
                { value: "bachelor", label: "本科" },
                { value: "master", label: "硕士" },
                { value: "doctor", label: "博士" },
            ],
        },
        {
            key: "workYears",
            label: "工作年限",
            type: "select",
            enabled: true,
            options: [
                { value: "1-3", label: "1-3年" },
                { value: "3-5", label: "3-5年" },
                { value: "5-10", label: "5-10年" },
                { value: "10+", label: "10年以上" },
            ],
        },
        {
            key: "skills",
            label: "技能标签",
            type: "multiple",
            enabled: true,
            options: [
                { value: "react", label: "React" },
                { value: "vue", label: "Vue.js" },
                { value: "angular", label: "Angular" },
                { value: "typescript", label: "TypeScript" },
                { value: "nodejs", label: "Node.js" },
            ],
        },
        {
            key: "graduationTime",
            label: "毕业时间",
            type: "date",
            enabled: true,
        },
    ]);

    // 排序选项配置
    const [sortOptions, setSortOptions] = useState<SortOption[]>([
        { key: "updateTime", label: "更新时间", direction: "desc" },
        { key: "workYears", label: "工作年限", direction: null },
        { key: "education", label: "学历", direction: null },
    ]);

    // 计算派生状态
    const checkAll = useMemo(() => {
        if (candidateList.length === 0) return false;
        return candidateList.every((candidate) =>
            selectedCandidate.find((selected) => selected.profile.applicantId === candidate.profile.applicantId)
        );
    }, [selectedCandidate, candidateList]);

    const indeterminate = useMemo(() => {
        const filterList = candidateList.filter((candidate) =>
            selectedCandidate.find((selected) => selected.profile.applicantId === candidate.profile.applicantId)
        );

        if (filterList.length === 0) return false;
        return filterList.length < candidateList.length;
    }, [selectedCandidate, candidateList]);

    // 事件处理函数
    const handleSearch = useCallback(
        (keyword: string) => {
            updateFilter("keyword", keyword);
            console.log("搜索关键词:", keyword);
            // TODO: 实现搜索逻辑
        },
        [updateFilter]
    );

    const handleFilterChange = useCallback(
        (newFilters: Partial<FilterState>) => {
            updateFilters(newFilters);
            console.log("筛选条件变化:", newFilters);
            // TODO: 实现筛选逻辑
        },
        [updateFilters]
    );

    const jobName = (talent: CandidateListResp) => {
        return talent.jobSpec?.jobNameInner.split("/")?.[talent.jobSpec?.jobNameInner.split("/")?.length - 1];
    };

    const handleFilterOptionsChange = (newFilterOptions: FilterOption[]) => {
        setFilterOptions(newFilterOptions);
        console.log("筛选选项变化:", newFilterOptions);
    };

    const handleSortChange = (newSortOptions: SortOption[]) => {
        setSortOptions(newSortOptions);
        console.log("排序变化:", newSortOptions);
        // TODO: 实现排序逻辑
    };

    const handlePageChange = async (page: number, pageSize?: number) => {
        const newPagination = {
            pageNum: page - 1,
            pageSize: pageSize || pagination.pageSize,
        };
        setPagination((prev) => ({
            ...prev,
            ...newPagination,
        }));

        const result = await fetchCandidateList(newPagination);
        if (result) {
            setPagination((prev) => ({
                ...prev,
                total: result.total,
            }));
        }
    };

    const handlePageSizeChange = async (current: number, size: number) => {
        const newPagination = {
            pageNum: 0,
            pageSize: size,
        };
        setPagination((prev) => ({
            ...prev,
            ...newPagination,
        }));

        const result = await fetchCandidateList(newPagination);
        if (result) {
            setPagination((prev) => ({
                ...prev,
                total: result.total,
            }));
        }
    };

    const handleCardClick = (talent: CandidateListResp) => {
        clear();
        // 导航到简历详情页
        navigate(Path.ResumeDetail, {
            state: {
                jobId: talent.application.jobId,
                jobName: jobName(talent),
                applicantId: talent.application.applicantId,
                mode: mode,
                // 是否是手动分配，适用于分配简历页面
                isManualDistribution: false,
            },
        });
    };

    const handleCardSelect = (talent: CandidateListResp) => {
        const isSelected = selectedCandidate.find(
            (selected) => selected.profile.applicantId === talent.profile.applicantId
        );

        if (isSelected) {
            // 取消选中
            setSelectedCandidate((prev) =>
                prev.filter((item) => item.profile.applicantId !== talent.profile.applicantId)
            );
        } else {
            // 选中
            setSelectedCandidate((prev) => [...prev, talent]);
        }
    };

    const handleSelectAll = () => {
        if (checkAll) {
            // 当前状态为全选，点击后取消当前页选中数据
            setSelectedCandidate(
                selectedCandidate.filter(
                    (item) => !candidateList.find((item2) => item2.profile.applicantId === item.profile.applicantId)
                )
            );
        } else {
            // 点击后选中当前页所有数据，如果当前页数据与选中数据有重复，则去重
            setSelectedCandidate(Array.from(new Set([...selectedCandidate, ...candidateList])));
        }
    };

    const handleBatchEliminate = () => {
        console.log("批量淘汰:", selectedCandidate);
        // TODO: 实现批量淘汰逻辑
        setSelectedCandidate([]);
    };

    const handleBatchRestore = () => {
        console.log("批量恢复:", selectedCandidate);
        // TODO: 实现批量恢复逻辑
        setSelectedCandidate([]);
    };

    // 初始化数据
    useEffect(() => {
        const loadData = async () => {
            const result = await fetchCandidateList({
                pageNum: pagination.pageNum,
                pageSize: pagination.pageSize,
            });
            if (result) {
                setPagination((prev) => ({
                    ...prev,
                    total: result.total,
                }));
            }
        };
        loadData();
    }, []);

    // 动态调整内容高度
    useEffect(() => {
        const updateHeight = () => {
            if (mainContentRef.current) {
                const rect = mainContentRef.current.getBoundingClientRect();
                const windowHeight = window.innerHeight;
                const availableHeight = windowHeight - rect.top - 120; // 预留底部空间
                setContentHeight(Math.max(availableHeight, 400));
            }
        };

        updateHeight();
        window.addEventListener("resize", updateHeight);

        return () => {
            window.removeEventListener("resize", updateHeight);
        };
    }, []);

    return (
        <div className={styles["elimination-pool-main"]}>
            {/* 筛选栏 */}
            <FilterBar
                filters={filters}
                onFilterChange={handleFilterChange}
                onSearch={handleSearch}
                onResetFilters={resetFilters}
                hasFilters={hasFilters}
                sortOptions={sortOptions}
                onSortChange={handleSortChange}
                filterOptions={filterOptions}
                onFilterOptionsChange={handleFilterOptionsChange}
                searchBarWidth={360}
                showSaveButton={true}
                showViewToggle={true}
                showDisposition={true}
            />

            {/* 候选人列表 */}
            <div
                ref={mainContentRef}
                className={styles["candidate-list-container"]}
                style={{ height: contentHeight, overflowY: "auto" }}
            >
                {loading ? (
                    // 加载状态
                    <div className={styles["loading-container"]}>
                        {Array.from({ length: 6 }).map((_, index) => (
                            <Skeleton key={index} active paragraph={{ rows: 4 }} />
                        ))}
                    </div>
                ) : candidateList.length > 0 ? (
                    // 候选人卡片列表
                    candidateList.map((talent) => {
                        const isSelected = selectedCandidate.find(
                            (selected) => selected.profile.applicantId === talent.profile.applicantId
                        );

                        return (
                            <TalentCard
                                key={talent.profile.applicantId}
                                talent={talent}
                                jobName={jobName(talent)}
                                onClick={handleCardClick}
                                // handleCardSelect={handleCardSelect}
                                // isSelect={!!isSelected}
                                disabledSelect={true}
                                // showActions={true}
                                isEliminated={true}
                                // renderOperationBtn={
                                //     <Flex gap={8}>
                                //         <Button
                                //             size="small"
                                //             danger
                                //             onClick={(e) => {
                                //                 e.stopPropagation();
                                //                 console.log("淘汰候选人:", talent.profile.applicantId);
                                //                 // TODO: 实现淘汰逻辑
                                //             }}
                                //         >
                                //             淘汰
                                //         </Button>
                                //         <Button
                                //             size="small"
                                //             type="primary"
                                //             onClick={(e) => {
                                //                 e.stopPropagation();
                                //                 console.log("恢复候选人:", talent.profile.applicantId);
                                //                 // TODO: 实现恢复逻辑
                                //             }}
                                //         >
                                //             恢复
                                //         </Button>
                                //     </Flex>
                                // }
                            />
                        );
                    })
                ) : (
                    // 空状态
                    <Flex style={{ width: "100%", height: "100%" }} justify="center" align="center">
                        <Empty description="暂无淘汰池数据" />
                    </Flex>
                )}
            </div>

            {/* 底部区域 */}
            <Flex justify="flex-end" align="center" className={styles["bottom-area"]}>
                {/* 全选复选框 */}
                {/* <Checkbox indeterminate={indeterminate} onChange={handleSelectAll} checked={checkAll}>
                    全选
                </Checkbox> */}

                {/* 分页控件 */}
                <Pagination
                    size="small"
                    current={pagination.pageNum + 1}
                    pageSize={pagination.pageSize}
                    total={pagination.total}
                    showSizeChanger
                    showTotal={(total) => `共 ${total} 条`}
                    onChange={handlePageChange}
                    onShowSizeChange={handlePageSizeChange}
                />
            </Flex>

            {/* 底部多选操作栏 */}
            <BottomMultiple selectedNum={selectedCandidate.length} handleClose={() => setSelectedCandidate([])}>
                <Flex gap={12}>
                    <span style={{ cursor: "pointer" }} onClick={handleBatchEliminate}>
                        批量淘汰
                    </span>
                    <span style={{ cursor: "pointer" }} onClick={handleBatchRestore}>
                        批量恢复
                    </span>
                </Flex>
            </BottomMultiple>
        </div>
    );
};

export default EliminationPoolMain;
