import { Button, Flex, Layout, Popover, Select, Input, Cascader, Spin } from "antd";
import styles from "./index.module.scss";
import { useEffect, useMemo, useState, useCallback, useRef } from "react";
import { treeToArray } from "@/app/utils";
import EliminationPoolMain from "./EliminationPoolMain";
import ResizableContainer from "@/app/components/ResizableContainer";
import useEliminationPoolStore from "@/app/store/modules/eliminationPool";
import { JobResp } from "@/app/store/modules/candidate";
import { TextView } from "@/app/components/TextView";
import { IconButton } from "@/app/components/Button";
import FilterIcon from "@/app/icons/talentTool/filter-icon.svg";
import SearchIcon from "@/app/icons/SearchIcon.svg";
import { getDepartmentListApi } from "@/app/request/modules/common";
import { recruitmentType } from "@/app/constant";
import { useFilterState } from "@/app/hooks/useFilterState";
import { Resp<PERSON>arams } from "@/app/typing";
import { getSubJobListApi } from "@/app/request/modules/candidate";
import { Department } from "@/app/store/modules/dashboard/jobFlow";
import React from "react";

export interface EliminationPoolProps {
    mode?: "default" | "Interviewer" | "HR";
}

const EliminationPool = ({ mode = "default" }: EliminationPoolProps) => {
    const {
        defaultTreeData,
        defaultFlatTreeData,
        currentDept,
        setDefaultTreeData,
        setDefaultFlatTreeData,
        setCurrentDept,
        setCurrentJob,
        currentJob,
    } = useEliminationPoolStore((state) => ({
        defaultTreeData: state.defaultTreeData,
        defaultFlatTreeData: state.defaultFlatTreeData,
        currentDept: state.currentDept,
        currentJob: state.currentJob,
        setDefaultTreeData: state.setDefaultTreeData,
        setDefaultFlatTreeData: state.setDefaultFlatTreeData,
        setCurrentDept: state.setCurrentDept,
        setCurrentJob: state.setCurrentJob,
    }));

    const [showFilter, setShowFilter] = useState(false);
    const [jobList, setJobList] = useState<JobResp[]>([]);
    const [filteredJobList, setFilteredJobList] = useState<JobResp[]>([]);
    const [searchValue, setSearchValue] = useState("");
    const [selectedRecruitmentType, setSelectedRecruitmentType] = useState(1); // 默认校园招聘
    const [selectedDepartment, setSelectedDepartment] = useState<string | undefined>(undefined);
    const [isFilterOperation, setIsFilterOperation] = useState(false);
    const [jobListLoading, setJobListLoading] = useState(false);

    // 使用 ref 存储临时的部门选择值，避免状态更新导致 Popover 重新渲染
    const tempSelectedDepartment = useRef<string | undefined>(undefined);
    const itemRefs = useRef<{ [key: number]: HTMLDivElement | null }>({});

    // 临时选中的部门状态，用于 Cascader 显示
    const [tempSelectedDeptState, setTempSelectedDeptState] = useState<string | undefined>(undefined);

    // 独立的部门选择器组件
    const DepartmentCascader = React.memo<{
        defaultTreeData: any[];
        findDepartmentPath: (code: string) => string[] | null;
        handleCascaderChange: (value: string[], selectedOptions: any[]) => void;
        selectedValue?: string[] | string;
    }>((props) => {
        // 处理 selectedValue，支持字符串或数组
        const getValue = () => {
            if (!props.selectedValue) return undefined;

            // 如果是数组，直接返回
            if (Array.isArray(props.selectedValue)) {
                return props.selectedValue;
            }

            // 如果是字符串，通过 findDepartmentPath 获取完整路径
            return props.findDepartmentPath(props.selectedValue) || undefined;
        };

        return (
            <Cascader
                style={{ width: "100%" }}
                placeholder="请选择部门"
                value={getValue()}
                onChange={props.handleCascaderChange}
                options={props.defaultTreeData}
                fieldNames={{
                    label: "dept_name",
                    value: "dept_code",
                    children: "children" as keyof Department,
                }}
                showSearch
                changeOnSelect
                allowClear
                expandTrigger="hover"
            />
        );
    });

    DepartmentCascader.displayName = "DepartmentCascader";

    // 使用增强的筛选状态Hook
    const { filters, pagination, resetFilters } = useFilterState();

    const getTreeData = async () => {
        const res: any = await (await getDepartmentListApi()).json();
        if (res.code === 200) {
            const departmentList = [res.data].map((item) => {
                if (item.dept_code === "100000") {
                    item.parent_dept_code = "-1";
                }
                return item;
            });

            setDefaultTreeData(departmentList);
            const flatTreeData = treeToArray(departmentList);
            setDefaultFlatTreeData(flatTreeData);
            setCurrentDept(currentDept ?? flatTreeData?.[0]);
        }
    };

    const getJobList = async () => {
        setJobList([]);
        setCurrentJob(undefined);
        setJobListLoading(true);

        try {
            const res: RespParams<JobResp[]> = await (await getSubJobListApi(currentDept?.dept_code ?? "")).json();
            if (res.code === 200) {
                setJobList(res?.data ?? []);
                const current = res?.data?.find((item) => item.id === currentJob?.id);
                // 只有当之前有选中的职位且该职位仍存在时才保持选中状态
                if (current) {
                    setCurrentJob(current);
                } else {
                    // 不自动选择第一个职位，让用户手动选择
                    setCurrentJob(undefined);
                }
            }
        } catch (error) {
            console.error("获取职位列表失败:", error);
        } finally {
            setJobListLoading(false);
        }
    };

    // 筛选职位列表
    useEffect(() => {
        let filtered = jobList;

        // 根据搜索关键词筛选
        if (searchValue) {
            filtered = filtered.filter((item) => {
                const formatStr = item.formatStr || "";
                const orgName = item.orgName || "";
                const jobNameInner = item.jobNameInner || "";
                const jobNameOuter = item.jobNameOuter || "";

                return (
                    formatStr.toLowerCase().includes(searchValue.toLowerCase()) ||
                    orgName.toLowerCase().includes(searchValue.toLowerCase()) ||
                    jobNameInner.toLowerCase().includes(searchValue.toLowerCase()) ||
                    jobNameOuter.toLowerCase().includes(searchValue.toLowerCase())
                );
            });
        }

        setFilteredJobList(filtered);
    }, [jobList, searchValue]);

    // 查找部门路径
    const findDepartmentPath = useCallback(
        (code: string): string[] | null => {
            if (!defaultFlatTreeData || defaultFlatTreeData.length === 0) return null;

            const path: string[] = [];
            let currentCode = code;

            while (currentCode && currentCode !== "-1") {
                path.unshift(currentCode);
                const parentDept = defaultFlatTreeData.find((dept) => dept.dept_code === currentCode);
                if (!parentDept) break;
                currentCode = parentDept.parent_dept_code;
            }

            return path.length > 0 ? path : null;
        },
        [defaultFlatTreeData]
    );

    // 初始化获取部门树数据
    useEffect(() => {
        getTreeData();
    }, []);

    // 同步当前部门到筛选状态（仅在非筛选操作时同步）
    useEffect(() => {
        if (currentDept && !isFilterOperation) {
            setSelectedDepartment(currentDept.dept_code);
        }
    }, [currentDept, isFilterOperation]);

    // 当部门变化时，获取职位列表
    useEffect(() => {
        if (currentDept && mode === "HR") {
            getJobList();
        }
    }, [currentDept, mode]);

    // 处理内联 Cascader 变化
    const handleInlineCascaderChange = useCallback((value: string[], selectedOptions: any[]) => {
        const lastValue = value[value.length - 1];
        setTempSelectedDeptState(lastValue);
        tempSelectedDepartment.current = lastValue;
    }, []);

    // 处理确认按钮点击
    const handleConfirmClick = () => {
        setShowFilter(false);
        setIsFilterOperation(true);
        const finalDepartment = tempSelectedDepartment.current;
        setSelectedDepartment(finalDepartment);
        if (finalDepartment) {
            const selectedDept = defaultFlatTreeData.find((dept) => dept.dept_code === finalDepartment);
            if (selectedDept) {
                setCurrentDept(selectedDept);
            }
        }
        setTimeout(() => setIsFilterOperation(false), 100);
    };

    // 处理取消按钮点击
    const handleCancelClick = useCallback(() => {
        // 恢复为之前的选择
        tempSelectedDepartment.current = selectedDepartment;
        setTempSelectedDeptState(selectedDepartment);
        setShowFilter(false);
    }, [selectedDepartment]);

    // 处理重置按钮点击
    const handleResetClick = useCallback(() => {
        tempSelectedDepartment.current = undefined;
        setTempSelectedDeptState(undefined);
        setSelectedRecruitmentType(1); // 重置为默认值
    }, []);

    // 筛选内容组件
    const FilterContent = useMemo(() => {
        return (
            <Flex vertical gap={16} style={{ padding: "8px 0" }}>
                <Flex vertical gap={16}>
                    <div>
                        <div style={{ marginBottom: 8, fontSize: 14, fontWeight: 500 }}>招聘场景</div>
                        <Select
                            style={{ width: "100%" }}
                            placeholder="请选择招聘场景"
                            value={selectedRecruitmentType}
                            onChange={setSelectedRecruitmentType}
                            options={recruitmentType}
                        />
                    </div>
                    <div>
                        <div style={{ marginBottom: 8, fontSize: 14, fontWeight: 500 }}>部门</div>
                        <DepartmentCascader
                            defaultTreeData={defaultTreeData}
                            findDepartmentPath={findDepartmentPath}
                            handleCascaderChange={handleInlineCascaderChange}
                            selectedValue={
                                tempSelectedDeptState
                                    ? (findDepartmentPath(tempSelectedDeptState) ?? undefined)
                                    : undefined
                            }
                        />
                    </div>
                </Flex>
                <Flex align="center" justify="space-between" gap={8}>
                    <Button onClick={handleResetClick}>重置</Button>
                    <Flex gap={8}>
                        <Button onClick={handleCancelClick}>取消</Button>
                        <Button type="primary" onClick={handleConfirmClick}>
                            确认
                        </Button>
                    </Flex>
                </Flex>
            </Flex>
        );
    }, [
        selectedRecruitmentType,
        defaultTreeData,
        tempSelectedDeptState,
        findDepartmentPath,
        selectedDepartment,
        defaultFlatTreeData,
    ]);

    const Filter = useMemo((): JSX.Element => {
        const handleIconClick = () => {
            // 打开 Popover 时，初始化临时选择值为当前状态值
            tempSelectedDepartment.current = selectedDepartment;
            setTempSelectedDeptState(selectedDepartment);
            setShowFilter(true);
        };

        const handleOpenChange = (open: boolean) => {
            setShowFilter(open);
        };

        const getContainer = () => document.body;

        return (
            <Popover
                classNames={{ root: `${styles["filter-popover"]}` }}
                title="高级筛选"
                placement="bottomLeft"
                trigger="click"
                open={showFilter}
                onOpenChange={handleOpenChange}
                content={FilterContent}
                getPopupContainer={getContainer}
            >
                <IconButton title="高级筛选" icon={<FilterIcon />} onClick={handleIconClick} />
            </Popover>
        );
    }, [showFilter, FilterContent, selectedDepartment]);

    useEffect(() => {
        // 如果没有当前职位，直接返回
        if (!currentJob?.id) return;

        // 定义检查和滚动函数
        const scrollToItem = () => {
            // 安全地检查和访问元素
            const element = itemRefs.current?.[currentJob?.id ?? -1];
            if (element) {
                // 执行滚动
                element.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                });
                return true; // 滚动成功
            }
            return false; // 没有找到元素
        };

        // 立即尝试滚动
        if (scrollToItem()) return;

        // 如果立即滚动失败，使用 requestAnimationFrame 在下一帧尝试
        const frameId = requestAnimationFrame(() => {
            if (scrollToItem()) return;

            // 仍然失败，设置一个短暂的延迟后再试一次
            const timeoutId = setTimeout(() => {
                scrollToItem();
            }, 100);

            // 清理 setTimeout
            return () => clearTimeout(timeoutId);
        });

        // 清理 requestAnimationFrame
        return () => cancelAnimationFrame(frameId);
    }, [currentJob]);

    return (
        <Layout className={styles["elimination-pool-wrapper"]}>
            <ResizableContainer resizable={{ right: true }} minWidth="16%" maxWidth="26%" initialWidth="16%">
                <Layout.Sider width="100%">
                    <div style={{ width: "100%" }}>
                        {mode === "HR" && (
                            <div style={{ width: "100%", marginBottom: 12 }}>
                                {/* 搜索和筛选区域 */}
                                <div style={{ marginBottom: 12 }}>
                                    <Flex gap={8} align="center">
                                        <Input
                                            placeholder="搜索职位..."
                                            prefix={<SearchIcon />}
                                            value={searchValue}
                                            onChange={(e) => setSearchValue(e.target.value)}
                                            allowClear
                                            style={{ flex: 1 }}
                                        />
                                        {Filter}
                                    </Flex>
                                </div>

                                {/* 职位列表 */}
                                <Spin spinning={jobListLoading} size="small">
                                    {(filteredJobList.length === 0 && jobList.length === 0 && !jobListLoading
                                        ? ([{ formatStr: "默认职位", id: -1 }] as JobResp[])
                                        : filteredJobList?.map((item: JobResp) => {
                                              let str = "";
                                              const list = item?.jobNameInner?.split("/");
                                              list.reverse();
                                              //   str = list?.[0] + "-" + item?.jobNameOuter;
                                              str = item?.jobNameOuter;
                                              item.formatStr = str;

                                              return item;
                                          })
                                    )?.map((item: JobResp) => (
                                        <div
                                            key={item.id}
                                            ref={(el) => {
                                                itemRefs.current[item?.id ?? new Date().getTime()] = el;
                                            }}
                                            onClick={() => setCurrentJob(item)}
                                            style={{
                                                padding: "8px 12px",
                                                cursor: "pointer",
                                                borderRadius: "6px",
                                                marginBottom: "4px",
                                                backgroundColor: currentJob?.id === item.id ? "#1890ff" : "transparent",
                                                color: currentJob?.id === item.id ? "white" : "inherit",
                                                border:
                                                    currentJob?.id === item.id
                                                        ? "1px solid #1890ff"
                                                        : "1px solid var(--border-color)",
                                                transition: "all 0.2s ease",
                                            }}
                                            onMouseEnter={(e) => {
                                                if (currentJob?.id !== item.id) {
                                                    e.currentTarget.style.backgroundColor = "var(--hover-color)";
                                                }
                                            }}
                                            onMouseLeave={(e) => {
                                                if (currentJob?.id !== item.id) {
                                                    e.currentTarget.style.backgroundColor = "transparent";
                                                }
                                            }}
                                        >
                                            <Flex vertical>
                                                <div>
                                                    <TextView text={item.formatStr ?? ""} placement="right" />
                                                </div>

                                                <div
                                                    style={{
                                                        marginTop: 4,
                                                        fontSize: 12,
                                                        color:
                                                            currentJob?.id === item.id
                                                                ? "rgba(255,255,255,0.9)"
                                                                : "var(--sub-text-color)",
                                                    }}
                                                >
                                                    <TextView text={item.orgName ?? ""} lines={2} placement="right" />
                                                </div>
                                            </Flex>
                                        </div>
                                    ))}
                                </Spin>
                            </div>
                        )}
                    </div>
                </Layout.Sider>
            </ResizableContainer>
            <Layout.Content>
                {/* <EliminationPoolMain mode={mode} onJobListRefresh={getJobList} /> */}
                <EliminationPoolMain mode={mode} />
            </Layout.Content>
        </Layout>
    );
};

export default EliminationPool;
