.sidebar-wrapper {
    width: var(--sidebar-width);
    height: 100vh;
    position: relative;
    flex-shrink: 0;
    transition: width 0.5s ease;
}

.sidebar {
    top: 0;
    width: var(--sidebar-width);
    height: 100%;
    box-sizing: border-box;
    padding: 12px;
    background-color: var(--sidebarSecond);
    display: flex;
    flex-direction: column;
    position: relative;
    transition: width 0.5s ease;

    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    &.super-narrow-sidebar {
        padding: 12px 8px;
        .sidebar-header-bar {
            align-items: center;
            .sidebar-bar-button {
                min-width: 36px;
                width: 36px;
                justify-content: center;
            }
        }
    }

    &.super-narrow-sidebar-hovered {
        position: absolute;
        top: 0;
        left: 0;
        width: 260px;
        height: 100vh;
        padding: 12px;
        overflow: visible;
        z-index: 1001;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        background-color: var(--sidebarSecond);

        .sidebar-header-bar {
            align-items: flex-start;
            .sidebar-bar-button {
                min-width: auto;
                width: 100%;
                justify-content: flex-start;
            }
        }
    }

    &.collapsed-sidebar {
        // width: 0;
        padding: 0;
        overflow: hidden;
        border: none;
    }

    .sidebar-header-bar {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 6px;
        padding-bottom: 14px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        .sidebar-bar-button {
            flex-grow: 1;
            width: 100%;
            height: 36px;
            border-radius: 4px;
            background-color: transparent !important;
            color: var(--black);
        }

        .new-chat-button {
            justify-content: flex-start;
            &:hover,
            &-active {
                border: 1px solid #0099f2;
                color: #1890ff;
                background-color: #e6f5fe !important;
            }
        }
    }
}

.collapsed-button-container {
    position: fixed;
    left: 16px;
    top: 16px;
    z-index: 1000;
}

.collapse-button-standalone {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--sidebarSecond) !important;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    &:hover {
        background-color: #e6f5fe !important;
    }
}

.sidebar-drag {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 2px;
    cursor: ew-resize;
    transition: all ease 0.3s;
    display: flex;
    align-items: center;

    &:hover,
    &:active {
        background-color: #0099f2;
    }
}

.window-content {
    width: var(--window-content-width);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.mobile {
    display: none;
}

@media only screen and (max-width: 600px) {
    .container {
        min-height: unset;
        min-width: unset;
        max-height: unset;
        min-width: unset;
        border: 0;
        border-radius: 0;
    }

    .sidebar {
        position: absolute;
        left: -100%;
        z-index: 1000;
        height: var(--full-height);
        transition: all ease 0.3s;
        box-shadow: none;
    }

    .sidebar-show {
        left: 0;
    }

    .mobile {
        display: block;
    }
}

.sidebar-header {
    position: relative;
    padding: 0 4px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease; // 添加过渡动画

    &-narrow {
        justify-content: center;
    }
    &-super-narrow {
        justify-content: center;
        padding-bottom: 10px;
    }

    &-collapsed {
        display: none;
    }
}

.collapse-button {
    transition: all 0.3s ease;
    z-index: 100;

    &-collapsed {
        display: none;
    }
}

.sidebar-logo {
    width: 27px;
    height: 27px;
}

.sidebar-title-container {
    display: inline-flex;
    flex-direction: column;
}

.sidebar-title {
    font-size: 20px;
    font-weight: bold;
    animation: slide-in ease 0.3s;
}

.sidebar-sub-title {
    font-size: 12px;
    font-weight: 400;
    animation: slide-in ease 0.3s;
}

.sidebar-body {
    flex: 1;
    overflow: auto;
    overflow-x: hidden;
}

.siderbar-body-narrow {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.sidebar-body-expand-button-container {
    margin-bottom: 16px;
}

.history-title {
    font-weight: 500;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
    margin: 18px 0 8px 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease; // 添加过渡动画

    &-super-narrow {
        justify-content: center;
        margin-left: 0;
    }
}

.sidebar-bottom {
    display: flex;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    gap: 8px;
    :global {
        .ant-avatar {
            background-color: transparent;
        }

        .ant-btn {
            width: 100%;
            padding: 0 6px;
            font-size: 15px;
        }
    }
    
    &-narrow {
        width: 100%;
    }
    
    &-super-narrow {
        justify-content: center;
    }
}

.user-dropdown-container {
    width: 100%;
    overflow: hidden;
}

.sidebar-username {
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
    margin: 0 6px;
    flex: 1;
    min-width: 0; // 允许flex项目收缩到比内容宽度小
    overflow: hidden;
}

.username-text {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dropdown-icon {
    flex-shrink: 0; // 防止图标被压缩
    margin-left: 4px;
}
