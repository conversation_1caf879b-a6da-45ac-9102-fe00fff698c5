import { getAIQuestionsListApi } from "@/app/request/modules/candidate";
import { getAllTagsApi } from "@/app/request/modules/dashboard";
import { AIQuestionResp, TrackStageStatistic } from "@/app/store/modules/candidate";
import { RespParams } from "@/app/typing";
import { useEffect, useState } from "react";
import styles from "./index.module.scss";

const AIInterviewQuestion = ({
    applicantId,
    currentStage,
}: {
    applicantId?: string;
    currentStage?: TrackStageStatistic;
}) => {
    const [questionList, setQuestionList] = useState<AIQuestionResp[]>([]);

    const getQuestionList = async () => {
        if (!applicantId || !currentStage) return;

        try {
            const res: RespParams<AIQuestionResp[]> = await (
                await getAIQuestionsListApi({ interviewId: applicantId, interviewStage: currentStage?.stageCode })
            ).json();
            if (res.code === 200) {
                setQuestionList(res.data);
            }
        } catch (err) {
            setQuestionList([]);
        }
    };

    useEffect(() => {
        getQuestionList();
    }, [applicantId, currentStage]);

    return (
        <div className={styles["ai-question-wrapper"]}>
            {questionList.map((item, index) => {
                return (
                    <div key={index} className={styles["question-card"]}>
                        <div className={styles["question-header"]}>
                            <span>{index + 1}.</span>
                            <span>{item.question}</span>
                        </div>
                        <div className={styles["question-content"]}>{item.answer}</div>
                    </div>
                );
            })}
        </div>
    );
};

export default AIInterviewQuestion;
