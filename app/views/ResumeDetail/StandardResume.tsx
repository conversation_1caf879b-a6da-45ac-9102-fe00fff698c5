import { Flex, Grid } from "antd";
import styles from "./index.module.scss";

const StandardResume = ({ resumeData }: { resumeData: any }) => {
    return (
        <div>
            <div className={styles["resume-item"]}>
                <div className={styles["resume-item__header"]}>个人信息</div>
                <div className={styles["resume-item__content"]}>
                    <Flex align="center">
                        <div className={styles["resume-item__content-item-label"]}>姓名</div>
                        <div className={styles["resume-item__content__item-value"]}>aaaaaa</div>
                    </Flex>
                    <Flex align="center">
                        <div className={styles["resume-item__content-item-label"]}>姓名22223333</div>
                        <div className={styles["resume-item__content__item-value"]}>aaaaaa</div>
                    </Flex>
                    <Flex align="center">
                        <div className={styles["resume-item__content-item-label"]}>姓名22223333</div>
                        <div className={styles["resume-item__content__item-value"]}>aaaaaa</div>
                    </Flex>
                    <Flex align="center">
                        <div className={styles["resume-item__content-item-label"]}>姓名22223333</div>
                        <div className={styles["resume-item__content__item-value"]}>aaaaaa</div>
                    </Flex>
                    <Flex align="center">
                        <div className={styles["resume-item__content-item-label"]}>姓名22223333</div>
                        <div className={styles["resume-item__content__item-value"]}>aaaaaa</div>
                    </Flex>
                    <Flex align="center">
                        <div className={styles["resume-item__content-item-label"]}>姓名22223333</div>
                        <div className={styles["resume-item__content__item-value"]}>aaaaaa</div>
                    </Flex>
                </div>
            </div>
            <div className={styles["resume-item"]}>
                <div className={styles["resume-item__header"]}>求职意向</div>
                <div className={styles["resume-item__content"]}></div>
            </div>
            <div className={styles["resume-item"]}>
                <div className={styles["resume-item__header"]}>教育经历</div>
                <div className={styles["resume-item__content"]}></div>
            </div>
            <div className={styles["resume-item"]}>
                <div className={styles["resume-item__header"]}>在校职务</div>
                <div className={styles["resume-item__content"]}></div>
            </div>{" "}
            <div className={styles["resume-item"]}>
                <div className={styles["resume-item__header"]}>实习经历</div>
                <div className={styles["resume-item__content"]}></div>
            </div>
            <div className={styles["resume-item"]}>
                <div className={styles["resume-item__header"]}>工作经历</div>
                <div className={styles["resume-item__content"]}></div>
            </div>
            <div className={styles["resume-item"]}>
                <div className={styles["resume-item__header"]}>项目经历</div>
                <div className={styles["resume-item__content"]}></div>
            </div>
            <div className={styles["resume-item"]}>
                <div className={styles["resume-item__header"]}>培训经历</div>
                <div className={styles["resume-item__content"]}></div>
            </div>
        </div>
    );
};

export default StandardResume;
