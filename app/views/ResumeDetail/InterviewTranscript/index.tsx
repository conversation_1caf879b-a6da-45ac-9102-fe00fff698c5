// 面试记录页面
import React from "react";
import { Button, Divider, Steps, Tag, Select } from "antd";
import {
    SwitcherOutlined,
    PlusSquareOutlined,
    DownloadOutlined,
    ShareAltOutlined,
    LaptopOutlined,
} from "@ant-design/icons";
import styles from "./index.module.scss";
import { TextView } from "@/app/components/TextView";
import { InterviewTranscriptResp } from "@/app/store/modules/candidate";
import { formatTime } from "@/app/utils";
import dayjs from "dayjs";

const HeaderBtnContent = () => {
    return (
        <div className={styles["header-btn-content"]}>
            <Button icon={<SwitcherOutlined />} type="text">
                总览评价
            </Button>
            <Divider type="vertical" />
            <Button icon={<ShareAltOutlined />} type="text">
                共享评价
            </Button>
            <Divider type="vertical" />
            <Button icon={<DownloadOutlined />} type="text">
                导出评价
            </Button>
            <Divider type="vertical" />
            <Button icon={<PlusSquareOutlined />} type="text">
                添加评价
            </Button>
        </div>
    );
};

interface DescriptionItemProps {
    data: InterviewTranscriptResp;
}

const DescriptionItem = ({ data }: DescriptionItemProps) => {
    const formatInterviewTime = () => {
        if (!data.gmtStart || !data.gmtEnd) return "--";

        const startTime = formatTime(data.gmtStart, "YYYY-MM-DD dddd HH:mm");
        // const endTime = formatTime(data.gmtEnd, "HH:mm");
        const endTime = dayjs(data.gmtEnd).subtract(15, "minute").format("HH:mm");

        return `${startTime}~${endTime}`;
    };

    const formatScheduledTime = (time: string) => {
        if (!time) return "--";
        return formatTime(time, "YYYY-MM-DD HH:mm");
    };

    // 根据面试结果状态获取Tag颜色
    const getTagColor = (itvRes: string) => {
        switch (itvRes) {
            case "通过":
                return "success";
            case "待定":
                return "warning";
            case "淘汰":
                return "error";
            default:
                return "default";
        }
    };

    return (
        <div className={styles["description-item"]}>
            <div className={styles["description-item-top"]}>
                <div className={styles["description-item-header"]}>
                    <div className={styles["description-item-header-title"]}>
                        {formatInterviewTime()}
                        <div className={styles["interview-type"]}>
                            <LaptopOutlined />
                            {data.video ? "视频面试" : "现场面试"}
                        </div>
                    </div>
                    <div className={styles["description-item-header-select"]}>
                        <Select
                            defaultValue="lucy"
                            style={{ fontSize: 12, height: 24 }}
                            options={[{ value: "lucy", label: "应聘者未答复-未签到" }]}
                        />
                        <Select
                            defaultValue="lucy"
                            style={{ fontSize: 12, height: 24 }}
                            allowClear
                            disabled
                            options={[{ value: "lucy", label: "更多操作" }]}
                            placeholder="select it"
                        />
                    </div>
                </div>
                <div className={styles["item-top-content"]}>
                    安排人
                    <TextView
                        text={() => {
                            return (
                                <span>
                                    {data.assginerEmpName || "--"}-{data.assginerEmpId}
                                </span>
                            );
                        }}
                    />
                    面试地址
                    <TextView text={data.loc || "--"} />
                </div>
                <div className={styles["item-top-content"]}>
                    安排时间
                    <TextView text={formatScheduledTime(data.gmtCreate)} />
                    视频工具
                    <TextView
                        text={() => {
                            if (data.video) {
                                return (
                                    <span>
                                        飞书视频面试
                                        <Button
                                            type="link"
                                            style={{ fontSize: "12px" }}
                                            href={data.appLink}
                                            target="_blank"
                                        >
                                            查看链接
                                        </Button>
                                    </span>
                                );
                            }
                            return "--";
                        }}
                    />
                </div>
            </div>
            <div className={styles["description-item-bottom"]}>
                <div className={styles["bottom-header"]}>
                    <div>
                        {data.itrEmpName || "--"}({data.itrEmpEmail})
                    </div>
                    <Button type="link">查看评价表</Button>
                </div>
                <div>
                    <Tag color={getTagColor(data.itvRes)}>{data.itvRes}</Tag> 评价时间：
                    {formatTime(data.evalTemp, "YYYY-MM-DD HH:mm")}
                </div>
                <div>{data.itvEval || "--"}</div>
            </div>
        </div>
    );
};

const InterviewTranscript: React.FC<{ data: InterviewTranscriptResp[]; loading?: boolean }> = ({ data, loading }) => {
    // Generate steps items from data
    const stepsItems = data.map((item) => ({
        title: item.itvStageName,
        description: <DescriptionItem data={item} />,
        status: "finish" as const,
    }));

    return (
        <div className={styles["interview-transcript-wrapper"]}>
            <HeaderBtnContent></HeaderBtnContent>
            <div className={styles["interview-transcript-content"]}>
                {loading ? (
                    <div style={{ padding: "20px", textAlign: "center" }}>加载中...</div>
                ) : data.length > 0 ? (
                    <Steps
                        direction="vertical"
                        size="small"
                        current={data.length > 0 ? data.length - 1 : 0}
                        items={stepsItems}
                    />
                ) : (
                    <div style={{ padding: "20px", textAlign: "center" }}>暂无面试记录</div>
                )}
            </div>
        </div>
    );
};

export default InterviewTranscript;
