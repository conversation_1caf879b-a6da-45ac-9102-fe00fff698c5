import { TrackLogResp } from "@/app/store/modules/candidate";
import styles from "./index.module.scss";
import { Divider, Empty, Flex, Timeline } from "antd";
import { useEffect, useMemo, useState } from "react";
import { CaretDownOutlined } from "@ant-design/icons";
import { DictData, RespParams } from "@/app/typing";
import { getDictDataByCodeApi } from "@/app/request/modules/common";

const OperationLogs = ({ logs }: { logs: TrackLogResp[] }) => {
    const [operateType, setOperateType] = useState<DictData[]>([]);

    const TimelineContent = ({ log }: { log: TrackLogResp }) => {
        const [expanded, setExpanded] = useState(true);

        return (
            <div className={styles["timeline-content"]}>
                <div>
                    <Flex align="center" style={{ cursor: "pointer" }} onClick={() => setExpanded(!expanded)}>
                        <CaretDownOutlined className={`${styles["icons"]} ${expanded ? "" : styles["collapsed"]}`} />
                        <div className={styles["operation-title"]}>
                            {log?.operatorEntity?.empId
                                ? `${log?.operatorEntity?.empId}-${log?.operatorEntity?.empName}`
                                : "暂无"}
                        </div>
                        <Divider type="vertical" style={{ background: "rgba(0, 0, 0, 0.26)", width: "1.5px" }} />
                        <div className={styles["operation-title"]}>
                            {operateType.find((item) => Number(item.value) === log.operationType)?.key}
                        </div>
                    </Flex>
                    <div className={`${styles["reason"]} ${expanded ? styles["expanded"] : ""}`}>
                        {log.reason ?? "暂无"}
                    </div>
                </div>
            </div>
        );
    };

    const timeLineItems = useMemo(() => {
        return logs.map((log) => ({
            label: log.operationTime,
            children: <TimelineContent log={log} />,
        }));
    }, [logs, operateType]);

    const getOperationTypeList = async () => {
        const res: RespParams<DictData[]> = await (await getDictDataByCodeApi("ITrackOperationType")).json();
        if (res.code === 200) {
            console.log("res", res.data);
            setOperateType(res.data);
        }
    };

    useEffect(() => {
        getOperationTypeList();
    }, []);

    return (
        <div className={styles["operation-logs-wrapper"]}>
            {logs.length > 0 ? (
                <div className={styles["operation-logs"]}>
                    <div className={styles["operation-logs-header"]}></div>
                    <div className={styles["operation-logs-content"]}>
                        <Timeline mode="left" items={timeLineItems} />
                    </div>
                </div>
            ) : (
                <Empty />
            )}
        </div>
    );
};

export default OperationLogs;
