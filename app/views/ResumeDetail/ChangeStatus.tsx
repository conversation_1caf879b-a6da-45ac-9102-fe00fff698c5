import ChatDrawer from "@/app/components/ChatDrawer";
import messageService from "@/app/lib/message";
import { resumeChangeStatusApi } from "@/app/request/modules/candidate";
import UseCandidateStore, {
    ChangeStatusExpose,
    ResumeChangeStatusReq,
    TrackStageStatistic,
} from "@/app/store/modules/candidate";
import { RespParams } from "@/app/typing";
import { Cascader, Form, Input } from "antd";
import { forwardRef, useImperativeHandle, useMemo, useState } from "react";

const ChangeStatus = forwardRef<ChangeStatusExpose, { mode: string; refresh: () => void }>((props, ref) => {
    const [show, setShow] = useState<boolean>(false);
    const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
    const [stageName, setStageName] = useState<string>();
    const [applicantId, setApplicantId] = useState<string>("");
    const [jobId, setJobId] = useState<string>("");
    const [currentStage, setCurrentStage] = useState<TrackStageStatistic>();

    const statusList = UseCandidateStore((state) => state.statusList);

    const [form] = Form.useForm();
    const { mode, refresh } = props;

    useImperativeHandle(ref, () => {
        return {
            showDrawer: (applicantId: string, jobId: string, stageName: string, currentStage: TrackStageStatistic) => {
                setShow(true);
                setStageName(stageName);
                setApplicantId(applicantId);
                setJobId(jobId);
                setCurrentStage(currentStage);
                form.resetFields();
            },
        };
    });

    // 格式化状态列表，统一字段
    const formatStatusList = useMemo(() => {
        return statusList.map((item: TrackStageStatistic) => {
            const disabled = mode === "Interviewer" && item.stageId !== currentStage?.stageId;

            return {
                stageId: item.stageId,
                stageName: item.stageName,
                disabled,
                children: item.trackStateStatistics?.map((child) => {
                    return {
                        stageId: child.stateId,
                        stageName: child.stateName,
                        disabled,
                    };
                }),
            };
        });
    }, [statusList, currentStage]);

    const handleOk = async () => {
        const validateRes = await form.validateFields();
        if (validateRes) {
            const formValue = form.getFieldsValue();
            const params: ResumeChangeStatusReq = {
                applicantId: applicantId,
                jobId: jobId,
                stageId: formValue.status[0],
                stateId: formValue.status[1],
                reason: formValue.reason ?? "",
            };

            try {
                setConfirmLoading(true);
                const res: RespParams<any> = await (await resumeChangeStatusApi(params)).json();
                if (res.code === 200) {
                    messageService.success(res?.msg ?? "状态变更成功");
                    setConfirmLoading(false);
                    setShow(false);
                    refresh();
                }
            } catch (err) {
                setConfirmLoading(false);
            }
        }
    };

    return (
        <ChatDrawer
            title="状态转移"
            open={show}
            confirmLoading={confirmLoading}
            onOk={handleOk}
            onCancel={() => setShow(false)}
        >
            <Form layout="vertical" form={form}>
                <Form.Item label="当前状态">
                    <Input value={stageName} disabled allowClear />
                </Form.Item>
                <Form.Item name="status" label="转移后状态" rules={[{ required: true }]}>
                    <Cascader
                        options={formatStatusList}
                        placeholder="请选择状态"
                        fieldNames={{ label: "stageName", value: "stageId", children: "children" }}
                        expandTrigger="hover"
                    />
                </Form.Item>
                <Form.Item name="reason" label="备注">
                    <Input.TextArea placeholder="请输入备注" rows={4} allowClear />
                </Form.Item>
            </Form>
        </ChatDrawer>
    );
});
ChangeStatus.displayName = "ChangeStatus";

export default ChangeStatus;
