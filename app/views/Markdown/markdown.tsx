import ReactMarkdown from "react-markdown";
import "katex/dist/katex.min.css";
import RemarkMath from "remark-math";
import RemarkBreaks from "remark-breaks";
// import RehypeKatex from "rehype-katex";
import RemarkGfm from "remark-gfm";
// import RehypeHighlight from "rehype-highlight";
import { useRef, useState, RefObject, useEffect, useMemo } from "react";
import { copyToClipboard, useWindowSize } from "../../utils";
import mermaid from "mermaid";
import Locale from "../../locales";
import LoadingIcon from "@/app/icons/three-dots.svg";
import ReloadButtonIcon from "@/app/icons/reload.svg";
import React from "react";
import { useDebouncedCallback } from "use-debounce";
import { showImageModal, FullScreen } from "../ui-lib";
import { ArtifactsShareButton, HTMLPreview, HTMLPreviewHander } from "../artifacts";
// import { useChatStore } from "../../store";
import { IconButton } from "../../components/Button";

// import { useAppConfig } from "../../store/config";
import clsx from "clsx";

/**
 * Mermaid组件 - 用于渲染mermaid图表
 * @param props.code - mermaid语法的代码字符串
 */
export function Mermaid(props: { code: string }) {
    const ref = useRef<HTMLDivElement>(null);
    const [hasError, setHasError] = useState(false);

    // 当代码变化时，使用mermaid库渲染图表
    useEffect(() => {
        if (props.code && ref.current) {
            mermaid
                .run({
                    nodes: [ref.current],
                    suppressErrors: true,
                })
                .catch((e) => {
                    setHasError(true);
                    console.error("[Mermaid] ", e.message);
                });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [props.code]);

    // 在新窗口中查看SVG图表
    function viewSvgInNewWindow() {
        const svg = ref.current?.querySelector("svg");
        if (!svg) return;
        const text = new XMLSerializer().serializeToString(svg);
        const blob = new Blob([text], { type: "image/svg+xml" });
        showImageModal(URL.createObjectURL(blob));
    }

    if (hasError) {
        return null;
    }

    return (
        <div
            className={clsx("no-dark", "mermaid")}
            style={{
                cursor: "pointer",
                overflow: "auto",
            }}
            ref={ref}
            onClick={() => viewSvgInNewWindow()}
        >
            {props.code}
        </div>
    );
}

/**
 * PreCode组件 - 处理代码块的预览和复制功能
 * 支持mermaid图表和HTML预览
 * @param props.children - 代码块内容
 */
export function PreCode(props: { children: any }) {
    const ref = useRef<HTMLPreElement>(null);
    const previewRef = useRef<HTMLPreviewHander>(null);
    const [mermaidCode, setMermaidCode] = useState("");
    const [htmlCode, setHtmlCode] = useState("");
    const { height } = useWindowSize();
    // const chatStore = useChatStore();
    // const session = chatStore.currentSession();

    // 渲染工件（mermaid图表和HTML预览）的防抖函数
    const renderArtifacts = useDebouncedCallback(() => {
        if (!ref.current) return;
        const mermaidDom = ref.current.querySelector("code.language-mermaid");
        if (mermaidDom) {
            setMermaidCode((mermaidDom as HTMLElement).innerText);
        }
        const htmlDom = ref.current.querySelector("code.language-html");
        const refText = ref.current.querySelector("code")?.innerText;
        if (htmlDom) {
            setHtmlCode((htmlDom as HTMLElement).innerText);
        } else if (refText?.startsWith("<!DOCTYPE") || refText?.startsWith("<svg") || refText?.startsWith("<?xml")) {
            setHtmlCode(refText);
        }
    }, 600);

    // const config = useAppConfig();
    // const enableArtifacts = (session.mask as any)?.enableArtifacts !== false && config.enableArtifacts;

    // 为纯文本包装段落
    useEffect(() => {
        if (ref.current) {
            const codeElements = ref.current.querySelectorAll("code") as NodeListOf<HTMLElement>;
            const wrapLanguages = ["", "md", "markdown", "text", "txt", "plaintext", "tex", "latex"];
            codeElements.forEach((codeElement) => {
                const languageClass = codeElement.className.match(/language-(\w+)/);
                const name = languageClass ? languageClass[1] : "";
                if (wrapLanguages.includes(name)) {
                    codeElement.style.whiteSpace = "pre-wrap";
                }
            });
            setTimeout(renderArtifacts, 1);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <>
            <pre ref={ref}>
                <span
                    className="copy-code-button"
                    onClick={() => {
                        if (ref.current) {
                            copyToClipboard(ref.current.querySelector("code")?.innerText ?? "");
                        }
                    }}
                ></span>
                {props.children}
            </pre>
            {mermaidCode.length > 0 && <Mermaid code={mermaidCode} key={mermaidCode} />}

            {/* && enableArtifacts */}
            {htmlCode.length > 0 && (
                <FullScreen className="no-dark html" right={70}>
                    <ArtifactsShareButton
                        style={{ position: "absolute", right: 20, top: 10 }}
                        getCode={() => htmlCode}
                    />
                    <IconButton
                        style={{ position: "absolute", right: 120, top: 10 }}
                        bordered
                        icon={<ReloadButtonIcon />}
                        shadow
                        onClick={() => previewRef.current?.reload()}
                    />
                    <HTMLPreview
                        ref={previewRef}
                        code={htmlCode}
                        autoHeight={!document.fullscreenElement}
                        height={!document.fullscreenElement ? 600 : height}
                    />
                </FullScreen>
            )}
        </>
    );
}

/**
 * CustomCode组件 - 自定义代码渲染，支持代码折叠功能
 * @param props.children - 代码内容
 * @param props.className - 代码块的CSS类名
 */
export function CustomCode(props: { children: any; className?: string }) {
    const ref = useRef<HTMLPreElement>(null);
    const [collapsed, setCollapsed] = useState(true);
    const [showToggle, setShowToggle] = useState(false);

    // 检测代码高度，决定是否显示折叠切换按钮
    useEffect(() => {
        if (ref.current) {
            const codeHeight = ref.current.scrollHeight;
            setShowToggle(codeHeight > 400);
            ref.current.scrollTop = ref.current.scrollHeight;
        }
    }, [props.children]);

    // 切换代码折叠状态
    const toggleCollapsed = () => {
        setCollapsed((collapsed) => !collapsed);
    };

    // 渲染"显示更多"按钮
    const renderShowMoreButton = () => {
        if (showToggle && collapsed) {
            return (
                <div
                    className={clsx("show-hide-button", {
                        collapsed,
                        expanded: !collapsed,
                    })}
                >
                    <button onClick={toggleCollapsed}>{Locale.NewChat.More}</button>
                </div>
            );
        }
        return null;
    };
    return (
        <>
            <code
                className={clsx(props?.className)}
                ref={ref}
                style={{
                    maxHeight: collapsed ? "400px" : "none",
                    overflowY: "hidden",
                }}
            >
                {props.children}
            </code>
            {/* 代码块过长，展开 */}
            {renderShowMoreButton()}
        </>
    );
}

/**
 * escapeBrackets函数 - 转义LaTeX格式的方括号和圆括号为美元符号格式
 * 不会影响代码块中的内容
 * @param text - 需要处理的文本
 * @returns 处理后的文本
 */
export function escapeBrackets(text: string) {
    const pattern = /(```[\s\S]*?```|`.*?`)|\\\[([\s\S]*?[^\\])\\\]|\\\((.*?)\\\)/g;
    return text.replace(pattern, (match, codeBlock, squareBracket, roundBracket) => {
        if (codeBlock) {
            return codeBlock;
        } else if (squareBracket) {
            return `$$${squareBracket}$$`;
        } else if (roundBracket) {
            return `$${roundBracket}$`;
        }
        return match;
    });
}

/**
 * tryWrapHtmlCode函数 - 尝试包装HTML代码块
 * 检测HTML文档并添加适当的代码块标记
 * @param text - 需要处理的文本
 * @returns 处理后的文本
 */
export function tryWrapHtmlCode(text: string) {
    // 如果已经包含代码块标记，则不处理
    if (text.includes("```")) {
        return text;
    }
    return text
        .replace(/([`]*?)(\w*?)([\n\r]*?)(<!DOCTYPE html>)/g, (match, quoteStart, lang, newLine, doctype) => {
            return !quoteStart ? "\n```html\n" + doctype : match;
        })
        .replace(
            /(<\/body>)([\r\n\s]*?)(<\/html>)([\n\r]*)([`]*)([\n\r]*?)/g,
            (match, bodyEnd, space, htmlEnd, newLine, quoteEnd) => {
                return !quoteEnd ? bodyEnd + space + htmlEnd + "\n```\n" : match;
            }
        );
}

/**
 * _MarkDownContent组件 - 核心Markdown渲染组件
 * 使用ReactMarkdown处理各种Markdown元素的渲染
 * @param props.content - Markdown文本内容
 */
function _MarkDownContent(props: { content: string }) {
    // 预处理内容
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const processedContent = useMemo(() => {
        return tryWrapHtmlCode(escapeBrackets(props.content));
    }, [props.content]);

    // 使用ReactMarkdown渲染器
    return (
        <ReactMarkdown
            remarkPlugins={[RemarkMath, RemarkGfm, RemarkBreaks]}
            // rehypePlugins={[
            //     RehypeKatex,
            //     [
            //         RehypeHighlight,
            //         {
            //             detect: false,
            //             ignoreMissing: true,
            //         },
            //     ],
            // ]}
            components={{
                pre: PreCode, // 自定义代码块渲染
                code: CustomCode, // 自定义内联代码渲染
                p: (pProps) => <p {...pProps} dir="auto" />, // 支持自动文本方向
                a: (aProps) => {
                    const href = aProps.href || "";
                    // 检测并渲染音频链接
                    if (/\.(aac|mp3|opus|wav)$/.test(href)) {
                        return (
                            <figure>
                                <audio controls src={href}></audio>
                            </figure>
                        );
                    }
                    // 检测并渲染视频链接
                    if (/\.(3gp|3g2|webm|ogv|mpeg|mp4|avi)$/.test(href)) {
                        return (
                            <video controls width="99.9%">
                                <source src={href} />
                            </video>
                        );
                    }
                    // 处理内部链接和外部链接
                    const isInternal = /^\/#/i.test(href);
                    const target = isInternal ? "_self" : (aProps.target ?? "_blank");
                    return <a {...aProps} target={target} />;
                },
            }}
        >
            {processedContent}
        </ReactMarkdown>
    );
}

// 使用React.memo优化渲染性能，避免不必要的重新渲染
export const MarkdownContent = React.memo(_MarkDownContent);

/**
 * Markdown组件 - 最终导出的Markdown渲染组件
 * 包含加载状态、字体大小和字体系列等配置选项
 * @param props.content - Markdown文本内容
 * @param props.loading - 是否显示加载状态
 * @param props.fontSize - 字体大小
 * @param props.fontFamily - 字体系列
 * @param props.parentRef - 父元素的引用
 * @param props.defaultShow - 是否默认显示
 */
export function Markdown(
    props: {
        content: string;
        loading?: boolean;
        fontSize?: number;
        fontFamily?: string;
        parentRef?: RefObject<HTMLDivElement>;
        defaultShow?: boolean;
    } & React.DOMAttributes<HTMLDivElement>
) {
    const mdRef = useRef<HTMLDivElement>(null);

    return (
        <div
            className="markdown-body"
            style={{
                fontSize: `${props.fontSize ?? 14}px`,
                fontFamily: props.fontFamily || "inherit",
            }}
            ref={mdRef}
            onContextMenu={props.onContextMenu}
            onDoubleClickCapture={props.onDoubleClickCapture}
            dir="auto"
        >
            {props.loading ? <LoadingIcon /> : <MarkdownContent content={props.content} />}
        </div>
    );
}
