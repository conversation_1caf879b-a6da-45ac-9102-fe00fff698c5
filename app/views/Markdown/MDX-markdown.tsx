import React, { useRef, RefObject, useMemo, useState, useCallback } from "react";
import { MarkdownRender } from "@douyinfe/semi-ui";
import LoadingIcon from "@/app/icons/three-dots.svg";
import clsx from "clsx";
import { CustomCode } from "./markdown";
import remarkMath from "remark-math";
import remarkBreaks from "remark-breaks";
import remarkGfm from "remark-gfm";
import { MobileTwoTone, MailTwoTone } from "@ant-design/icons";
import { Path } from "@/app/constant";
import useRightSidebarStore from "@/app/store/rightSidebar";
import { useDragSideBar } from "@/app/views/LeftSidebar";
import dynamic from "next/dynamic";

// 使用 dynamic 导入确保 PDFViewer 组件只在客户端渲染
const PDFViewer = dynamic(() => import("@/app/components/PdfViewer").then((mod) => mod.PDFViewer), {
    ssr: false,
    loading: () => (
        <div style={{ textAlign: "center", padding: "20px" }}>
            <div>加载中...</div>
        </div>
    ),
});

// 简历卡片组件
const ResumeCard = ({ data }: { data: any }) => {
    const { setVisible, setHeader, setContent } = useRightSidebarStore();
    const { isCollapsed, toggleCollapse } = useDragSideBar();

    if (!data || !data.entity) return null;

    const { entity, score, id } = data;
    const { name, highest_edu, gender, birthday, skill_list, contact_info } = entity;

    const getResumeDetail = async (id: string): Promise<string | null> => {
        try {
            const response = await fetch(`http://10.32.228.50:8888/api/v1alpha1/talent/cv/download?id=${id}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/octet-stream",
                },
            });

            if (!response.ok) {
                throw new Error(`获取简历失败: ${response.status} ${response.statusText}`);
            }

            const blob = await response.blob();
            return URL.createObjectURL(blob);
        } catch (error) {
            console.error("获取简历详情出错:", error);
            return null;
        }
    };

    const handleClick = async (name: string, id: string) => {
        setVisible(true, Path.Chat);

        // 设置标题
        setHeader(
            <div>
                <div>{name} 的简历详情</div>
            </div>
        );

        // 显示加载中状态
        setContent(
            <div style={{ padding: "20px", textAlign: "center" }}>
                <div>正在加载简历...</div>
            </div>
        );

        // 如果侧边栏已折叠，则展开它
        if (!isCollapsed) {
            toggleCollapse();
        }

        // 获取PDF URL
        const pdfUrl = await getResumeDetail(id);

        // 设置内容为PDF查看器或错误信息
        if (pdfUrl) {
            // 使用类型断言确保类型安全
            setContent((<PDFViewer pdfUrl={pdfUrl} />) as React.ReactElement);
        } else {
            setContent(
                <div style={{ padding: "20px", textAlign: "center", color: "#ff4d4f" }}>
                    <div>获取简历失败，请稍后重试。</div>
                </div>
            );
        }
    };

    return (
        <div
            style={{
                border: "1px solid #e0e0e0",
                borderRadius: "8px",
                padding: "16px",
                marginBottom: "16px",
                backgroundColor: "#f9f9f9",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                cursor: "pointer",
            }}
            onClick={() => {
                handleClick(name, id);
            }}
        >
            <div style={{ display: "flex", justifyContent: "space-between", marginBottom: "8px" }}>
                <h3 style={{ margin: "0", color: "#1677ff" }}>{name || "未知姓名"}</h3>
                <span
                    style={{
                        backgroundColor: "#e6f7ff",
                        color: "#1677ff",
                        padding: "2px 8px",
                        borderRadius: "12px",
                        fontSize: "12px",
                    }}
                >
                    匹配度: {(score * 100).toFixed(1)}%
                </span>
            </div>

            <div style={{ display: "flex", flexWrap: "wrap", gap: "8px", marginBottom: "12px" }}>
                {highest_edu && (
                    <span
                        style={{
                            backgroundColor: "#f0f0f0",
                            padding: "2px 8px",
                            borderRadius: "4px",
                            fontSize: "12px",
                        }}
                    >
                        {typeof highest_edu === "string"
                            ? highest_edu
                            : `${highest_edu.school || ""} ${highest_edu.degree || ""} ${highest_edu.major || ""}`}
                    </span>
                )}
                {gender && (
                    <span
                        style={{
                            backgroundColor: "#f0f0f0",
                            padding: "2px 8px",
                            borderRadius: "4px",
                            fontSize: "12px",
                        }}
                    >
                        {gender}
                    </span>
                )}
                {birthday && (
                    <span
                        style={{
                            backgroundColor: "#f0f0f0",
                            padding: "2px 8px",
                            borderRadius: "4px",
                            fontSize: "12px",
                        }}
                    >
                        {birthday}
                    </span>
                )}
            </div>

            {skill_list && skill_list.length > 0 && (
                <div style={{ marginBottom: "12px" }}>
                    <strong>技能：</strong>
                    <div style={{ display: "flex", flexWrap: "wrap", gap: "6px", marginTop: "4px" }}>
                        {skill_list.map((skill: string, index: number) => (
                            <span
                                key={index}
                                style={{
                                    backgroundColor: "#e6f4ff",
                                    color: "#1677ff",
                                    padding: "2px 8px",
                                    borderRadius: "4px",
                                    fontSize: "12px",
                                }}
                            >
                                {skill}
                            </span>
                        ))}
                    </div>
                </div>
            )}

            {contact_info && (
                <div>
                    <strong>联系方式：</strong>
                    <div
                        style={{
                            display: "flex",
                            flexDirection: "column",
                            gap: "4px",
                            marginTop: "4px",
                            fontSize: "12px",
                        }}
                    >
                        {contact_info.phone_list && contact_info.phone_list.length > 0 && (
                            <div>
                                <MobileTwoTone style={{ marginRight: "4px" }} />：{contact_info.phone_list.join(", ")}
                            </div>
                        )}
                        {contact_info.email_list && contact_info.email_list.length > 0 && (
                            <div>
                                <MailTwoTone style={{ marginRight: "4px" }} />：{contact_info.email_list.join(", ")}
                            </div>
                        )}
                        {contact_info.qq && <div>QQ: {contact_info.qq}</div>}
                        {contact_info.wechat && <div>微信: {contact_info.wechat}</div>}
                    </div>
                </div>
            )}

            {/* <div style={{ fontSize: "10px", color: "#999", marginTop: "12px" }}>ID: {id.substring(0, 8)}...</div> */}
        </div>
    );
};

// 简历列表组件
const ResumeList = ({ content }: { content: string }) => {
    try {
        // 尝试解析内容
        let jsonData;
        try {
            jsonData = JSON.parse(content);
        } catch (e) {
            console.error("JSON解析失败:", e);
            // 直接显示原始内容
            return (
                <pre>
                    <code>{content}</code>
                </pre>
            );
        }

        // 处理数据是字符串数组的情况（新格式）
        if (Array.isArray(jsonData) && jsonData.length > 0 && typeof jsonData[0] === "string") {
            // 尝试解析每个字符串为JSON对象
            const parsedItems = jsonData
                .map((item, index) => {
                    try {
                        return JSON.parse(item);
                    } catch (parseErr) {
                        console.error(`无法解析第${index}项:`, parseErr);
                        return null;
                    }
                })
                .filter(Boolean); // 过滤掉解析失败的项目

            if (parsedItems.length > 0) {
                return (
                    <div>
                        <h3 style={{ marginBottom: "16px", color: "#1677ff" }}>简历推荐结果（{parsedItems.length}）</h3>
                        {parsedItems.map((item, index) => (
                            <ResumeCard key={item.id || index} data={item} />
                        ))}
                    </div>
                );
            }
        }

        // 处理常规的对象数组（旧格式）
        if (Array.isArray(jsonData) && jsonData.length > 0) {
            // 过滤出有效的简历数据
            const validData = jsonData.filter((item) => item && item.entity);

            if (validData.length > 0) {
                return (
                    <div>
                        <h3 style={{ marginBottom: "16px", color: "#1677ff" }}>简历推荐结果（{validData.length}）</h3>
                        {validData.map((item, index) => (
                            <ResumeCard key={item.id || index} data={item} />
                        ))}
                    </div>
                );
            }
        }

        // 不是简历数据，原样返回
        return (
            <pre>
                <code>{content}</code>
            </pre>
        );
    } catch (e) {
        // 解析错误，原样返回
        console.error("简历列表渲染失败:", e);
        return (
            <pre>
                <code>{content}</code>
            </pre>
        );
    }
};

// 混合内容解析函数：将内容分为JSON和富文本部分
const parseMixedContent = (content: string): { jsonPart: string | null; textPart: string } => {
    try {
        // 处理新的数据格式：一段完整的JSON，然后是富文本
        // 首先检查内容是否以JSON数组开始
        if (!content.trimStart().startsWith("[")) {
            return { jsonPart: null, textPart: content };
        }

        // 寻找JSON数组结束位置
        let jsonEnd = -1;
        let depth = 0;
        let inString = false;
        let escapeNext = false;

        // 解析JSON，查找结束位置
        for (let i = 0; i < content.length; i++) {
            const char = content[i];

            if (escapeNext) {
                escapeNext = false;
                continue;
            }

            if (char === "\\") {
                escapeNext = true;
                continue;
            }

            if (char === '"' && !escapeNext) {
                inString = !inString;
                continue;
            }

            if (inString) continue;

            if (char === "[") {
                depth++;
            } else if (char === "]") {
                depth--;
                if (depth === 0) {
                    // 找到了JSON数组的结束位置
                    jsonEnd = i;
                    break;
                }
            }
        }

        // 如果找不到完整的JSON数组，返回整个内容作为文本
        if (jsonEnd === -1) {
            return { jsonPart: null, textPart: content };
        }

        // 提取JSON部分
        const jsonPart = content.substring(0, jsonEnd + 1);

        // 找到文本部分的起始位置（跳过JSON后的空白字符和可能的分隔符）
        let textStartIndex = jsonEnd + 1;
        while (
            textStartIndex < content.length &&
            (content[textStartIndex] === "," ||
                content[textStartIndex] === " " ||
                content[textStartIndex] === "\n" ||
                content[textStartIndex] === "\r")
        ) {
            textStartIndex++;
        }

        // 提取文本部分
        const textPart = content.substring(textStartIndex);

        try {
            // 处理JSON字符串，可能包含转义的双引号和特殊字符
            const cleanedJson = processJsonString(jsonPart);

            // 尝试解析JSON
            const jsonData = JSON.parse(cleanedJson);

            // 验证是否是有效的简历数据结构
            if (Array.isArray(jsonData)) {
                // 尝试解析和处理每个字符串项
                const processedItems = jsonData
                    .map((item) => {
                        // 如果项目是字符串（可能是包含简历数据的JSON字符串）
                        if (typeof item === "string") {
                            try {
                                // 尝试解析JSON字符串
                                return JSON.parse(item);
                            } catch (parseErr) {
                                console.error("无法解析JSON字符串项:", parseErr);
                                return null;
                            }
                        }
                        return item;
                    })
                    .filter(Boolean); // 过滤掉无法解析的项

                // 如果有有效的简历数据
                if (processedItems.length > 0 && processedItems.some((item) => item?.entity)) {
                    return {
                        jsonPart: JSON.stringify(processedItems),
                        textPart: textPart || "",
                    };
                }
            }

            // 如果不是有效的简历数据结构但依然是数组，返回原始JSON
            if (Array.isArray(jsonData)) {
                return {
                    jsonPart: jsonPart,
                    textPart: textPart || "",
                };
            }
        } catch (jsonError) {
            console.error("JSON解析失败:", jsonError);

            // 尝试通过正则表达式识别简历数据
            const regexResult = extractResumeDataWithRegex(jsonPart);
            if (regexResult) {
                return {
                    jsonPart: regexResult,
                    textPart: textPart || "",
                };
            }
        }

        // 如果解析失败，仍然返回拆分后的内容
        return {
            jsonPart: jsonPart,
            textPart: textPart || "",
        };
    } catch (e) {
        console.error("解析混合内容失败:", e);
        return { jsonPart: null, textPart: content };
    }
};

// 处理JSON字符串的辅助函数
function processJsonString(jsonStr: string): string {
    return (
        jsonStr
            // 处理连续的逗号
            .replace(/,\s*,+/g, ",")
            // 处理数组末尾多余的逗号
            .replace(/,\s*]/g, "]")
            // 确保数组之间有逗号
            .replace(/\]\s*\[/g, "],[")
    );
}

// 用正则表达式提取简历数据的辅助函数
function extractResumeDataWithRegex(jsonPart: string): string | null {
    try {
        // 使用正则表达式识别简历数据的实体和分数
        const entityMatches = jsonPart.match(/"entity"\s*:\s*{[^}]*}/g) || [];
        const scoreMatches = jsonPart.match(/"score"\s*:\s*[0-9.]+/g) || [];
        const idMatches = jsonPart.match(/"id"\s*:\s*"[^"]*"/g) || [];

        if (entityMatches.length > 0) {
            // 构建简历数据数组
            const resumeItems = [];

            for (let i = 0; i < Math.max(entityMatches.length, scoreMatches.length); i++) {
                const entity = entityMatches[i % entityMatches.length] || '"entity":{}';
                const score = scoreMatches[i % scoreMatches.length] || '"score":0.5';
                const id = idMatches[i % idMatches.length] || `"id":"generated-${i}"`;

                resumeItems.push(`{${entity},${score},${id}}`);
            }

            if (resumeItems.length > 0) {
                const resumeArray = `[${resumeItems.join(",")}]`;

                // 验证构建的JSON
                try {
                    JSON.parse(resumeArray);
                    return resumeArray;
                } catch (parseError) {
                    console.error("构建的简历数据格式无效:", parseError);
                }
            }
        }
    } catch (e) {
        console.error("正则表达式提取简历数据失败:", e);
    }

    return null;
}

/**
 * Semi MarkdownRender 组件 - 替换原有的 MDXMarkdown
 *
 * @param props.content - Markdown 文本内容
 * @param props.loading - 是否显示加载状态
 * @param props.fontSize - 字体大小
 * @param props.fontFamily - 字体系列
 * @param props.parentRef - 父元素的引用
 * @param props.defaultShow - 是否默认显示
 */
export function MDXMarkdown(
    props: {
        content: string;
        loading?: boolean;
        fontSize?: number;
        fontFamily?: string;
        parentRef?: RefObject<HTMLDivElement>;
        defaultShow?: boolean;
    } & React.DOMAttributes<HTMLDivElement>
) {
    const mdRef = useRef<HTMLDivElement>(null);

    // 解析混合内容
    const { jsonPart, textPart } = useMemo(() => parseMixedContent(props.content), [props.content]);

    // 处理文本部分，移除可能导致MDX解析错误的内容
    const processedTextPart = useMemo(() => {
        if (!textPart) return "";

        // 清理可能导致解析错误的内容，但保留Markdown语法
        return (
            textPart
                // 移除可能导致Unicode转义序列错误的内容
                .replace(/\\u[0-9a-fA-F]{0,3}([^0-9a-fA-F]|$)/g, (match, p1) => p1 || "")
                // 修复不完整的转义序列，但保留Markdown特殊字符
                // eslint-disable-next-line no-useless-escape
                .replace(/\\([^unrtvfbx0-9a-fA-F\*\_\`\#\~\>\<\[\]\(\)\{\}\!\|])/g, "$1")
                // 将换行符替换为普通空格
                .replace(/\r/g, " ")
                // 处理可能导致解析错误的特殊符号，但允许ASCII和中文字符
                // eslint-disable-next-line no-useless-escape
                .replace(/[^\x20-\x7E\u4E00-\u9FFF\u3000-\u303F\uFF00-\uFFEF\s\*\_\`\#\~\>\<\[\]\(\)\{\}\!\|\.]/g, "")
                // 移除连续的逗号
                .replace(/,{2,}/g, ",")
        );
    }, [textPart]);

    // 错误状态
    const [renderError, setRenderError] = useState(false);

    // 自定义组件
    const components = {
        // pre: (props: any) => <PreCode {...props} />,
        code: (props: any) => {
            // 检查是否是json代码块且包含简历数据
            if (
                props.className === "language-json" ||
                props.className === "json" ||
                (props.children.trim().startsWith("[") && props.children.trim().endsWith("]"))
            ) {
                try {
                    // 尝试解析JSON内容
                    const content = props.children.trim();
                    const jsonData = JSON.parse(content);

                    // 检查是否是简历数据结构
                    if (
                        Array.isArray(jsonData) &&
                        jsonData.length > 0 &&
                        typeof jsonData[0] === "object" &&
                        jsonData[0] !== null
                    ) {
                        // 进一步验证是否包含entity字段
                        if (jsonData.some((item) => item.entity && typeof item.entity === "object")) {
                            return <ResumeList content={content} />;
                        }
                    }
                } catch (e) {
                    console.log("JSON解析失败:", e);
                    // 解析失败就当普通代码处理
                }
            }
            return <CustomCode {...props} />;
        },
        // 直接添加pre标签的处理，拦截JSON代码块
        pre: (props: any) => {
            // 如果是包含code子元素的pre，且code是JSON
            if (
                props.children &&
                props.children.props &&
                (props.children.props.className === "language-json" || props.children.props.className === "json")
            ) {
                try {
                    const content = props.children.props.children;
                    const jsonData = JSON.parse(content);

                    if (
                        Array.isArray(jsonData) &&
                        jsonData.length > 0 &&
                        jsonData.some((item) => item.entity && typeof item.entity === "object")
                    ) {
                        return <ResumeList content={content} />;
                    }
                } catch (e) {
                    // 解析失败，使用默认渲染
                }
            }

            // 默认情况返回原始的props.children
            return <pre {...props} />;
        },
        p: (props: any) => <p {...props} dir="auto" />,
        a: (props: any) => {
            const href = props.href || "";
            // 检测并渲染音频链接
            if (/\.(aac|mp3|opus|wav)$/.test(href)) {
                return (
                    <figure>
                        <audio controls src={href}></audio>
                    </figure>
                );
            }
            // 检测并渲染视频链接
            if (/\.(3gp|3g2|webm|ogv|mpeg|mp4|avi)$/.test(href)) {
                return (
                    <video controls width="99.9%">
                        <source src={href} />
                    </video>
                );
            }
            // 处理内部链接和外部链接
            const isInternal = /^\/#/i.test(href);
            const target = isInternal ? "_self" : (props.target ?? "_blank");
            return <a {...props} target={target} />;
        },
    };

    // 配置 remarkPlugins
    const remarkPlugins: any[] = [remarkMath, remarkBreaks, remarkGfm];

    // 配置 rehypePlugins
    const rehypePlugins: any[] = [];

    // 修改renderTextPart函数，确保能处理包含Markdown代码块的内容
    const renderTextPart = useCallback(() => {
        if (!processedTextPart || renderError) {
            return <div className="plain-text">{textPart}</div>;
        }

        try {
            // 始终使用MarkdownRender组件处理文本，不再根据文本内容特征决定是否使用Markdown渲染
            return (
                <MarkdownRender
                    raw={processedTextPart}
                    format="md"
                    components={components}
                    remarkPlugins={remarkPlugins}
                    rehypePlugins={rehypePlugins}
                    remarkGfm={true}
                />
            );
        } catch (error) {
            console.error("渲染Markdown失败:", error);
            setRenderError(true);
            return <div className="plain-text">{textPart}</div>;
        }
    }, [processedTextPart, textPart, renderError, components, remarkPlugins, rehypePlugins]);

    return (
        <div
            className={clsx("markdown-body")}
            style={{
                fontSize: `${props.fontSize ?? 14}px`,
                fontFamily: props.fontFamily || "inherit",
            }}
            ref={mdRef}
            onContextMenu={props.onContextMenu}
            onDoubleClickCapture={props.onDoubleClickCapture}
            dir="auto"
        >
            {props.loading ? (
                <LoadingIcon />
            ) : (
                <>
                    {/* 渲染JSON部分（如果存在） */}
                    {jsonPart && <ResumeList content={jsonPart} />}

                    {/* 渲染文本部分（如果存在） */}
                    {textPart && textPart.trim() !== "" && renderTextPart()}
                </>
            )}
        </div>
    );
}

export const MDXMarkdownContent = MDXMarkdown;
