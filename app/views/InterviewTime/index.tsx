import styles from "./index.module.scss";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ConfigProvider, notification } from "antd";
import { RollbackOutlined } from "@ant-design/icons";
import ZoomIcon from "@/app/icons/talentTool/zoom.svg";
import ZoomOutIcon from "@/app/icons/talentTool/zoomout.svg";
import React, { useEffect, useRef } from "react";
import Calendar from "./components/Calendar";
import useFullScreenStore from "@/app/store/modules/fullscreen";
// import BreadCrumb from "@/app/components/BreadCrumb";
// import { Path } from "@/app/constant";
import useFeishuAuthorizationStore from "@/app/store/modules/feishu-authorization";
import { safeLocalStorage } from "@/app/utils";
import { getAppBaseUrlByEnv } from "@/app/utils/url";
import InterviewTimeIcon from "@/app/icons/sidebar/interview-time.svg";
import { useSearchParams } from "react-router-dom";

const InterviewTime: React.FC = () => {
    const { isFullScreen, setIsFullScreen, toggleFullScreen } = useFullScreenStore((state) => ({
        isFullScreen: state.isFullScreen,
        setIsFullScreen: state.setIsFullScreen,
        toggleFullScreen: state.toggleFullScreen,
    }));
    const mainContentRef = useRef<HTMLDivElement>(null);
    const [searchParams, setSearchParams] = useSearchParams();

    const { queryFeishuAuthorization, getFeishuAuthorizationUrl, completeFeishuAuthorization } =
        useFeishuAuthorizationStore();

    const token = JSON.parse(safeLocalStorage().getItem("chat-userinfo-store") ?? "{}")?.state?.token;

    // 监听全屏状态变化
    useEffect(() => {
        const handleFullscreenChange = () => {
            setIsFullScreen(!!document.fullscreenElement);
        };

        document.addEventListener("fullscreenchange", handleFullscreenChange);
        return () => {
            document.removeEventListener("fullscreenchange", handleFullscreenChange);
        };
    }, [setIsFullScreen]);

    // 处理飞书授权回调
    useEffect(() => {
        const code = searchParams.get("code");
        const redirectUrl = getAppBaseUrlByEnv() + "#/interview-time";

        if (code) {
            // 检测到授权回调，调用完成授权接口
            completeFeishuAuthorization({ code: code, redirect: redirectUrl })
                .then((success) => {
                    if (success) {
                        notification.success({
                            message: "飞书授权成功",
                            description: "您已成功完成飞书日历授权，现在可以正常使用面试时间功能了。",
                        });
                    } else {
                        notification.error({
                            message: "飞书授权失败",
                            description: "授权过程中出现问题，请重试。",
                        });
                    }
                })
                .catch((error) => {
                    console.error("完成飞书授权失败:", error);
                    notification.error({
                        message: "飞书授权失败",
                        description: "授权过程中出现错误，请重试。",
                    });
                })
                .finally(() => {
                    // 清除URL中的code参数
                    const newSearchParams = new URLSearchParams(searchParams);
                    newSearchParams.delete("code");
                    setSearchParams(newSearchParams, { replace: true });
                });
        }
    }, [searchParams, setSearchParams, completeFeishuAuthorization]);

    useEffect(() => {
        queryFeishuAuthorization({ token }).then((res) => {
            if (!res.authorized) {
                notification.warning({
                    message: "还没有进行飞书授权",
                    description: (
                        <>
                            请完成飞书授权
                            <Button
                                type="link"
                                onClick={() => {
                                    // 使用工具函数动态构建重定向URL
                                    const redirectUrl = getAppBaseUrlByEnv() + "#/interview-time";
                                    const authUrl = getFeishuAuthorizationUrl({
                                        redirect: redirectUrl,
                                    });
                                    window.location.href = authUrl;
                                }}
                            >
                                去授权
                            </Button>
                        </>
                    ),
                    duration: 0,
                });
            }
        });
    }, [queryFeishuAuthorization, getFeishuAuthorizationUrl, token]);

    return (
        <div
            className={`${styles.container} ${isFullScreen ? styles["container--fullscreen"] : ""}`}
            ref={mainContentRef}
            id="interviewTimeId"
        >
            {/* 用ConfigProvider包裹，让message可以在全屏下显示 */}
            <ConfigProvider getPopupContainer={() => mainContentRef.current || document.body}>
                {/* 面包屑导航 */}
                {/* <div className={styles["breadcrumb-wrapper"]}>
                    <BreadCrumb currentPath={Path.InterviewTime} currentTitle="面试时间" />
                </div> */}
                <div className={styles["container-header"]}>
                    <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                        <Button
                            icon={<RollbackOutlined />}
                            onClick={() => {
                                window.history.back();
                            }}
                        />
                        <InterviewTimeIcon />
                        面试时间设置
                    </div>
                    <Tooltip title={isFullScreen ? "退出全屏" : "进入全屏"}>
                        <Button
                            type="text"
                            icon={isFullScreen ? <ZoomOutIcon /> : <ZoomIcon />}
                            className={styles["more-button"]}
                            onClick={() => toggleFullScreen(mainContentRef.current, "interviewTimeId")}
                        />
                    </Tooltip>
                </div>

                <div className={styles["container-content"]}>
                    <Calendar />
                </div>
            </ConfigProvider>
        </div>
    );
};

export default InterviewTime;
