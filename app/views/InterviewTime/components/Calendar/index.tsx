import React, { useCallback, useRef, useState, useEffect } from "react";
import styles from "./index.module.scss";
import {
    message,
    Button,
    Segmented,
    Modal,
    Popover,
    Form,
    Input,
    Select,
    Space,
    DatePicker,
    Alert,
    // Avatar,
} from "antd";
import type { DatePickerProps } from "antd";
import { DownOutlined } from "@ant-design/icons";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import interactionPlugin from "@fullcalendar/interaction";
import timeGridPlugin from "@fullcalendar/timegrid";
import { LeftOutlined, RightOutlined, CloseOutlined, LoadingOutlined } from "@ant-design/icons";
import { TextView } from "@/app/components/TextView";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import updateLocale from "dayjs/plugin/updateLocale";
import minMax from "dayjs/plugin/minMax";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import useInterviewTimeStore, { ViewScheduleDetailResponse } from "@/app/store/modules/interview-time";
import useUserInfoStore from "@/app/store/userInfo";
// import { InterviewStatus } from "@/app/store/modules/interview-time";

dayjs.extend(updateLocale);
dayjs.extend(minMax);
dayjs.extend(isSameOrBefore);
dayjs.updateLocale("zh-cn", { weekStart: 0 });
dayjs.locale("zh-cn");

interface CalendarProps {
    className?: string;
}

interface SelectInfo {
    start: Date;
    end: Date;
    view: any;
}

const Calendar: React.FC<CalendarProps> = ({ className }) => {
    const calendarRef = useRef<FullCalendar>(null);
    const [currentView, setCurrentView] = useState<string>("周");
    const [currentDate, setCurrentDate] = useState(dayjs());
    // 每个事件的popover弹窗
    const [popoverVisible, setPopoverVisible] = useState<boolean>(false);
    const [selectedInfo, setSelectedInfo] = useState<SelectInfo | null>(null);
    // 每个单元格的气泡位置
    const [popoverPosition, setPopoverPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
    // 控制事件的内容块
    const [events, setEvents] = useState<any[]>([]);
    // 公司节假日
    const [companyHolidays, setCompanyHolidays] = useState<any[]>([]);
    const [userOperableDates, setUserOperableDates] = useState<string[] | null>(null);
    // 月视图popover
    const [monthPopoverVisible, setMonthPopoverVisible] = useState(false);
    const [monthPopoverPosition, setMonthPopoverPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
    const [selectedDateForPopover, setSelectedDateForPopover] = useState<dayjs.Dayjs | null>(null);
    const [availableSlots, setAvailableSlots] = useState<{ start: dayjs.Dayjs; end: dayjs.Dayjs }[]>([]);
    const [isLoadingSlots, setIsLoadingSlots] = useState(false);

    // 日程详情 popover
    const [scheduleDetail, setScheduleDetail] = useState<ViewScheduleDetailResponse | null>(null);
    const [isDetailLoading, setIsDetailLoading] = useState(false);
    const [detailPopoverVisible, setDetailPopoverVisible] = useState(false);
    const [detailPopoverPosition, setDetailPopoverPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

    const [form] = Form.useForm();
    // hook的形式让message组件可以全局使用
    const [messageApi, contextHolder] = message.useMessage();
    const [modalApi, modalHolder] = Modal.useModal();
    // 用户的信息，工号
    const { user } = useUserInfoStore((state) => ({
        user: state.user,
    }));
    const userId = user.emp_id;

    const FreeTimeBorderColor = "#dcf5e3";
    const FreeTimeBackgroundColor = "rgba(220, 245, 227, 0.7)";
    const InterviewBorderColor = "#bbe7e5";

    const { createSchedule, querySchedule, updateSchedule, deleteSchedule, getCompanyHoliday, getUserOperableDate } =
        useInterviewTimeStore();

    const fetchCompanyHoliday = async () => {
        try {
            // 获取公司节假日，不填起止时间和截止时间，默认当月
            const res = await getCompanyHoliday({
                startTime: "",
                endTime: "",
            });
            // 从响应对象中获取公司节假日列表
            if (res && Array.isArray(res.companyHolidayList)) {
                const holidayEvents = res.companyHolidayList.map((holiday: any) => ({
                    start: holiday.start_time,
                    end: holiday.end_time,
                    display: "background",
                    backgroundColor: "#f0f0f0",
                    allDay: true,
                }));
                setCompanyHolidays(holidayEvents);
            } else {
                setCompanyHolidays([]);
            }
        } catch (error) {
            messageApi.error("获取公司节假日失败");
            setCompanyHolidays([]);
        }
    };

    const fetchUserOperableDate = async () => {
        try {
            const formattedTime = dayjs().add(2, "day").format("YYYY-MM-DD HH:mm:ss"); // 假设返回类似 "2025-07-01 12:30:45" 的字符串
            // 转换为时间戳（秒）
            const timestamp = dayjs(formattedTime, "YYYY-MM-DD HH:mm:ss").unix();
            const res = await getUserOperableDate({
                from: timestamp,
                threshold: "6", // 获取未来6天
            });
            if (Array.isArray(res)) {
                setUserOperableDates(res.map((dateStr) => dayjs(dateStr).format("YYYY-MM-DD")));
            }
        } catch (error) {
            messageApi.error("获取可操作日期失败");
            setUserOperableDates([]);
        }
    };

    useEffect(() => {
        fetchCompanyHoliday();
        fetchUserOperableDate();
    }, []);

    // 月视图对齐日程
    useEffect(() => {
        if (monthPopoverVisible && selectedDateForPopover) {
            const fetchAndCalculate = async () => {
                setIsLoadingSlots(true);
                const startOfDay = selectedDateForPopover.startOf("day");
                const endOfDay = selectedDateForPopover.endOf("day");
                try {
                    const res = await querySchedule({
                        startTime: startOfDay.format("YYYY-MM-DD HH:mm:ss"),
                        endTime: endOfDay.format("YYYY-MM-DD HH:mm:ss"),
                        specificId: "",
                        targetEmpId: [userId],
                        period: "",
                    });

                    const businessStart = startOfDay.hour(8).minute(30);
                    const businessEnd = startOfDay.hour(20).minute(30);
                    const busySlots: { start: dayjs.Dayjs; end: dayjs.Dayjs }[] = [];

                    // 从响应对象中获取当前用户的日程数据
                    const userScheduleData = res[userId];
                    if (userScheduleData) {
                        // 处理 items 数据
                        if (Array.isArray(userScheduleData.items)) {
                            console.log("userScheduleData.items", userScheduleData.items);
                            const itemSlots = userScheduleData.items.map((e) => ({
                                start: dayjs(e.gmt_start),
                                end: dayjs(e.gmt_end),
                            }));
                            busySlots.push(...itemSlots);
                        }

                        // 处理 feishu_items 数据
                        if (Array.isArray(userScheduleData.feishu_items)) {
                            console.log("userScheduleData.feishu_items", userScheduleData.feishu_items);
                            const feishuSlots = userScheduleData.feishu_items.map((e) => ({
                                start: dayjs(e.gmtStart),
                                end: dayjs(e.gmtEnd),
                            }));
                            busySlots.push(...feishuSlots);
                        }

                        // 按开始时间排序
                        busySlots.sort((a, b) => a.start.diff(b.start));
                    }

                    const freeSlots: { start: dayjs.Dayjs; end: dayjs.Dayjs }[] = [];
                    let lastEndTime = businessStart;

                    busySlots.forEach((busy) => {
                        if (busy.start.isAfter(lastEndTime)) {
                            freeSlots.push({ start: lastEndTime, end: busy.start });
                        }
                        lastEndTime = dayjs.max(lastEndTime, busy.end);
                    });

                    if (businessEnd.isAfter(lastEndTime)) {
                        freeSlots.push({ start: lastEndTime, end: businessEnd });
                    }

                    const minDuration = 45;
                    const finalSlots: { start: dayjs.Dayjs; end: dayjs.Dayjs }[] = [];
                    freeSlots.forEach((free) => {
                        if (free.end.diff(free.start, "minutes") >= minDuration) {
                            let slotStart = free.start;
                            while (slotStart.add(minDuration, "minutes").isSameOrBefore(free.end)) {
                                finalSlots.push({ start: slotStart, end: slotStart.add(minDuration, "minutes") });
                                slotStart = slotStart.add(15, "minutes");
                            }
                        }
                    });

                    setAvailableSlots(finalSlots);
                } catch (error) {
                    messageApi.error("获取当天日程失败");
                    setAvailableSlots([]);
                } finally {
                    setIsLoadingSlots(false);
                }
            };
            fetchAndCalculate();
        }
    }, [monthPopoverVisible, selectedDateForPopover, querySchedule, userId]);

    // 获取日程
    const fetchAndSetEvents = useCallback(
        async (start: dayjs.Dayjs, end: dayjs.Dayjs) => {
            try {
                const res = await querySchedule({
                    startTime: start.format("YYYY-MM-DD HH:mm:ss"),
                    endTime: end.format("YYYY-MM-DD HH:mm:ss"),
                    specificId: "",
                    targetEmpId: [userId],
                    period: "",
                });

                // 从响应对象中获取当前用户的日程数据
                const userScheduleData = res[userId];
                if (userScheduleData) {
                    const formattedEvents: any[] = [];

                    // 处理 items 数据
                    if (Array.isArray(userScheduleData.items)) {
                        console.log("userScheduleData.items", userScheduleData.items);
                        const itemEvents = userScheduleData.items.map((event: any) => ({
                            id: event.id,
                            title: event.period === "A" ? "可面试" : "已安排面试",
                            start: event.gmt_start,
                            end: event.gmt_end,
                            backgroundColor:
                                event.period === "A" ? FreeTimeBackgroundColor : "rgba(187, 231, 229, 0.7)",
                            borderColor: event.period === "A" ? FreeTimeBorderColor : InterviewBorderColor,
                            textColor: event.period === "A" ? "rgba(17, 174, 65, 1)" : "#0082ce",
                            classNames: event.period === "A" ? ["event-period-a"] : ["event-period-b"],
                            // 仅允许“可面试(A)”日程被拖拽/调整
                            editable: event.period === "A",
                            durationEditable: event.period === "A",
                            extendedProps: {
                                period: event.period,
                                feishu_id: event.feishu_id,
                                source: "items", // 标记数据来源
                            },
                        }));
                        formattedEvents.push(...itemEvents);
                    }

                    // 处理 feishu_items 数据，以 period 为 "B" 的逻辑处理
                    if (Array.isArray(userScheduleData.feishu_items)) {
                        console.log("userScheduleData.feishu_items", userScheduleData.feishu_items);
                        const feishuEvents = userScheduleData.feishu_items.map((feishuEvent: any, index: number) => ({
                            id: `feishu_${index}_${Date.now()}`, // 生成唯一ID
                            title: feishuEvent.summary || "已安排面试", // 使用飞书日程的标题
                            start: feishuEvent.gmtStart,
                            end: feishuEvent.gmtEnd,
                            backgroundColor: "rgba(254,227,227,0.7)", // 使用 period B 的样式
                            // borderColor: "#f54d46",
                            textColor: "#b6514d",
                            classNames: ["event-period-c"],
                            // 飞书日程不允许拖拽/调整
                            editable: false,
                            durationEditable: false,
                            extendedProps: {
                                period: "B", // 设置为 B 类型
                                feishu_id: feishuEvent.feishuId, // 飞书日程可能没有 feishu_id
                                source: "feishu_items", // 标记数据来源
                                summary: feishuEvent.summary,
                                description: feishuEvent.description,
                                appLink: feishuEvent.appLink,
                                meetingLink: feishuEvent.meetingLink,
                            },
                        }));
                        formattedEvents.push(...feishuEvents);
                    }

                    setEvents(formattedEvents);
                } else {
                    // 如果没有找到当前用户的数据，设置为空数组
                    setEvents([]);
                }
            } catch (error) {
                messageApi.error("获取日程失败");
            }
        },
        [querySchedule, userId]
    );

    // // 初始化日程
    // useEffect(() => {
    //     const startWeek = dayjs().startOf("week");
    //     const endWeek = dayjs().endOf("week");
    //     fetchAndSetEvents(startWeek, endWeek);
    // }, [fetchAndSetEvents]);

    // 创建日程的通用逻辑
    const createEventFromSelection = useCallback(
        async (selectionInfo: SelectInfo) => {
            const calendarApi = selectionInfo.view.calendar;
            const start = dayjs(selectionInfo.start);
            const end = dayjs(selectionInfo.end);
            const gapStart = start.format("YYYY-MM-DD HH:mm:ss");
            const gapEnd = end.format("YYYY-MM-DD HH:mm:ss");

            // 显示加载提示，参数为0表示不需要自动关闭，而是手动关闭
            const hideLoading = messageApi.loading("正在创建日程...", 0);

            try {
                const startTimeStr = start.format("HH:mm:ss");
                let endTimeStr = end.format("HH:mm:ss");
                // 如果结束时间是00:00:00 并且不在同一天，说明是跨天到午夜，当作前一天的24:00:00处理
                if (endTimeStr === "00:00:00" && !end.isSame(start, "day")) {
                    endTimeStr = "24:00:00";
                }

                if (startTimeStr < "08:30:00" || endTimeStr > "20:30:00") {
                    calendarApi.unselect();
                    hideLoading();
                    return messageApi.warning("请在8:30-20:30之间创建日程");
                }

                const { saved: newEvent } = await createSchedule({ gapStart, gapEnd, reminderId: "" });

                calendarApi.addEvent({
                    id: newEvent.id,
                    title: "可面试",
                    start: newEvent.gmt_start,
                    end: newEvent.gmt_end,
                    backgroundColor: FreeTimeBackgroundColor,
                    borderColor: FreeTimeBorderColor,
                    classNames: ["event-period-a"],
                    // 仅允许“可面试(A)”日程被拖拽/调整，
                    editable: true,
                    durationEditable: true,
                });

                hideLoading();
                messageApi.success("日程创建成功");
                return true;
            } catch (error: any) {
                hideLoading();
                messageApi.error(error?.msg || "日程创建失败");
                return false;
            } finally {
                calendarApi.unselect();
            }
        },
        [createSchedule]
    );

    // 视图映射
    const viewMapping = {
        日: "timeGridDay",
        周: "timeGridWeek",
        月: "dayGridMonth",
    };

    // 自定义按钮点击处理
    const handleCustomButtonClick = useCallback((buttonName: string) => {
        const calendarApi = calendarRef.current?.getApi();
        if (!calendarApi) return;

        switch (buttonName) {
            case "today":
                calendarApi.today();
                break;
            case "prev":
                calendarApi.prev();
                break;
            case "next":
                calendarApi.next();
                break;
        }
    }, []);

    // 处理Segmented切换 日周月
    const handleViewChange = useCallback((value: string) => {
        const calendarApi = calendarRef.current?.getApi();
        if (!calendarApi) return;

        const viewName = viewMapping[value as keyof typeof viewMapping];
        if (viewName) {
            calendarApi.changeView(viewName);
            setCurrentView(value);
        }
    }, []);

    // 处理表单提交
    const handleFormSubmit = useCallback(async () => {
        if (!selectedInfo) return;

        const success = await createEventFromSelection(selectedInfo);
        if (success) {
            setSelectedInfo(null);
            form.resetFields();
        }
    }, [selectedInfo, form, createEventFromSelection]);

    // 处理表单取消
    const handleFormCancel = useCallback(() => {
        if (selectedInfo) {
            const calendarApi = selectedInfo.view.calendar;
            calendarApi.unselect();
        }

        setSelectedInfo(null);
        form.resetFields();
    }, [selectedInfo, form]);

    const isDateSelectionAllowed = (selectInfo: any) => {
        const startDate = dayjs(selectInfo.start).format("YYYY-MM-DD");
        // 1.如果可操作日期列表还未加载完毕，则禁止选择
        if (userOperableDates === null) {
            return false;
        }
        //2.如果可操作日期列表存在，检查选择的日期是否在其中
        if (userOperableDates.length > 0 && !userOperableDates.includes(startDate)) {
            return false;
        }
        // 3.检查是否在节假日内
        const selectionStart = dayjs(selectInfo.start);
        const selectionEnd = dayjs(selectInfo.end);
        for (const holiday of companyHolidays) {
            const holidayStart = dayjs(holiday.start);
            const holidayEnd = dayjs(holiday.end);
            if (selectionStart.isBefore(holidayEnd) && selectionEnd.isAfter(holidayStart)) {
                return false;
            }
        }

        return true;
    };

    //日期选择
    const handleDateSelect = useCallback(
        async (selectInfo: any) => {
            const calendarApi = selectInfo.view.calendar;
            const viewType = calendarApi.view.type;

            // 月视图进行创建事件，唤起弹窗小卡片
            if (viewType === "dayGridMonth") {
                if (!isDateSelectionAllowed(selectInfo)) {
                    messageApi.warning("请在允许的日期内创建日程");
                    calendarApi.unselect();
                    return;
                }

                const rect = selectInfo.jsEvent.target.getBoundingClientRect();
                setMonthPopoverPosition({ x: rect.left, y: rect.bottom });
                setSelectedDateForPopover(dayjs(selectInfo.start));
                setMonthPopoverVisible(true);
                calendarApi.unselect();
                return;
            }

            // 获取鼠标位置或选择区域的位置
            // const rect = selectInfo.jsEvent?.target?.getBoundingClientRect();
            // if (rect) {
            //     setPopoverPosition({
            //         x: rect.right,
            //         y: rect.top,
            //     });
            // }
            if (!isDateSelectionAllowed(selectInfo)) {
                messageApi.warning("请在允许的日期内创建日程");
                selectInfo.view.calendar.unselect();
                return;
            }

            const { start, end } = selectInfo;
            const durationMinutes = (end.getTime() - start.getTime()) / (1000 * 60);
            if (durationMinutes < 45) {
                messageApi.warning("日程时长不能少于45分钟");
                selectInfo.view.calendar.unselect();
                return;
            }

            await createEventFromSelection(selectInfo);
        },
        [isDateSelectionAllowed, createEventFromSelection]
    );

    // 处理日程事件修改
    const handleEventChange = useCallback(
        async (changeInfo: any) => {
            const { event } = changeInfo;

            const { start, end } = event;
            const durationMinutes = (end.getTime() - start.getTime()) / (1000 * 60);
            if (durationMinutes < 45) {
                messageApi.warning("日程时长不能少于45分钟");
                changeInfo.revert();
                return;
            }

            try {
                const updatedEvent = await updateSchedule({
                    id: event.id,
                    gapStart: dayjs(event.start).format("YYYY-MM-DD HH:mm:ss"),
                    gapEnd: dayjs(event.end).format("YYYY-MM-DD HH:mm:ss"),
                    reminderId: "",
                });
                messageApi.success("日程修改成功");
                setEvents((prevEvents) =>
                    prevEvents.map((e) => {
                        if (e.id === updatedEvent.saved.id) {
                            return { ...e, start: updatedEvent.saved.gmt_start, end: updatedEvent.saved.gmt_end };
                        }
                        return e;
                    })
                );
            } catch (error) {
                messageApi.error("修改日程失败");
                changeInfo.revert();
            }
        },
        [updateSchedule]
    );
    // Popover内容组件
    const PopoverContent = () => (
        <div style={{ width: 280 }}>
            <Form form={form} layout="vertical" onFinish={handleFormSubmit} initialValues={{ room: "A" }}>
                <Form.Item label="面试标题" name="title" rules={[{ required: true, message: "请输入面试日程标题" }]}>
                    <Input placeholder="请输入面试日程标题"></Input>
                </Form.Item>
                <Form.Item label="面试会议室" name="room" rules={[{ required: true, message: "请输入面试会议室" }]}>
                    <Select>
                        <Select.Option value="A">面试室A</Select.Option>
                        <Select.Option value="B">面试室B</Select.Option>
                    </Select>
                </Form.Item>
                <Form.Item>
                    <Space>
                        <Button type="primary" htmlType="submit">
                            确定
                        </Button>
                        <Button onClick={handleFormCancel}>取消</Button>
                    </Space>
                </Form.Item>
            </Form>
        </div>
    );

    // 月视图弹窗内容组件
    const MonthPopoverContent = () => (
        <div className={styles["month-popover-content"]}>
            {isLoadingSlots ? (
                <div className={styles["loading-container"]}>
                    <LoadingOutlined />
                    <span style={{ marginLeft: 8 }}>加载可用时间...</span>
                </div>
            ) : availableSlots.length > 0 ? (
                <div className={styles["slots-container"]}>
                    {availableSlots.map((slot, index) => (
                        <Button
                            key={index}
                            type="text"
                            className={styles["slot-button"]}
                            onClick={() => handleSlotClick(slot)}
                        >
                            {`${slot.start.format("HH:mm")} - ${slot.end.format("HH:mm")}`}
                        </Button>
                    ))}
                </div>
            ) : (
                <div className={styles["no-slots"]}>当天没有可预约的时间</div>
            )}
        </div>
    );

    // 月视图点击可用时间，创建事件
    const handleSlotClick = async (slot: { start: dayjs.Dayjs; end: dayjs.Dayjs }) => {
        const calendarApi = calendarRef.current?.getApi();
        if (!calendarApi) return;

        const selectInfo = {
            start: slot.start.toDate(),
            end: slot.end.toDate(),
            view: calendarApi.view,
        };

        // createEventFromSelection默认根据新建成功与否返回ture/false
        const isSuccess = await createEventFromSelection(selectInfo);
        if (isSuccess) {
            setMonthPopoverVisible(false);
        }
    };

    // 处理点击表格块
    const handleEventClick = useCallback((clickInfo: any) => {
        const { event } = clickInfo;
        // 只有 period 为 'B' 的事件才可能触发查看详情
        if (event.extendedProps.period === "B" && event.extendedProps.source === "feishu_items") {
            // 设置弹窗位置
            const rect = clickInfo.jsEvent.target.getBoundingClientRect();
            setDetailPopoverPosition({ x: rect.left, y: rect.bottom });
            setDetailPopoverVisible(true);

            // 直接从事件数据创建详情信息，统一处理所有事件
            setIsDetailLoading(false);
            const eventDetail: ViewScheduleDetailResponse = {
                deleted: false,
                createTime: "",
                modifiedTime: "",
                id: event.id || "",
                itrEmpId: "",
                gmtStart: event.start,
                gmtEnd: event.end,
                summary: event.extendedProps?.summary || event.title || "",
                description: event.extendedProps?.description || "",
                assginerEmpId: "",
                video: false,
                attendees: [],
                evalTempId: "",
                itvResult: "",
                hookCandidateNames: {},
                meetingLink: event.extendedProps?.meetingLink || "",
                appLink: event.extendedProps?.appLink || "",
                avatar: "",
                name: "",
            };
            setScheduleDetail(eventDetail);
        }
    }, []);

    // 处理删除事件
    const handleDeleteEvent = useCallback((clickInfo: any) => {
        modalApi.confirm({
            title: "提示",
            content: `确定删除「${dayjs(clickInfo.event.start).format("YYYY-MM-DD HH:mm:ss")} - ${dayjs(clickInfo.event.end).format("YYYY-MM-DD HH:mm:ss")}」的日程吗`,
            onOk: async () => {
                clickInfo.event.remove();
                await deleteSchedule({
                    id: clickInfo.event.id,
                    gapStart: clickInfo.event.start,
                    gapEnd: clickInfo.event.end,
                });
                messageApi.success("删除成功");
            },
        });
    }, []);

    // 事件内容
    const EventContent = (arg: any) => {
        // console.log("arg", arg);

        // 视图的类型
        const viewType = arg.view.type;

        // 计算事件持续时间（分钟）
        const startTime = new Date(arg.event.start!);
        const endTime = new Date(arg.event.end!);
        const durationMinutes = (endTime.getTime() - startTime.getTime()) / (1000 * 60);

        // 月视图展示
        const startTimeHour = arg.timeText;
        const monthEventContent = `${startTimeHour} ${arg.event.title}`;

        // console.log("arg.event", arg);

        if (viewType === "dayGridMonth") {
            return (
                <div className={styles["month-event-content"]}>
                    <div
                        className={styles["month-event-content-point"]}
                        style={{ backgroundColor: arg.textColor }}
                    ></div>
                    <div className={styles["month-event-content-text"]}>
                        <TextView text={monthEventContent}></TextView>
                    </div>
                </div>
            );
        }

        // 如果事件持续时间小于等于45分钟，则显示短事件
        if (durationMinutes < 45) {
            return (
                <div
                    className={`${styles["event-title"]} ${styles["event-title-short"]}`}
                    style={{ padding: "4px 8px" }}
                >
                    <div className={styles["event-title-header"]}>
                        <div className={styles["event-title-room"]} style={{ fontSize: "12px", fontWeight: "500" }}>
                            {arg.timeText}
                        </div>
                        <div className={styles["event-title-room"]} style={{ fontSize: "12px", fontWeight: "500" }}>
                            <TextView text={arg.event.title}></TextView>
                        </div>
                        <Button
                            color="default"
                            variant="link"
                            icon={<CloseOutlined />}
                            style={{ width: "14px", height: "14px", minWidth: "14px" }}
                            onClick={() => handleDeleteEvent(arg)}
                        ></Button>
                    </div>
                </div>
            );
        }

        return (
            <div className={styles["event-title"]}>
                <div className={styles["event-title-header"]}>
                    <div className={styles["event-title-room"]}>{arg.timeText}</div>
                    {arg.event.classNames[0] === "event-period-a" && (
                        <Button
                            color="default"
                            variant="link"
                            icon={<CloseOutlined />}
                            style={{ width: "16px", height: "16px" }}
                            onClick={() => handleDeleteEvent(arg)}
                        ></Button>
                    )}
                </div>
                <div className={styles["event-title-text"]}>
                    <TextView text={arg.event.title} />
                </div>
            </div>
        );
    };

    const DayCellContent = (arg: any) => {
        const viewType = arg.view.type;
        if (viewType === "dayGridMonth") {
            const day = arg.date.getDate();
            // return <div className={styles["month-day-number"]}>{`${day}`}</div>;
            const classNames = [styles["month-day-number"]];
            if (arg.isToday) {
                classNames.push(styles["month-today-highlight"]);
            }
            return <div className={classNames.join(" ")}>{`${day}`}</div>;
        }
        return arg.dayNumberText;
    };

    const handleDateChange: DatePickerProps["onChange"] = (date) => {
        const calendarApi = calendarRef.current?.getApi();
        if (!calendarApi) return;
        if (date) {
            calendarApi.gotoDate(date.toDate());
            setCurrentDate(date);
        }
        // 点击日期选择器删除按钮的时候触发回到今天
        else {
            calendarApi.today();
            setCurrentDate(dayjs());
        }
    };

    const viewType = viewMapping[currentView as keyof typeof viewMapping];

    // 日期选择器在不同的视图的显示格式
    const DateFormat = (value: dayjs.Dayjs) => {
        if (!value) {
            return "";
        }
        const year = value.year();

        if (viewType === "timeGridWeek") {
            const startOfWeek = value.startOf("week");
            const endOfWeek = value.endOf("week");
            const startYear = startOfWeek.year();
            const startMonth = startOfWeek.month() + 1;
            const endMonth = endOfWeek.month() + 1;
            const startDate = startOfWeek.date();

            const endDate = endOfWeek.date();
            const endYear = endOfWeek.year();
            if (startYear !== endYear) {
                return `${startYear}年${startMonth}月${startDate}日 - ${endYear}年${endMonth}月${endDate}日`;
            }
            if (startMonth !== endMonth) {
                return `${startYear}年${startMonth}月${startDate}日 - ${endMonth}月${endDate}日`;
            }
            return `${startYear}年${startMonth}月${startDate}日 - ${endDate}日`;
        }
        const month = value.month() + 1;
        if (viewType === "timeGridDay") {
            const date = value.date();
            return `${year}年${month}月${date}日`;
        }
        return `${year}年${month}月`;
    };

    // 自定义视图头部
    const DayHeaderContent = (arg: any) => {
        {
            const view = arg.view.type;
            if (view === "dayGridMonth") {
                const dayOfWeek = new Date(arg.date).toLocaleDateString("zh-CN", { weekday: "short" });
                return <div className={styles["calendar-day-header-date"]}>{dayOfWeek}</div>;
            }
            // 获取今天的日期（不含时间）
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            // 获取单元格的日期（不含时间）
            const cellDate = new Date(arg.date);
            cellDate.setHours(0, 0, 0, 0);

            let textColor = "rgba(0, 0, 0, 0.6)"; // 未来日期默认为黑色
            if (cellDate.getTime() < today.getTime()) {
                textColor = "rgba(0, 0, 0, 0.26)"; // 过去的日期为灰色
            } else if (cellDate.getTime() === today.getTime()) {
                textColor = "rgba(0, 153, 242, 1)"; // 当天为蓝色
            }

            const dayOfWeek = cellDate.toLocaleDateString("zh-CN", { weekday: "short" });
            const month = cellDate.getMonth() + 1;
            const date = cellDate.getDate();
            const formattedDate = `${month}月${date}日`;
            const formttedWeek = `${dayOfWeek} `;

            return (
                <div style={{ color: textColor }}>
                    <span className={styles["calendar-day-header-week"]}>{formttedWeek}</span>
                    <span className={styles["calendar-day-header-date"]}>{formattedDate}</span>
                </div>
            );
        }
    };

    // 日程详情Popover内容
    const ScheduleDetailPopoverContent = () => {
        if (isDetailLoading) {
            return (
                <div className={styles["loading-container"]}>
                    <LoadingOutlined />
                    <span style={{ marginLeft: 8 }}>正在加载详情...</span>
                </div>
            );
        }
        if (!scheduleDetail) {
            return <div className={styles["no-slots"]}>暂无详情</div>;
        }

        // 判断是否为简化的详情信息（通过检查关键字段是否为空来判断）
        // const isSimpleDetail = !scheduleDetail.id && !scheduleDetail.assginerEmpId;

        return (
            <div style={{ width: 320 }}>
                <p>
                    <strong>标题:</strong> {scheduleDetail.summary}
                </p>
                {/* {scheduleDetail.description && (
                    <p>
                        <strong>描述:</strong> {scheduleDetail.description}
                    </p>
                )} */}
                <p>
                    <strong>时间:</strong> {dayjs(scheduleDetail.gmtStart).format("YYYY-MM-DD HH:mm")} -{" "}
                    {dayjs(scheduleDetail.gmtEnd).format("HH:mm")}
                </p>

                {/* 只有完整详情才显示以下信息 */}
                {/* {!isSimpleDetail && ( */}
                {/* <>
                    <p>
                        <strong>HR联系人:</strong>
                        <Avatar src={scheduleDetail.avatar} />
                        {scheduleDetail.name}-{scheduleDetail.assginerEmpId}
                    </p>
                    <p>
                        <strong>候选人:</strong> {Object.values(scheduleDetail.hookCandidateNames ?? {}).join(", ")}
                    </p>
                    <p>
                        <strong>面试状态:</strong>{" "}
                        {scheduleDetail.itvResult === InterviewStatus.NotStart
                            ? "未开始"
                            : scheduleDetail.itvResult === InterviewStatus.Completed
                              ? "已完成"
                              : "未评价"}
                    </p>
                </> */}
                {/* )} */}

                {scheduleDetail.meetingLink && (
                    <p>
                        <strong>会议链接:</strong>
                        <a href={scheduleDetail.meetingLink} target="_blank" rel="noopener noreferrer">
                            点击加入
                        </a>
                    </p>
                )}

                {scheduleDetail.appLink && (
                    <p>
                        <strong>飞书链接:</strong>
                        <a href={scheduleDetail.appLink} target="_blank" rel="noopener noreferrer">
                            跳转飞书
                        </a>
                    </p>
                )}
            </div>
        );
    };

    return (
        <div className={`${styles.calendar} ${className || ""}`}>
            {contextHolder}
            {modalHolder}
            {/* 自定义头部工具栏 */}
            <div className={styles["calendar-header"]}>
                <div className={styles["calendar-header-left"]}>
                    <Button onClick={() => handleCustomButtonClick("today")}>今天</Button>
                    <Button onClick={() => handleCustomButtonClick("prev")} icon={<LeftOutlined />}></Button>
                    <Button onClick={() => handleCustomButtonClick("next")} icon={<RightOutlined />}></Button>

                    <DatePicker
                        value={currentDate}
                        picker={viewType === "dayGridMonth" ? "month" : viewType === "timeGridWeek" ? "week" : "date"}
                        format={DateFormat}
                        showWeek={false}
                        variant="borderless"
                        suffixIcon={<DownOutlined />}
                        onChange={handleDateChange}
                        style={{ width: viewType === "timeGridWeek" ? 260 : viewType === "timeGridDay" ? 180 : 140 }}
                        className={styles["calendar-date-picker"]}
                    />
                </div>
                <Alert
                    message="仅支持选择明天之后的 6 个工作日（含周六，不含周日），每日可预约时段为 8:30-20:30，每个日程需至少 45 分钟"
                    type="info"
                    showIcon
                />
                <Segmented options={["日", "周", "月"]} value={currentView} onChange={handleViewChange} />
            </div>

            <Popover
                content={<PopoverContent />}
                title="添加面试安排"
                open={popoverVisible}
                onOpenChange={(visible) => {
                    if (!visible) {
                        handleFormCancel();
                    }
                }}
                trigger="click"
                placement="right"
            >
                <div
                    style={{
                        position: "absolute",
                        left: popoverPosition.x,
                        top: popoverPosition.y,
                        pointerEvents: "none",
                    }}
                ></div>
            </Popover>

            <Popover
                content={<MonthPopoverContent />}
                title={selectedDateForPopover ? `${selectedDateForPopover.format("YYYY-MM-DD")} 可用时间` : "可用时间"}
                open={monthPopoverVisible}
                onOpenChange={setMonthPopoverVisible}
                trigger="click"
                placement="bottomLeft"
                openClassName={styles["month-popover"]}
            >
                <div
                    style={{
                        position: "absolute",
                        left: monthPopoverPosition.x,
                        top: monthPopoverPosition.y,
                        pointerEvents: "none",
                    }}
                ></div>
            </Popover>

            <Popover
                content={<ScheduleDetailPopoverContent />}
                title="面试日程详情"
                open={detailPopoverVisible}
                onOpenChange={setDetailPopoverVisible}
                trigger="click"
                placement="bottomLeft"
            >
                <div
                    style={{
                        position: "absolute",
                        left: detailPopoverPosition.x,
                        top: detailPopoverPosition.y,
                        pointerEvents: "none",
                    }}
                ></div>
            </Popover>

            <FullCalendar
                ref={calendarRef}
                plugins={[dayGridPlugin, interactionPlugin, timeGridPlugin]}
                // 不显示头部工具栏
                headerToolbar={false}
                // 初始视图
                initialView="timeGridWeek"
                // 实时指示当前在什么时候（红色线跟红色箭头）
                nowIndicator={true}
                editable={true}
                selectable={true}
                // 拖动时是否绘制一个占位符事件
                selectMirror={true}
                dayMaxEvents={true}
                weekends={true}
                // 设置默认事件颜色为绿色
                eventColor={FreeTimeBorderColor}
                eventBorderColor={FreeTimeBorderColor}
                eventBackgroundColor={FreeTimeBackgroundColor}
                select={handleDateSelect}
                selectAllow={isDateSelectionAllowed}
                eventClick={handleEventClick}
                // 拖动事件改变，触发修改事件
                eventDrop={handleEventChange}
                eventResize={handleEventChange}
                handleWindowResize={true}
                eventContent={(arg) => {
                    return <EventContent {...arg} />;
                }}
                dayCellContent={(arg) => {
                    return <DayCellContent {...arg} />;
                }}
                datesSet={(arg) => {
                    const middleDate = new Date(arg.start.getTime() + (arg.end.getTime() - arg.start.getTime()) / 2);
                    setCurrentDate(dayjs(middleDate));
                    fetchAndSetEvents(dayjs(arg.start), dayjs(arg.end));
                }}
                dayHeaderContent={(arg) => <DayHeaderContent {...arg} />}
                events={[...events, ...companyHolidays]}
                locale="zh-cn"
                height="100%"
                // contentHeight="auto"
                // aspectRatio={1.8}
                slotMinTime="08:00:00"
                slotMaxTime="21:00:00"
                expandRows={false}
                // businessHours={{
                //     daysOfWeek: [1, 2, 3, 4, 5],
                //     startTime: "08:30",
                //     endTime: "18:00",
                // }}
                slotLabelContent={(arg) => {
                    // 更改第一列y轴时间格式
                    const time = new Date(arg.date);
                    const hours = time.getHours();
                    const minutes = time.getMinutes();
                    const formattedTime = `${hours}:${minutes.toString().padStart(2, "0")}`;
                    return <div className={styles["slot-label"]}>{formattedTime}</div>;
                }}
                slotLabelInterval={"00:30:00"}
                slotDuration={"00:15:00"}
                allDaySlot={false}
                unselectAuto={false}
                // 月视图当事件过多时显示的跳转内容
                moreLinkContent={(arg) => {
                    return <div className={styles["more-link-content"]}>还有 {arg.num} 项</div>;
                }}
                dayPopoverFormat={{ weekday: "long" }}
                // handleWindowResize={false}
            />
        </div>
    );
};

export default Calendar;
