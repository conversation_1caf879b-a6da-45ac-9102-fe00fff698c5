import React from "react";
import { Pagination as AntPagination, Select } from "antd";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";

/** Pagination组件属性接口 */
interface PaginationProps {
    /** 当前页码 */
    current: number;
    /** 每页条数 */
    pageSize: number;
    /** 总条数 */
    total: number;
    /** 是否显示每页条数选择器 */
    showSizeChanger?: boolean;
    /** 是否显示快速跳转 */
    showQuickJumper?: boolean;
    /** 是否显示总数 */
    showTotal?: boolean;
    /** 每页条数选项 */
    pageSizeOptions?: string[];
    /** 页码变化回调 */
    onChange?: (page: number, pageSize: number) => void;
    /** 每页条数变化回调 */
    onShowSizeChange?: (current: number, size: number) => void;
}

/**
 * 分页组件
 * @param props Pagination组件属性
 * @returns Pagination组件
 */
const Pagination: React.FC<PaginationProps> = ({
    current,
    pageSize,
    total,
    showSizeChanger = true,
    showQuickJumper = true,
    showTotal = true,
    pageSizeOptions = ["10", "20", "50", "100"],
    onChange,
    onShowSizeChange,
}) => {
    /**
     * 处理页码变化
     * @param page 页码
     * @param size 每页条数
     */
    const handleChange = (page: number, size: number) => {
        onChange?.(page, size);
    };

    /**
     * 处理每页条数变化
     * @param current 当前页码
     * @param size 每页条数
     */
    const handleShowSizeChange = (current: number, size: number) => {
        onShowSizeChange?.(current, size);
        onChange?.(1, size); // 切换每页条数时回到第一页
    };

    /**
     * 自定义总数显示
     * @param total 总条数
     * @param range 当前页范围
     * @returns 总数显示文本
     */
    const showTotalText = (total: number) => {
        return <span className={styles["pagination-total"]}>共 {total} 条</span>;
    };

    // 计算总页数
    const totalPages = Math.ceil(total / pageSize);

    // 如果总数为0或只有一页，不显示分页
    if (total === 0 || totalPages <= 1) {
        return null;
    }

    return (
        <div className={styles["pagination-wrapper"]}>
            <AntPagination
                current={current}
                pageSize={pageSize}
                total={total}
                showSizeChanger={showSizeChanger}
                showQuickJumper={showQuickJumper}
                showTotal={showTotal ? showTotalText : undefined}
                pageSizeOptions={pageSizeOptions}
                onChange={handleChange}
                onShowSizeChange={handleShowSizeChange}
                className={styles["pagination"]}
            />
        </div>
    );
};

export default Pagination;
export type { PaginationProps };
