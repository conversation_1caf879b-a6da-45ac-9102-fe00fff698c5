import React, { forwardRef, useImper<PERSON><PERSON><PERSON><PERSON>, use<PERSON>emo, useState } from "react";
import { Table, Steps } from "antd";
import styles from "./index.module.scss";
import {
    FlowDetailExposeHandle,
    FlowDetailResp,
    FlowItemParams,
    FlowNodeDefinition,
    Job,
    JobFlowReq,
    JobFormReq,
    NodeInstance,
    TagsItem,
    TagsResp,
} from "@/app/store/modules/dashboard/jobFlow";
import {
    getAllTagsApi,
    getFlowDetailApi,
    getJobFlowChildrenByIdApi,
    getJobFlowDetailApi,
} from "@/app/request/modules/dashboard";
import { RespParams } from "@/app/typing";
import { JobTags, TaskTypeList } from "../constant";
import { cloneDeep } from "lodash-es";
import JobDetail from "../JobDetail";
import { getJobListApi } from "@/app/request/modules/common";
import { TODO_STATUS } from "@/app/constant";

interface FlowStep {
    title: string;
    description?: string;
    status?: "wait" | "process" | "finish" | "error";
}

interface FlowRecord {
    key: string;
    time: string;
    node: string;
    operator: string;
    action: string;
    remark: string;
    result: string;
}

const FlowDetail = forwardRef<
    FlowDetailExposeHandle,
    {
        flowId: string;
        type?: string;
        mode?: string;
        jobId?: number;
        handleChangeLoading?: (val: boolean) => void;
        handleShowFooter?: (val: boolean) => void;
        handleShowRejectBtn?: (val: boolean) => void;
    }
>((props, ref) => {
    const { flowId, type, mode, jobId, handleChangeLoading, handleShowFooter, handleShowRejectBtn } = props;
    const [currentFlow, setCurrentFlow] = useState<NodeInstance | null>(null);
    const [currentJobForm, setCurrentJobForm] = useState<JobFormReq | null>(null);

    useImperativeHandle(ref, () => ({
        currentFlow: currentFlow,
        currentJobForm: currentJobForm,
        isEditMode: isEditMode,
        refresh: () => {
            setFlowSteps([]);
            setDataSource([]);
            setFormList([]);
            if (flowId) {
                getFlowDetail(flowId);
                return;
            }
            if (jobId) {
                getFlowFormData(String(jobId), 1);
            }
        },
    }));

    //获取流程详情
    const getFlowDetail = async (id: string) => {
        try {
            const res: RespParams<FlowDetailResp> = await (await getFlowDetailApi(id)).json();
            // 当前流程是否可操作
            handleShowFooter?.(res.data.status === 1);
            // 处理流程步骤
            // const currentNode = res.data.currentNodeInstance?.nodeId;
            setCurrentFlow(res.data.currentNodeInstance);
            handleShowRejectBtn?.(res.data.currentNodeInstance?.nodeDefinition.fallbackNodeId !== -1);
            const flowList = res.data.flowNodeDefinitions?.map((item: FlowNodeDefinition) => {
                let currentStatus = "wait";
                // 从后往前查找，
                const current = res.data.nodeInstanceList.findLast(
                    (item2) => item2.nodeId === item.id && !item2?.parentNodeInstanceId
                );
                if (current) {
                    if (Object.values(TODO_STATUS).includes(current.status)) {
                        switch (current.status) {
                            case TODO_STATUS.process:
                                currentStatus = "process";
                                break;
                            case TODO_STATUS.done:
                                currentStatus = "finish";
                                break;
                            case TODO_STATUS.close:
                                currentStatus = "error";
                                break;
                            default:
                                currentStatus = "wait";
                                break;
                        }
                    }
                }

                return {
                    title: item.name,
                    description: item?.description ?? "默认描述",
                    status: currentStatus,
                };
            });

            setFlowSteps(flowList as FlowStep[]);

            // 处理审批记录
            const flowRecordList = res.data.nodeInstanceList?.map((item: any, index: number) => {
                let comment = item.comment;
                if (item.status === 3 && res.data.nodeInstanceList.length > index + 1) {
                    comment += `（转办人：${
                        res.data.nodeInstanceList[index + 1].approver
                            ? `${res.data.nodeInstanceList[index + 1].approver?.emp_id}-${
                                  res.data.nodeInstanceList[index + 1].approver?.emp_name
                              }`
                            : "暂无"
                    }）`;
                }

                return {
                    id: item.id,
                    key: item.id,
                    time: item.startedAt,
                    node: item.nodeDefinition?.name,
                    operator: item.approver ? `${item.approver?.emp_id}-${item.approver?.emp_name}` : "暂无",
                    action: item.statusStr,
                    remark: comment,
                    result: "",
                    parentNodeInstanceId: item?.parentNodeInstanceId ?? "",
                    nodeTypeStr: item.nodeDefinition?.nodeTypeStr,
                };
            });

            // 构建树形
            const buildTree = (items: any[]) => {
                const itemMap = new Map();
                items.forEach((item) => {
                    itemMap.set(item.id, { ...item, children: [] });
                });

                // 构建树
                const rootItems: any[] = [];

                items.forEach((item) => {
                    const mappedItem = itemMap.get(item.id);

                    if (item.parentNodeInstanceId && itemMap.has(item.parentNodeInstanceId)) {
                        // 如果有父节点，将此项添加到父节点的 children 中
                        const parent = itemMap.get(item.parentNodeInstanceId);
                        parent.children.push(mappedItem);
                    } else {
                        // 如果没有父节点或父节点不存在，则为根节点
                        rootItems.push(mappedItem);
                    }
                });

                // 清理空的 children 数组
                const cleanEmptyChildren = (node: any) => {
                    if (node.children.length === 0) {
                        delete node.children;
                    } else {
                        node.children.forEach(cleanEmptyChildren);
                    }
                    return node;
                };

                return rootItems.map(cleanEmptyChildren);
            };

            const treeData = flowRecordList ? buildTree(flowRecordList) : [];
            setDataSource(treeData);
            setCurrentJobForm({
                flowId: res.data.flowId,
                relatedForm: res.data.relatedForm,
            });
            if (TaskTypeList.map((item) => item.key).includes(type ?? "")) {
                getFlowData(res?.data?.relatedForm, res.data.flowId);
            }
        } catch (error) {
            console.log(error);
        }
    };

    // 审批记录表格字段
    const columns = [
        { title: "时间", dataIndex: "time", key: "time", width: 200, ellipsis: true },
        { title: "节点名称", dataIndex: "node", key: "node", width: 180 },
        {
            title: "节点类型",
            dataIndex: "nodeTypeStr",
            key: "nodeTypeStr",
            width: 120,
        },
        { title: "审批人", dataIndex: "operator", key: "operator", width: 150 },
        {
            title: "审批状态",
            dataIndex: "action",
            key: "action",
            width: 120,
        },
        { title: "处理意见", dataIndex: "remark", key: "remark", width: 200 },
    ];

    // 审批流程步骤数据
    const [flowSteps, setFlowSteps] = useState<FlowStep[]>([]);
    // 审批记录表格数据
    const [dataSource, setDataSource] = useState<FlowRecord[]>([]);
    // 表单数据
    const [formList, setFormList] = useState<JobFlowReq[]>([]);
    // 验证需求表单是否为编辑模式
    const [isEditMode, setIsEditMode] = useState<boolean>(false);

    const isAllowEdit = useMemo(() => {
        if (mode) {
            return true;
        }

        return currentFlow?.nodeDefinition.fallbackNodeId === -1 && type === "JobFlowTodo";
    }, [currentFlow, mode, type]);

    let tagsList: TagsResp[] = [];

    const getFlowFormData = async (formId: string, flowType: number) => {
        const res: RespParams<JobFlowReq> = await (
            await (flowType === 1 ? getJobFlowDetailApi(formId) : getJobFlowChildrenByIdApi(Number(formId)))
        ).json();
        if (res.code === 200) {
            tagsList = [];
            let list: JobFlowReq[] = [];
            list.push(res.data);
            if (res.data?.tags) {
                const res2: RespParams<TagsResp[]> = await (await getAllTagsApi({ layer: "", label: "" })).json();
                if (res2.code === 200) {
                    tagsList = res2.data;
                }
            }

            list = list.map((item) => {
                item.orgId = Array.isArray(item.orgId) ? item.orgId : item.orgId.split("/");
                item.jobNameInnerPath = Array.isArray(item.jobNameInnerPath)
                    ? item.jobNameInnerPath
                    : item.jobNameInnerPath.split("/");
                item.educational = Array.isArray(item.educational) ? item.educational : item.educational.split("/");
                item.jobLocation = Array.isArray(item.jobLocation) ? item.jobLocation : item.jobLocation.split("/");

                item.jobTags = cloneDeep(JobTags);

                const tagsDetail: TagsResp[] = [];
                item.tags?.forEach((item2: number) => {
                    const tagItem = tagsList.find((item3) => item3.id === item2);

                    if (tagItem) {
                        tagsDetail.push(tagItem);
                    }
                });

                tagsDetail.forEach((item2) => {
                    const groupIndex = item.jobTags?.findIndex((item3: TagsItem) => item3.label === item2.layer) ?? -1;
                    if (groupIndex !== -1) {
                        const tagIndex =
                            item.jobTags?.[groupIndex]?.children?.findIndex(
                                (item3: any) => item3.label === item2.label
                            ) ?? -1;

                        if (tagIndex !== -1) {
                            item.jobTags?.[groupIndex]?.children?.[tagIndex]?.value?.push(item2);
                        }
                    }
                });

                item.editMode = false;

                if (item.jobSpecs && item.jobSpecs.length > 0) {
                    item.jobSpecs.forEach((item2: FlowItemParams) => {
                        item2.assignment = parseInt((item2 as any).proportionValues);
                    });
                }

                return item;
            });
            const promiseList = list.map(async (item: JobFlowReq) => {
                const orgNo = item.orgId?.[item.orgId?.length - 1];

                const res: RespParams<Job[]> = await (await getJobListApi({ orgNo })).json();

                return res;
            });

            Promise.all(promiseList).then((res) => {
                const newList = list.map((item: JobFlowReq, index: number) => {
                    item.jobList = res[index].data;

                    return item;
                });

                setFormList((prev) => [...prev, ...newList]);
            });
        }
    };

    const getFlowData = async (relatedForm: string, flowType: number) => {
        setFormList([]);
        const formIdList = relatedForm.split(",");
        if (Array.isArray(formIdList) && formIdList.length > 0) {
            formIdList.forEach((item) => {
                getFlowFormData(item, flowType);
            });
        }
    };

    return (
        <div className={styles["flow-history-detail-wrapper"]}>
            {flowSteps.length > 0 && (
                <div
                    className={styles["flow-history-steps"]}
                    style={{ background: "#fff", padding: 24, borderRadius: 8, marginBottom: 16 }}
                >
                    <Steps
                        current={2}
                        status="process"
                        size="small"
                        labelPlacement="vertical"
                        items={flowSteps.map((step) => ({
                            title: step.title,
                            description: step.description,
                            status: step.status,
                        }))}
                    />
                </div>
            )}
            {TaskTypeList.map((item) => item.key).includes(type ?? "") && (
                <JobDetail
                    isAllowEdit={isAllowEdit}
                    detail={formList}
                    mode={mode}
                    handleFormLoading={(val) => handleChangeLoading?.(val)}
                    handleChangeFormList={(list) => {
                        const isEdit = list.some((item) => item.editMode);
                        setIsEditMode(isEdit);
                    }}
                    refresh={() => getFlowDetail(flowId)}
                />
            )}

            <Table tableLayout="auto" columns={columns} dataSource={dataSource} pagination={false} />
        </div>
    );
});

FlowDetail.displayName = "FlowDetail";
export default FlowDetail;
