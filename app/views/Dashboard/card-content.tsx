import React from "react";
import Icon from "@ant-design/icons";
import { Divider, Select, Button, Tooltip } from "antd";
import DownloadIcon from "../../icons/dashboard/download.svg";
import WarningIcon from "../../icons/dashboard/warning.svg";
import styles from "./index.module.scss";
import clsx from "clsx";
import * as echarts from "echarts";
import { useEffect, useRef } from "react";

const TopContent = () => {
    return (
        <div className={styles["top-content"]}>
            <div className={clsx(styles["top-content-item"], styles["top-content-item-left"])}></div>
            <div className={clsx(styles["top-content-item"], styles["top-content-item-center"])}></div>
            <div className={clsx(styles["top-content-item"], styles["top-content-item-right"])}></div>
        </div>
    );
};

const BottomContent = () => {
    const barChartRef = useRef<HTMLDivElement>(null);
    const pieChartRef = useRef<HTMLDivElement>(null);
    const barContainerRef = useRef<HTMLDivElement>(null);
    const pieContainerRef = useRef<HTMLDivElement>(null);
    const barChartInstance = useRef<echarts.ECharts>();
    const pieChartInstance = useRef<echarts.ECharts>();

    useEffect(() => {
        // 岗位投递分析数据
        const jobData = {
            positions: ["高级工程师", "Java开发工程师", "后端工程师", "资深工程师", "全栈工程师", "开发专家"],
            values: [3.0, 2.6, 3.2, 2.8, 3.1, 4],
        };

        const barOptions = {
            tooltip: {
                trigger: "axis",
                axisPointer: {
                    type: "shadow",
                },
            },
            legend: {
                data: ["数量"],
                left: "left",
                top: "top",
                itemWidth: 10,
                itemHeight: 10,
                textStyle: {
                    color: "#767C84",
                    fontSize: 12,
                },
            },
            grid: {
                left: "3%",
                right: "4%",
                bottom: "3%",
                top: "30px",
                containLabel: true,
            },
            xAxis: [
                {
                    type: "category",
                    data: jobData.positions,
                    axisTick: {
                        alignWithLabel: true,
                    },
                    axisLine: {
                        lineStyle: {
                            color: "#A0A4AA",
                        },
                    },
                },
            ],
            yAxis: [
                {
                    type: "value",
                    name: "数量",
                    nameLocation: "middle",
                    nameGap: 30,
                    nameRotate: 90,
                    nameTextStyle: {
                        color: "#999",
                        fontSize: 12,
                    },
                    min: 0,
                    max: 4000,
                    interval: 2000,
                    axisLabel: {
                        formatter: function (value: number) {
                            if (value === 0) return "0";
                            return value / 1000 + "k";
                        },
                    },
                },
            ],
            series: [
                {
                    name: "数量",
                    type: "bar",
                    barWidth: "10px",
                    itemStyle: {
                        color: "#43ADFF",
                        borderRadius: [4, 4, 0, 0],
                    },
                    data: jobData.values.map((v) => v * 1000), // 将数据乘以1000，以匹配k单位
                },
            ],
        };

        // 各部门招聘情况数据
        const departmentData = [
            { value: 121, name: "行政部门" },
            { value: 65, name: "运营部" },
            { value: 25, name: "技术部" },
            { value: 45, name: "市场部" },
            { value: 25, name: "产品部" },
        ];

        const pieOptions = {
            tooltip: {
                trigger: "item",
                formatter: "{a} <br/>{b}: {c} ({d}%)",
            },
            legend: {
                orient: "vertical",
                top: "center",
                textStyle: {
                    color: "#767C84",
                    fontSize: 12,
                },
                itemWidth: 10,
                itemHeight: 10,
                formatter: function (name: string) {
                    const item = departmentData.find((item) => item.name === name);
                    return `${name}  ${item ? item.value : ""}`;
                },
            },
            series: [
                {
                    name: "招聘数",
                    type: "pie",
                    radius: ["65%", "80%"],
                    center: ["25%", "50%"],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 0,
                        borderColor: "#fff",
                        borderWidth: 1,
                    },
                    label: {
                        show: true,
                        position: "center",
                        formatter: "{total|321}\n{subtitle|总招聘数}",
                        rich: {
                            total: {
                                fontSize: 24,
                                fontWeight: 600,
                                color: "#333",
                                padding: [0, 0, 5, 0],
                            },
                            subtitle: {
                                fontSize: 12,
                                color: "#767C84",
                            },
                        },
                        color: "#333",
                        fontWeight: 600,
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 16,
                            fontWeight: "bold",
                        },
                    },
                    labelLine: {
                        show: false,
                    },
                    data: departmentData,
                    color: ["#5B8FF9", "#8B69FF", "#F0AF4E", "#7ADF4A", "#9661BC"],
                },
            ],
        };

        const timer1 = setTimeout(() => {
            if (barChartRef.current) {
                barChartInstance.current = echarts.init(barChartRef.current);
                barChartInstance.current.setOption(barOptions);
            }
        }, 350);

        const timer2 = setTimeout(() => {
            if (pieChartRef.current) {
                pieChartInstance.current = echarts.init(pieChartRef.current);
                pieChartInstance.current.setOption(pieOptions);

                // 监听饼图点击
                pieChartInstance.current?.on("click", (params) => {
                    console.log(params);
                });
            }
        }, 350);

        // 创建一个resizeObserver实例监听容器大小变化
        const resizeObserver = new ResizeObserver((entries) => {
            for (const entry of entries) {
                if (entry.target === barContainerRef.current) {
                    barChartInstance.current?.resize();
                } else if (entry.target === pieContainerRef.current) {
                    pieChartInstance.current?.resize();
                }
            }
        });

        if (barContainerRef.current) {
            resizeObserver.observe(barContainerRef.current);
        }
        if (pieContainerRef.current) {
            resizeObserver.observe(pieContainerRef.current);
        }

        // 窗口大小变化，触发图表重绘
        const handleWindowResize = () => {
            if (barChartInstance.current) {
                barChartInstance.current.resize();
            }
            if (pieChartInstance.current) {
                pieChartInstance.current.resize();
            }
        };

        window.addEventListener("resize", handleWindowResize);

        return () => {
            clearTimeout(timer1);
            clearTimeout(timer2);

            // 移除resize事件监听
            window.removeEventListener("resize", handleWindowResize);

            // 移除resizeObserver
            resizeObserver.disconnect();

            if (barChartInstance.current) {
                barChartInstance.current.dispose();
            }
            if (pieChartInstance.current) {
                pieChartInstance.current?.off("click");

                pieChartInstance.current.dispose();
            }
        };
    }, []);

    return (
        <div className={styles["bottom-content"]}>
            <div
                className={clsx(styles["bottom-content-item"], styles["bottom-content-item-left"])}
                ref={barContainerRef}
            >
                <div className={styles["bottom-content-item-left-title"]}>
                    <div className={styles["text-title"]}>岗位投递分析</div>
                    <div className={styles["bottom-content-item-left-title-right"]}>
                        <Select style={{ width: 151 }} placeholder="选择岗位">
                            <Select.Option value="1">运营类</Select.Option>
                            <Select.Option value="2">开发类</Select.Option>
                            <Select.Option value="3">产品类</Select.Option>
                        </Select>
                        <Divider type="vertical" />
                        <Tooltip title="导出">
                            <Button>
                                <Icon component={() => <DownloadIcon />} size={16}></Icon>
                            </Button>
                        </Tooltip>
                    </div>
                </div>
                <div ref={barChartRef} style={{ height: "calc(100% - 50px)", width: "100%" }}></div>
            </div>
            <div
                className={clsx(styles["bottom-content-item"], styles["bottom-content-item-right"])}
                ref={pieContainerRef}
            >
                <div className={styles["bottom-content-item-right-title"]}>
                    <div className={styles["text-title"]}>各部门招聘情况</div>
                    <div className={styles["bottom-content-item-right-title-right"]}>
                        <div className={styles["warning-text"]}>
                            <Icon component={() => <WarningIcon />} size={24}></Icon>
                            <div>点击详情查看部门维度情况</div>
                        </div>
                        <Divider type="vertical" />
                        <Tooltip title="导出">
                            <Button className={styles["export-button"]}>
                                <Icon component={() => <DownloadIcon />} size={16}></Icon>
                            </Button>
                        </Tooltip>
                    </div>
                </div>
                <div ref={pieChartRef} style={{ height: "calc(100% - 20px)", width: "100%" }}></div>
            </div>
        </div>
    );
};

export function CardContent() {
    return (
        <div className={styles["card-content"]}>
            <TopContent />
            <BottomContent />
        </div>
    );
}
