import React, { useRef, useEffect, useState, forwardRef, useImper<PERSON><PERSON>andle, useMemo } from "react";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { ExclamationCircleFilled, InboxOutlined } from "@ant-design/icons";
import FlowIcon from "@/app/icons/dashboard/flow.svg";
import MeetingIcon from "@/app/icons/dashboard/create_meeting.svg";
import UploadIcon from "@/app/icons/dashboard/upload_document.svg";
import TaskIcon from "@/app/icons/dashboard/assign_task.svg";
import FlowHistoryIcon from "@/app/icons/dashboard/flow_history.svg";
import { Button, Empty, Flex, Modal, Pagination, Skeleton, Tabs, Tooltip, Upload } from "antd";
import ChatDialog from "../../components/ChatDialog";
import AddFlow from "./AddFlow";
import styles from "./index.module.scss";
import { nanoid } from "nanoid";
import { Path } from "../../constant";
import Dragger from "antd/es/upload/Dragger";
import {
    checkRolePermissionApi,
    deleteTempJobFlowDataApi,
    deprecateJobFlowApi,
    downloadJobFlowTemplateApi,
    getTempJobFlowDataApi,
    getTodoListApi,
    processFlowApi,
    saveTempJobFlowDataApi,
} from "@/app/request/modules/dashboard";
import { DictData, PaginationInfo, RespPaginationParams, RespParams } from "@/app/typing";
import {
    ApprovalModalRef,
    Department,
    FlowDetailExposeHandle,
    FlowItemResp,
    Job,
    ProcessFlowReq,
    TodoItem,
    AddFlowExposeHandle,
} from "@/app/store/modules/dashboard/jobFlow";
import { cloneDeep } from "lodash-es";
import { defaultFlowItem, JobTags, TaskTypeList } from "./constant";
import TextArea from "antd/es/input/TextArea";
import FlowDetail from "./FlowHistory/FlowDetail";
import useUserInfoStore from "@/app/store/userInfo";
import { arrayToTree, formatHtmlWithLists, getNodeWithAncestors, treeToArray } from "@/app/utils";
import UserSelect from "../../components/UserSelect";
import { RespUser, UserSelectExpose } from "../../components/typing";
import messageService from "@/app/lib/message";
import useJobFlowStore from "@/app/store/modules/dashboard/jobFlow";
import { getDepartmentListApi, getDictDataByCodeApi, getJobListApi } from "@/app/request/modules/common";

export default function Flow({
    type,
    flowId,
    keyword,
}: {
    type: string | null;
    flowId: string | null;
    keyword?: string;
}) {
    const { token, user } = useUserInfoStore((state) => ({
        token: state.token,
        user: state.user,
    }));

    const [showFlow, setShowFlow] = useState(false);
    const [tabsActiveKey, setTabsActiveKey] = useState("input");
    const [isInit, setIsInit] = useState(true);

    const { departmentList, flowItems, setFlowItems, setDepartmentList, setEducationList } = useJobFlowStore(
        (state) => ({
            flowItems: state.flowItems,
            setFlowItems: state.setFlowItems,
            departmentList: state.departmentList,
            setDepartmentList: state.setDepartmentList,
            setEducationList: state.setEducationList,
        })
    );

    const navigate = useNavigate();

    const handleHistory = (e: React.MouseEvent<HTMLDivElement>): void => {
        e.stopPropagation();
        navigate(Path.FlowHistory);
    };

    const headerItem = [
        {
            key: "flow",
            icon: <FlowIcon />,
            title: (
                <div style={{ display: "flex", justifyContent: "space-between" }}>
                    <span>发起审批</span>
                    <Tooltip title="查看历史审批">
                        <FlowHistoryIcon
                            style={{ width: "22px", height: "22px" }}
                            onClick={(e: React.MouseEvent<HTMLDivElement>) => handleHistory(e)}
                        />
                    </Tooltip>
                </div>
            ),
            bgColor: "#FFF8E4",
            borderColor: "rgba(255, 146, 66, 0.30)",
            content: "快速发起各类审批",
        },
        {
            key: "flowPublish",
            icon: <MeetingIcon />,
            title: "招聘需求",
            bgColor: "#e7f8fc",
            borderColor: "#a2dbf9",
            content: "快速定位当前招聘需求进展",
        },
        {
            key: "jobManage",
            icon: <UploadIcon />,
            title: "职位管理",
            bgColor: "#f0f8eb",
            borderColor: "#cae8b8",
            content: "维护职位面试信息",
        },
        {
            key: "task",
            icon: <TaskIcon />,
            title: "任务分配",
            bgColor: "#e8f6ff",
            borderColor: "#98cdec",
            content: "创建并分配团队任务",
        },
    ];

    const addFlowRef = useRef<AddFlowExposeHandle>(null);

    const getDepartmentList = async () => {
        const res: RespParams<Department> = await (await getDepartmentListApi()).json();
        if (res.code === 200) {
            const origin = [res.data].map((item) => {
                if (item.dept_code === "100000") {
                    item.parent_dept_code = "-1";
                }
                return item;
            });
            const list = treeToArray(origin);
            list.forEach((item) => {
                item.isDisabled = item.dept_level === 90;
            });
            const treeData = arrayToTree(list, "dept_code", "parent_dept_code");
            setDepartmentList(treeData);
        }
    };

    const getEducationList = async () => {
        const res: RespParams<DictData[]> = await (await getDictDataByCodeApi("education")).json();
        if (res.code === 200) {
            setEducationList(res.data.map((item) => item.key));
        }
    };

    useEffect(() => {
        getDepartmentList();
        getEducationList();
    }, []);

    // 打开发起审批弹窗
    const handleClick = async (key: string) => {
        if (key === "flow") {
            const empId = user.emp_id;
            // 验证登录用户是否有权限发起审批
            const res: RespParams<boolean> = await (await checkRolePermissionApi(1)).json();
            if (res.code === 200) {
                if (!res.data) {
                    messageService.warning("当前用户没有审批权限，请联系管理员");
                    return;
                }
            }
            // 获取缓存表单数据
            const res2: RespParams<{ id: number; value: string }> = await (
                await getTempJobFlowDataApi("jobFlow")
            ).json();
            if (res2.code === 200) {
                if (res2.data) {
                    const data = JSON.parse(res2.data?.value);

                    const promiseList = data.map(async (item: FlowItemResp) => {
                        const res: RespParams<Job[]> = await (
                            await getJobListApi({
                                orgNo: item.orgId[item.orgId.length - 1],
                            })
                        ).json();

                        return res;
                    });

                    Promise.all(promiseList).then((res) => {
                        const list = data.map((item: FlowItemResp, index: number) => {
                            item.jobList = res[index].data;

                            return item;
                        });

                        const timer = setTimeout(() => {
                            setFlowItems(list);
                            addFlowRef.current?.setActiveKey(data.map((item: FlowItemResp) => item.jobId));

                            clearTimeout(timer);
                        }, 50);
                    });
                } else {
                    const timer = setTimeout(() => {
                        const newFlowItem = cloneDeep({ ...defaultFlowItem, jobId: nanoid(), jobTags: [...JobTags] });
                        setFlowItems([newFlowItem]);
                        addFlowRef.current?.setActiveKey([newFlowItem.jobId]);

                        clearTimeout(timer);
                    }, 50);
                }
                setTabsActiveKey("input");
                setFileList([]);
                setShowFlow(true);
            }

            return;
        }

        if (key === "flowPublish") {
            // 验证登录用户是否有权限发起审批
            const res: RespParams<boolean> = await (await checkRolePermissionApi(2)).json();
            if (res.code === 200) {
                if (!res.data) {
                    messageService.warning("当前用户没有招聘需求权限，请联系管理员");
                    return;
                }
            }
            navigate(Path.FlowPublish);
            return;
        }

        if (key === "jobManage") {
            navigate(Path.JobManage);
            return;
        }
    };

    // 关闭弹窗
    const onCancel = async () => {
        const result: any = await tempDialog();

        if (result === true) {
            addFlowRef.current?.onCancel();
            setShowFlow(false);
        }
    };

    // 提示弹窗，是否缓存数据
    const tempDialog = () => {
        return new Promise((resolve) => {
            Modal.confirm({
                title: "是否暂存已填写数据?",
                icon: <ExclamationCircleFilled />,
                content: "点击确定后暂存数据，关闭弹窗",
                onOk() {
                    const list = flowItems.map((item) => {
                        let params = addFlowRef.current?.formRefs?.[item?.jobId]?.getForm()?.getFieldsValue();
                        if (!params) {
                            params = { ...item };
                        }
                        params.jobTags = item.jobTags;
                        params.jobJD =
                            addFlowRef.current?.formRefs[item?.jobId]?.richTextRef?.current?.getHTML() ?? params.jobJD;
                        params.jobJD = formatHtmlWithLists(params.jobJD);
                        return params;
                    });

                    return new Promise((resolve2, reject2) => {
                        saveTempJobFlowDataApi({
                            category: "jobFlow",
                            content: JSON.stringify(list),
                        })
                            .then((response) => response.json())
                            .then((res: any) => {
                                if (res.code === 200) {
                                    messageService.success("保存成功");
                                    resolve2(true);
                                    resolve(true);
                                } else {
                                    reject2(false);
                                }
                            })
                            .catch((err) => {
                                reject2(false);
                            });
                    });
                },
                onCancel() {
                    resolve(true);
                },
            });
        });
    };

    // 提交审批
    const onOk = async () => {
        const res = await addFlowRef.current?.onOk();

        if (res) {
            deleteTempJobFlowDataApi("jobFlow");
            setShowFlow(false);
            resetPagination();
            setRefreshCounter((prev) => prev + 1);
        }
    };

    const [todoList, setTodoList] = useState<any>([]);
    const [currentTabs, setCurrentTabs] = useState("JobFlowTodo");
    const [pagination, setPagination] = useState<PaginationInfo>({ pageNum: 0, pageSize: 10, total: 0 });
    const [todoItemLoading, setTodoItemLoading] = useState(false);
    const [refreshCounter, setRefreshCounter] = useState(0);

    useEffect(() => {
        if (!isInit) {
            resetPagination();
            getTodoList();
        }
    }, [keyword]);

    useEffect(() => {
        getTodoList();
        setIsInit(false);
    }, [currentTabs, pagination.pageNum, pagination.pageSize, refreshCounter]);

    const getTodoList = async () => {
        const status = TaskTypeList.find((item) => item.key === currentTabs)?.status;
        if (status) {
            setTodoItemLoading(true);
            try {
                const res: RespPaginationParams<TodoItem[]> = await (
                    await getTodoListApi({
                        pageNum: pagination.pageNum,
                        pageSize: pagination.pageSize,
                        status,
                        keyword: keyword,
                    })
                ).json();

                if (res.code === 200) {
                    setTodoList(res?.data?.records ?? []);
                    setPagination({ ...pagination, total: res?.data?.total ?? 0 });
                    setTodoItemLoading(false);
                }
            } catch (err) {
                setTodoItemLoading(false);
            }
        }
    };

    const resetPagination = () => {
        setPagination((prev) => ({ ...prev, pageNum: 0, pageSize: 10, total: 0 }));
    };

    const tabItems = TaskTypeList.filter((item) => !item.isHistory).map((item) => {
        return {
            ...item,
            children: (
                <TodoContent
                    type={type}
                    flowId={flowId}
                    currentTabs={currentTabs}
                    todoList={todoList}
                    refresh={() => {
                        resetPagination();
                        setRefreshCounter((prev) => prev + 1);
                    }}
                    pagination={pagination}
                    loading={todoItemLoading}
                    handlePaginationChange={(pageNum, pageSize) => {
                        setPagination({ ...pagination, pageNum: pageNum - 1, pageSize });
                    }}
                />
            ),
        };
    });
    const [loading, setLoading] = useState(false);
    const [fileList, setFileList] = useState<any>([]);

    const props = {
        name: "file",
        accept: ".xlsx,.xls,.csv,.ods",
        action: `${process.env.NEXT_PUBLIC_FETCH_BASE_API}/jobSpec/import/excel`,
        headers: {
            Authorization: token,
        },
        multiple: false,
        fileList: fileList,
        beforeUpload(file: File) {
            const allowedMimeTypes = [
                "application/vnd.ms-excel",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "text/csv",
            ];

            if (!allowedMimeTypes.includes(file.type)) {
                messageService.error("文件格式不正确，上传失败！");

                return Upload.LIST_IGNORE;
            }

            // 开始上传
            setLoading(true);
        },
        async onChange(info: any) {
            if (info.fileList.length === 0) {
                setFileList([]);
            } else {
                setFileList([info.file]);
            }

            const { status, response } = info.file;
            if (status === "done") {
                const res = info.file.response;

                if (res.code === 200) {
                    messageService.success(`${info.file.name} 文件上传成功`);

                    let allJobList: Job[] = [];
                    const res2: RespParams<Job[]> = await (await getJobListApi({ orgNo: "" })).json();
                    if (res2.code === 200) {
                        allJobList = [...res2.data];
                    }

                    if (res.data && res.data.length > 0) {
                        const data = res.data.map((item: FlowItemResp) => {
                            item.jobNameInnerPath = item.jobNameInner.split("/");
                            item.educational = Array.isArray(item.educational)
                                ? item.educational
                                : item.educational.split("/");
                            item.jobLocation = Array.isArray(item.jobLocation)
                                ? item.jobLocation
                                : item.jobLocation.split("/");
                            item.orgId = Array.isArray(item.orgId) ? item.orgId : item.orgId.split("/");

                            const orgNameList = getNodeWithAncestors(
                                departmentList,
                                item.orgId[item.orgId.length - 1],
                                "dept_code"
                            );
                            item.orgName = orgNameList.map((item) => item.dept_name).join("/");
                            item.jobId = nanoid();
                            item.jobTags = [...JobTags];
                            item.jobList = allJobList;
                            item.jobNameInnerPath = item.jobNameInner.split("/");
                            const jobNameList = getNodeWithAncestors(
                                allJobList,
                                item.jobNameInnerPath[item.jobNameInnerPath.length - 1],
                                "code"
                            );
                            item.jobNameInner = jobNameList.map((item2) => item2.desc).join("/");
                            return item;
                        });

                        setFlowItems(data);
                        setTabsActiveKey("input");
                        setLoading(false);

                        addFlowRef.current?.setActiveKey([]);
                    } else {
                        messageService.error("文件内容为空");
                        setLoading(false);
                    }
                }
            } else if (status === "error") {
                messageService.error(response?.msg ?? `${info.file.name} 文件上传失败`);
                setLoading(false);
            }
        },
    };

    // 模版下载
    const handleDownloadTemplate = async () => {
        try {
            await downloadJobFlowTemplateApi();
        } catch (error) {
            console.error(error);
            messageService.error("模版下载失败");
        }
    };

    return (
        <div className={styles["flow-wrapper"]}>
            <div className={styles["flow-wrapper-header"]}>
                {headerItem.map((item, index) => {
                    return (
                        <div
                            className={styles["flow-wrapper-header-item"]}
                            key={index}
                            onClick={() => handleClick(item.key)}
                        >
                            <div
                                className={styles["item-icon"]}
                                style={{ background: item.bgColor, borderColor: item.borderColor }}
                            >
                                {item.icon}
                            </div>
                            <div style={{ flex: 1 }}>
                                <div className={styles["item-title"]}>{item.title}</div>
                                <div className={styles["item-content"]}>{item.content}</div>
                            </div>
                        </div>
                    );
                })}
            </div>
            <div className={styles["flow-wrapper-content"]}>
                <Tabs
                    defaultActiveKey={currentTabs}
                    items={tabItems}
                    onChange={(val) => {
                        setCurrentTabs(val);
                        resetPagination();
                    }}
                />
            </div>
            <div className={styles["flow-wrapper-footer"]}></div>
            {/* 新增审批 */}
            <ChatDialog
                open={showFlow}
                title="发起审批"
                onOk={onOk}
                onCancel={onCancel}
                confirmLoading={loading}
                width="75%"
                className={styles["add-flow-dialog-wrapper"]}
            >
                <Tabs
                    activeKey={tabsActiveKey}
                    items={[
                        {
                            key: "input",
                            label: "手动输入",
                            children: (
                                <div className={styles["tabs-content"]}>
                                    <AddFlow ref={addFlowRef} handleChangeLoading={setLoading}></AddFlow>
                                </div>
                            ),
                        },
                        {
                            key: "import",
                            label: "文件上传",
                            children: (
                                <div className={styles["tabs-content"]}>
                                    <div className={styles["upload-btn-wrapper"]}>
                                        <Button type="primary" onClick={handleDownloadTemplate}>
                                            模版下载
                                        </Button>
                                    </div>
                                    <Dragger {...props}>
                                        <p className="ant-upload-drag-icon">
                                            <InboxOutlined />
                                        </p>
                                        <p className="ant-upload-text">点击或拖拽上传文件</p>
                                        <p className="ant-upload-hint">
                                            只允许上传单个文件，并且文件格式要求为.xlsx,.xls,.csv,.ods
                                        </p>
                                    </Dragger>
                                </div>
                            ),
                        },
                    ]}
                    onChange={setTabsActiveKey}
                />
            </ChatDialog>
        </div>
    );
}

// 代办、已办列表
const TodoContent = ({
    currentTabs,
    todoList,
    refresh,
    type,
    flowId,
    pagination,
    loading,
    handlePaginationChange,
}: {
    currentTabs: string;
    todoList: TodoItem[];
    refresh: () => void;
    type: string | null;
    flowId: string | null;
    pagination: PaginationInfo;
    loading: boolean;
    handlePaginationChange: (pageNum: number, pageSize: number) => void;
}) => {
    const [showDetail, setShowDetail] = useState(false);
    const [currentTodoItem, setCurrentTodoItem] = useState<TodoItem | null>();
    const [confirmLoading, setConfirmLoading] = useState(false);
    const [showFooter, setShowFooter] = useState(true);
    const [showRejectBtn, setShowRejectBtn] = useState(false);
    const modalRef = useRef<ApprovalModalRef>(null);
    const flowDetailRef = useRef<FlowDetailExposeHandle>(null);
    const [searchParams] = useSearchParams();
    const location = useLocation();
    const navigate = useNavigate();

    const handleClick = async (item: TodoItem) => {
        setCurrentTodoItem(item);
        setShowDetail(true);
        const timer = setTimeout(() => {
            flowDetailRef.current?.refresh();
            clearInterval(timer);
        }, 100);
    };

    useEffect(() => {
        if (type === "1" && flowId && currentTabs === "JobFlowTodo") {
            const todoItem: TodoItem = {
                id: 0,
                content: "",
                start_time: "",
                title: "",
                type: 0,
                deadline: "",
                ref_id: flowId,
                senderUser: {
                    avatar: "",
                    emp_id: "",
                    emp_name: "",
                    gender: "",
                    perms: [],
                    permGroups: [],
                },
                targetUser: {
                    avatar: "",
                    empId: "",
                    empName: "",
                    emp_id: "",
                    emp_name: "",
                    gender: "",
                    perms: [],
                    permGroups: [],
                },
            };
            handleClick(todoItem);
        }
    }, [type, flowId]);

    // 从外部跳转后，点击通过或者驳回，去除url参数，重新刷新页面
    const updateSearchParams = () => {
        if (searchParams.get("flowId") && searchParams.get("type") === "1") {
            navigate(location.pathname, { replace: true });
        }
    };

    // 模态框组件,用于填写审批意见
    const ApprovalModal = forwardRef((props, ref) => {
        const [isVisible, setIsVisible] = useState(false);
        const [inputValue, setInputValue] = useState("");
        const [showDelegate, setShowDelegate] = useState(false);
        const [loading, setLoading] = useState(false);
        const [showUserSelect, setShowUserSelect] = useState(false);
        const [currentUser, setCurrentUser] = useState<RespUser>();
        const [params, setParams] = useState<ProcessFlowReq>({ flowInstanceId: "", comment: "", status: "" });

        const promiseRef = useRef<{
            resolve: ((value: boolean | PromiseLike<boolean>) => void) | null;
            reject: ((reason?: any) => void) | null;
        }>({ resolve: null, reject: null });
        const userSelectRef = useRef<UserSelectExpose>(null);

        // 暴露方法给父组件
        useImperativeHandle(ref, () => ({
            open: (newParams: ProcessFlowReq) => {
                return new Promise((resolve, reject) => {
                    setInputValue(newParams.comment || "");
                    setParams(newParams);
                    promiseRef.current = { resolve, reject };
                    setIsVisible(true);
                    setShowDelegate(newParams.showDelegate ?? false);
                });
            },
        }));

        const handleOk = async () => {
            try {
                if (!inputValue.trim()) {
                    messageService.warning("请输入审批意见");
                    return;
                }

                let updatedParams = { ...params, comment: inputValue.trim() };
                if (showDelegate) {
                    if (!currentUser) {
                        messageService.warning("请先选择转办人！");
                        return;
                    } else {
                        updatedParams = { ...updatedParams, targetApprover: currentUser.empId };
                    }
                }
                setLoading(true);
                const res: RespParams<any> = await (await processFlowApi(updatedParams)).json();
                if (res.code === 200) {
                    setLoading(false);
                    promiseRef.current.resolve?.(true);
                    setIsVisible(false);
                } else {
                    setLoading(false);
                }
            } catch (error) {
                console.error("处理失败", error);
                setLoading(false);
            }
        };

        const handleCancel = () => {
            promiseRef.current.reject?.("用户取消操作");
            setIsVisible(false);
        };

        const handleSelectUser = () => {
            const user = userSelectRef.current?.getSelectedUser;

            if (user) {
                setCurrentUser(user?.[0]);
                setShowUserSelect(false);
            } else {
                messageService.warning("请选择转办人！");
            }
        };

        return (
            <Modal
                title="请填写审批意见"
                confirmLoading={loading}
                open={isVisible}
                onOk={handleOk}
                onCancel={handleCancel}
            >
                {showDelegate && (
                    <div style={{ margin: "16px 0" }}>
                        {currentUser && (
                            <span>
                                {currentUser?.empId}-{currentUser?.empName}
                            </span>
                        )}
                        <Button style={{ marginLeft: "12px" }} type="primary" onClick={() => setShowUserSelect(true)}>
                            选择转办人
                        </Button>
                    </div>
                )}
                <TextArea
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    placeholder="请填写审批意见"
                    rows={4}
                />
                <ChatDialog
                    open={showUserSelect}
                    title="选择转办人"
                    width="75%"
                    onOk={handleSelectUser}
                    onCancel={() => setShowUserSelect(false)}
                    confirmLoading={confirmLoading}
                >
                    <UserSelect ref={userSelectRef} />
                </ChatDialog>
            </Modal>
        );
    });
    ApprovalModal.displayName = "ApprovalModal";

    // 废弃
    const handleDeprecate = async () => {
        const res = flowDetailRef.current?.isEditMode;
        if (res) {
            messageService.error("请先保存表单");
            return;
        }

        try {
            const result = await new Promise((resolve) => {
                Modal.confirm({
                    title: "是否废弃该需求?",
                    icon: <ExclamationCircleFilled />,
                    content: "点击确定后废弃，关闭弹窗",
                    onOk() {
                        return new Promise((resolve2, reject2) => {
                            deprecateJobFlowApi({
                                type: flowDetailRef.current?.currentJobForm?.flowId ?? 0,
                                ids: [Number(flowDetailRef.current?.currentJobForm?.relatedForm)],
                            })
                                .then((response) => response.json())
                                .then((res: any) => {
                                    if (res.code === 200) {
                                        messageService.success("审批废弃");
                                        resolve2(true);
                                        resolve(true);
                                    } else {
                                        reject2(false);
                                    }
                                })
                                .catch((err) => {
                                    reject2(false);
                                });
                        });
                    },
                    onCancel() {
                        resolve(true);
                    },
                });
            });
            if (result) {
                setShowDetail(false);
                refresh();
                updateSearchParams();
            }
        } catch (error) {
            console.log("操作取消", error);
        }
    };

    // 驳回
    const handleReject = async () => {
        const res = flowDetailRef.current?.isEditMode;
        if (res) {
            messageService.error("请先保存表单");
            return;
        }

        try {
            const currentFlow = flowDetailRef.current?.currentFlow;
            if (currentFlow?.nodeDefinition.fallbackNodeId === -1) {
                messageService.error("当前节点不允许驳回");
                throw new Error("当前节点不允许驳回");
            }

            await modalRef.current?.open({
                flowInstanceId: currentTodoItem?.ref_id ?? "",
                status: "2",
                comment: "驳回",
            });
            messageService.success("审批驳回");
            setShowDetail(false);
            refresh();
            updateSearchParams();
        } catch (error) {
            console.log("操作取消", error);
        }
    };

    // 通过
    const handlePass = async () => {
        const res = flowDetailRef.current?.isEditMode;
        if (res) {
            messageService.error("请先保存表单");
            return;
        }

        try {
            await modalRef.current?.open({
                flowInstanceId: currentTodoItem?.ref_id ?? "",
                status: "1",
                comment: "同意",
            });
            messageService.success("审批通过");
            setShowDetail(false);
            refresh();
            updateSearchParams();
        } catch (error) {
            console.log("操作取消", error);
        }
    };

    // 转办
    const handleDelegate = async () => {
        const res = flowDetailRef.current?.isEditMode;
        if (res) {
            messageService.error("请先保存表单");
            return;
        }

        try {
            await modalRef.current?.open({
                flowInstanceId: currentTodoItem?.ref_id ?? "",
                status: "3",
                comment: "转办",
                showDelegate: true,
            });
            messageService.success("审批通过");
            setShowDetail(false);
            refresh();
            updateSearchParams();
        } catch (error) {
            console.log("操作取消", error);
        }
    };

    const handlePageChange = (page: number, pageSize: number) => {
        handlePaginationChange(page, pageSize);
    };

    const handlePageSizeChange = (pageSize: number) => {
        handlePaginationChange(pagination.pageNum, pageSize);
    };

    return (
        <Flex vertical gap={16} className={styles["todo-wrapper"]}>
            <div className={styles["flow-wrapper-content-list"]}>
                {loading ? (
                    <Skeleton active paragraph={{ rows: 10 }} />
                ) : todoList.length > 0 ? (
                    todoList?.map((item) => (
                        <div className={styles["list-item"]} key={item.id} onClick={() => handleClick(item)}>
                            <div className={styles["list-item-left"]}>
                                <div className={styles["time"]}>{item.start_time}</div>
                                {/* <div className={styles["status"]}>待参加</div> */}
                            </div>
                            <div className={styles["list-item-right"]}>
                                <div className={styles["title"]}>
                                    <span>{item.title}</span>
                                </div>
                                <div className={styles["info"]}>
                                    <span>类型：{item.content}</span>
                                    <span>截止时间：{item.deadline}</span>
                                    <div className={styles["owner"]}>
                                        <span>审批人:</span>
                                        {/* <div className={styles["avatar"]}>
                                        <AvatarIcon style={{ width: "100%", height: "100%" }} />
                                    </div> */}
                                        <span>
                                            {item.targetUser.empId}-{item.targetUser.empName}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            {/* <div className={styles["tags"]}>
                            <div className={styles["priority"]}>高优</div>
                            <div className={styles["progress"]}>
                                进度<span style={{ color: "#0099f2" }}>30%</span>
                            </div>
                        </div> */}
                        </div>
                    ))
                ) : (
                    <Empty />
                )}
            </div>
            <Flex justify="end" className={styles["todo-pagination"]}>
                <Pagination
                    size="small"
                    current={pagination.pageNum + 1}
                    pageSize={pagination.pageSize}
                    total={pagination.total}
                    showSizeChanger
                    showTotal={(total) => `共 ${total} 条`}
                    onChange={handlePageChange}
                    onShowSizeChange={handlePageSizeChange}
                />
            </Flex>
            <ChatDialog
                open={showDetail}
                title="审批详情"
                width="75%"
                onOk={() => {}}
                onCancel={() => setShowDetail(false)}
                confirmLoading={confirmLoading}
                className={styles["flow-detail-dialog-wrapper"]}
                footer={
                    showFooter && currentTabs === "JobFlowTodo" ? (
                        <div>
                            <Button
                                style={{ marginRight: 12 }}
                                loading={confirmLoading}
                                color="danger"
                                variant="outlined"
                                onClick={handleDeprecate}
                            >
                                废弃
                            </Button>
                            {showRejectBtn && (
                                <Button
                                    style={{ marginRight: 12 }}
                                    loading={confirmLoading}
                                    color="danger"
                                    variant="outlined"
                                    onClick={handleReject}
                                >
                                    驳回
                                </Button>
                            )}
                            <Button
                                style={{ marginRight: 12 }}
                                loading={confirmLoading}
                                color="primary"
                                variant="outlined"
                                onClick={handleDelegate}
                            >
                                转办
                            </Button>
                            <Button loading={confirmLoading} color="primary" variant="outlined" onClick={handlePass}>
                                通过
                            </Button>
                        </div>
                    ) : null
                }
            >
                <FlowDetail
                    ref={flowDetailRef}
                    flowId={currentTodoItem?.ref_id ?? ""}
                    handleChangeLoading={(val) => setConfirmLoading(val)}
                    handleShowFooter={(val) => setShowFooter(val)}
                    handleShowRejectBtn={(val) => setShowRejectBtn(val)}
                    type={currentTabs}
                />
                <ApprovalModal ref={modalRef} />
            </ChatDialog>
        </Flex>
    );
};
