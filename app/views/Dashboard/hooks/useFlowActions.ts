import { useCallback } from "react";
import useJobDetailStore from "@/app/store/modules/dashboard/jobDetail";

/**
 * 流程操作Hook
 * @returns 流程操作方法集合
 */
export const useFlowActions = () => {
    const dispatch = useJobDetailStore((state) => state.dispatch);
    const batchUpdate = useJobDetailStore((state) => state.batchUpdate);

    /**
     * 更新阶段信息
     * @param stageCode 阶段编码
     * @param data 更新数据
     */
    const updateStage = useCallback(
        (stageCode: string, data: any) => {
            dispatch({ type: "UPDATE_STAGE", payload: { stageCode, data } });
        },
        [dispatch]
    );

    /**
     * 添加新阶段
     * @param stageData 阶段数据
     */
    const addStage = useCallback(
        (stageData: any) => {
            dispatch({ type: "ADD_STAGE", payload: stageData });
        },
        [dispatch]
    );

    /**
     * 删除阶段
     * @param stageCode 阶段编码
     */
    const deleteStage = useCallback(
        (stageCode: string) => {
            dispatch({ type: "DELETE_STAGE", payload: { stageCode } });
        },
        [dispatch]
    );

    /**
     * 重新排序阶段
     * @param fromIndex 源索引
     * @param toIndex 目标索引
     */
    const reorderStages = useCallback(
        (fromIndex: number, toIndex: number) => {
            dispatch({ type: "REORDER_STAGES", payload: { fromIndex, toIndex } });
        },
        [dispatch]
    );

    /**
     * 添加新状态
     * @param stageCode 阶段编码
     * @param statusData 状态数据
     */
    const addStatus = useCallback(
        (stageCode: string, statusData: any) => {
            dispatch({ type: "ADD_STATUS", payload: { stageCode, newStatus: statusData } });
        },
        [dispatch]
    );

    /**
     * 更新阶段状态
     * @param stageCode 阶段编码
     * @param statusCode 状态编码
     * @param data 更新数据
     */
    const updateStageStatus = useCallback(
        (stageCode: string, statusCode: string, data: any) => {
            dispatch({ type: "UPDATE_STAGE_STATUS", payload: { stageCode, statusCode, data } });
        },
        [dispatch]
    );

    /**
     * 删除阶段状态
     * @param stageCode 阶段编码
     * @param statusCode 状态编码
     */
    const deleteStageStatus = useCallback(
        (stageCode: string, statusCode: string) => {
            dispatch({ type: "DELETE_STAGE_STATUS", payload: { stageCode, statusCode } });
        },
        [dispatch]
    );

    /**
     * 重新排序状态
     * @param stageCode 阶段编码
     * @param fromIndex 源索引
     * @param toIndex 目标索引
     */
    const reorderStatuses = useCallback(
        (stageCode: string, fromIndex: number, toIndex: number) => {
            dispatch({ type: "REORDER_STATUSES", payload: { stageCode, fromIndex, toIndex } });
        },
        [dispatch]
    );

    return {
        updateStage,
        addStage,
        deleteStage,
        reorderStages,
        addStatus,
        updateStageStatus,
        batchUpdate,
        deleteStageStatus,
        reorderStatuses,
    };
};
