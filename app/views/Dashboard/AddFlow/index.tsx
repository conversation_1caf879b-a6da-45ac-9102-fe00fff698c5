import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import styles from "./index.module.scss";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from "react";
import {
    AddFlowExposeHandle,
    AddFlowItemExposeHandle,
    Department,
    FlowItemParams,
    JobFlowReq,
    TagsItem,
    TagsResp,
} from "@/app/store/modules/dashboard/jobFlow";
import FlowItem from "./FlowItem";
import { nanoid } from "nanoid";
import { defaultFlowItem, educationalData, jobLocationData, JobTags } from "../constant";
import { cloneDeep } from "lodash-es";
import useJobFlowStore from "@/app/store/modules/dashboard/jobFlow";
import { arrayToTree, formatHtmlWithLists, treeToArray } from "@/app/utils";
import messageService from "@/app/lib/message";
import { DictD<PERSON>, RespParams } from "@/app/typing";
import { submitJobFlow } from "@/app/request/modules/dashboard";
import { getDepartmentListApi, getDictDataByCodeApi } from "@/app/request/modules/common";

const AddFlow = forwardRef<AddFlowExposeHandle, { handleChangeLoading: (val: boolean) => void }>((props, ref) => {
    const { handleChangeLoading } = props;

    const { flowItems, educationList, setFlowItems, setDepartmentList, setEducationList } = useJobFlowStore(
        (state) => ({
            flowItems: state.flowItems,
            educationList: state.educationList,
            setFlowItems: state.setFlowItems,
            setDepartmentList: state.setDepartmentList,
            setEducationList: state.setEducationList,
        })
    );

    const [defaultActiveKey, setDefaultActiveKey] = useState<string[]>([]);

    useImperativeHandle(ref, () => ({
        formRefs: formRefs.current,
        onOk: async () => {
            const validateRes = [];
            const list: JobFlowReq[] = [];

            for (const item of flowItems) {
                const res = formRefs.current?.[item?.jobId]?.getForm()?.validateFields();
                if (res) {
                    validateRes.push(res);
                } else {
                    if (!item.orgId && !item.orgName) {
                        messageService.error(item.orgName + "-" + item.jobNameInner + "需求部门不能为空");
                        return false;
                    }
                    if (!item.jobNameInner && !item.jobNameInnerPath) {
                        messageService.error(item.orgName + "-" + item.jobNameInner + "内部岗位名称不能为空");
                        return false;
                    }
                    if (!item.jobNameOuter) {
                        messageService.error(item.orgName + "-" + item.jobNameInner + "岗位名称(对外)不能为空");
                        return false;
                    }
                    const regex = /【26校招(\s*-\s*[^\s】][^】]*)?】.*/;
                    if (!regex.test(item.jobNameOuter)) {
                        messageService.error(
                            item.orgName +
                                "-" +
                                item.jobNameInner +
                                "对外职位名称格式为【26校招 - xxxx】岗位名称或者【26校招】岗位名称"
                        );
                        return false;
                    }
                    if (!item.hiringBatch) {
                        messageService.error(item.orgName + "-" + item.jobNameInner + "招聘批次不能为空");
                        return false;
                    }
                    if (!item.hiringPersonNum) {
                        messageService.error(item.orgName + "-" + item.jobNameInner + "目标人数不能为空");
                        return false;
                    }
                    if (!item.offerTarget) {
                        messageService.error(item.orgName + "-" + item.jobNameInner + "offer目标不能为空");
                        return false;
                    }
                    if (!item.jobType) {
                        messageService.error(item.orgName + "-" + item.jobNameInner + "职位类别不能为空");
                        return false;
                    }
                    if (item.jobType === "核心岗位" && !item.reason) {
                        messageService.error(item.orgName + "-" + item.jobNameInner + "原因说明不能为空");
                        return false;
                    }
                    if (!item.jobLocation) {
                        messageService.error(item.orgName + "-" + item.jobNameInner + "工作地点不能为空");
                        return false;
                    }
                    if (item.jobLocation) {
                        for (const item2 of item.jobLocation) {
                            if (!jobLocationData.find((item3) => item2 === item3.value)) {
                                messageService.error(
                                    item.orgName + "-" + item.jobNameInner + `工作地点“${item2}”不存在`
                                );
                                return false;
                            }
                        }
                    }
                    if (!item.educational) {
                        messageService.error(item.orgName + "-" + item.jobNameInner + "学历要求不能为空");
                        return false;
                    }
                    if (item.educational) {
                        for (const item2 of item.educational) {
                            if (!educationList.find((item3) => item2 === item3)) {
                                messageService.error(
                                    item.orgName + "-" + item.jobNameInner + `学历要求“${item2}”不存在`
                                );
                                return false;
                            }
                        }
                    }
                }

                let params: any = formRefs.current?.[item?.jobId]?.getForm()?.getFieldsValue();
                if (!params) {
                    params = { ...item };
                }

                params.jobJD = formRefs.current?.[item?.jobId]?.richTextRef?.current?.getHTML() ?? params.jobJD;
                params.jobJD = formatHtmlWithLists(params.jobJD);
                if (Array.isArray(params.orgId)) {
                    params.orgId = params?.orgId?.join("/");
                }
                if (Array.isArray(params.jobNameInnerPath)) {
                    params.jobNameInnerPath = params?.jobNameInnerPath?.join("/");
                }
                if (Array.isArray(params.educational)) {
                    params.educational = params?.educational?.join("/");
                }
                if (Array.isArray(params.jobLocation)) {
                    params.jobLocation = params?.jobLocation?.join("/");
                }

                // 校验人才比例之后是否为100%
                if (Math.abs(params.topTalents + params.excellentTalents + params.regularTalents - 100) > 0.01) {
                    messageService.error(
                        `${params?.orgName ?? "默认组织"}-${params?.jobNameInner ?? "默认岗位"} 人才比例之和必须为100%`
                    );
                    return false;
                }

                // 检验职位JD是否符合正则校验
                const regex1 = /(工作职责|岗位职责|主要职责)[:：]\s*(?=\S)/g;
                const regex2 = /(职位要求|岗位要求|任职要求)[:：]\s*(?=\S)/g;
                if (!regex1.test(params.jobJD) || !regex2.test(params.jobJD)) {
                    messageService.error(
                        <Flex vertical align="start">
                            <div>岗位JD格式错误，格式必须为: </div>
                            <div>工作职责/岗位职责/主要职责: </div>
                            <div>xxxxxxx</div>
                            <div>职位要求/岗位要求/任职要求:</div>
                            <div>xxxxxxx</div>
                        </Flex>
                    );
                    return false;
                }

                params.tags = [];
                params.jobTags = item.jobTags;
                params.jobTags?.forEach((item: TagsItem) => {
                    item?.children?.forEach((item: TagsItem) => {
                        params.tags.push(...(item.value?.map((item2: TagsResp) => item2.id) ?? []));
                    });
                });
                delete params.jobTags;

                list.push(params);
            }

            try {
                await Promise.all(validateRes);
                handleChangeLoading(true);

                const res: RespParams<any> = await (await submitJobFlow(list)).json();
                if (res.code === 200) {
                    handleChangeLoading(false);
                    messageService.success("提交成功！");
                    return true;
                }
            } catch (err: any) {
                const validateError = err?.values ?? null;
                if (validateError?.jobId) {
                    setDefaultActiveKey([validateError.jobId]);
                }

                handleChangeLoading(false);
                return false;
            }

            return true;
        },
        onCancel: () => {
            console.log("onCancel");
            return true;
        },
        setActiveKey: (key: string[]) => {
            setDefaultActiveKey(key);
        },
    }));

    // 创建refs来存储子组件实例
    const formRefs = useRef<{ [key: string]: AddFlowItemExposeHandle }>({});
    // 优化的 ref 注册函数
    const registerFormRef = useCallback((jobId: string, el: AddFlowItemExposeHandle | null) => {
        if (el) {
            formRefs.current[jobId] = el;
        } else {
            // 如果传入 null，则删除引用
            delete formRefs.current[jobId];
        }
    }, []);

    const getDepartmentList = async () => {
        const res: RespParams<Department> = await (await getDepartmentListApi()).json();
        if (res.code === 200) {
            const origin = [res.data].map((item) => {
                if (item.dept_code === "100000") {
                    item.parent_dept_code = "-1";
                }
                return item;
            });
            const list = treeToArray(origin);
            list.forEach((item) => {
                item.isDisabled = item.dept_level === 90;
            });
            const treeData = arrayToTree(list, "dept_code", "parent_dept_code");
            setDepartmentList(treeData);
        }
    };

    const getEducationList = async () => {
        const res: RespParams<DictData[]> = await (await getDictDataByCodeApi("education")).json();
        if (res.code === 200) {
            setEducationList(res.data.map((item) => item.key));
        }
    };

    useEffect(() => {
        getDepartmentList();
        getEducationList();
    }, []);

    const handleAdd = (event: React.MouseEvent) => {
        event.stopPropagation();
        const newFlowItem = cloneDeep({ ...defaultFlowItem, jobId: nanoid(), jobTags: [...JobTags] });

        setFlowItems([...flowItems, newFlowItem]);
        setDefaultActiveKey([newFlowItem.jobId]);
    };

    const FlowHeader = ({ item }: { item: FlowItemParams }) => {
        const handleDelete = (event: React.MouseEvent) => {
            event.stopPropagation();

            if (flowItems.length === 1) return messageService.warning("至少存在一个需求！");

            const list = flowItems.filter((item2) => item2.jobId !== item.jobId);
            setFlowItems(list);
            setDefaultActiveKey([list[list.length - 1].jobId]);
        };

        return (
            <div className={styles["flow-header"]}>
                <span className={styles["flow-header-title"]}>
                    {(item?.orgName === "" ? null : item?.orgName) ?? "默认组织"}-
                    {(item?.jobNameInner === "" ? null : item?.jobNameInner) ?? "默认岗位"}
                </span>

                <Tooltip title="删除">
                    <DeleteOutlined style={{ color: "var(--danger-color)" }} onClick={(e) => handleDelete(e)} />
                </Tooltip>
            </div>
        );
    };

    // 使用函数方式创建 FlowItem ref 回调
    const createRefCallback = useCallback(
        (jobId: string) => {
            return (el: AddFlowItemExposeHandle | null) => {
                registerFormRef(jobId, el);
            };
        },
        [registerFormRef]
    );

    const items = useMemo(() => {
        return flowItems.map((item: FlowItemParams) => {
            return {
                key: item.jobId,
                label: <FlowHeader item={item} />,
                children: (
                    <FlowItem
                        ref={createRefCallback(item.jobId)}
                        item={item}
                        handleJobNameChange={(val) => {
                            const list = flowItems.map((item2) => {
                                if (item2.jobId === item.jobId) {
                                    item2.jobNameInner = val;
                                }
                                return item2;
                            });
                            setFlowItems(list);
                        }}
                        handleOrgChange={(val) => {
                            const list = flowItems.map((item2) => {
                                if (item2.jobId === item.jobId) {
                                    item2.orgName = val;
                                }
                                return item2;
                            });
                            setFlowItems(list);
                        }}
                        handleChangeLoading={handleChangeLoading}
                    />
                ),
            };
        });
    }, [flowItems, createRefCallback]);

    return (
        <div className={styles["add-flow-wrapper"]}>
            <Collapse
                items={items}
                bordered={false}
                activeKey={defaultActiveKey}
                onChange={(key) => {
                    setDefaultActiveKey(key);
                }}
            />
            <Button
                style={{ marginTop: 12 }}
                icon={<PlusOutlined />}
                block
                color="primary"
                variant="dashed"
                onClick={handleAdd}
            >
                新增需求
            </Button>
        </div>
    );
});
AddFlow.displayName = "AddFlow";

export default AddFlow;
