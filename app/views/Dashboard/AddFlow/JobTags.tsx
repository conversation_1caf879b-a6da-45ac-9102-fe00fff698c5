import React, { useEffect, useState } from "react";
import { Card, Select, Tag, Tooltip } from "antd";
import { CloseOutlined, CheckOutlined } from "@ant-design/icons";
import AddIcon from "@/app/icons/add.svg";
import styles from "./index.module.scss";
import { TagsItem, TagsResp } from "@/app/store/modules/dashboard/jobFlow";
import { getAllTagsApi } from "@/app/request/modules/dashboard";
import { RespParams } from "@/app/typing";

// TagItem 组件，用于渲染单个标签项
const TagItem = ({
    tagItem,
    tagsList,
    groupIndex,
    groupLabel,
    tagIndex,
    editable,
    options,
    handleChangeTags,
}: {
    tagItem: TagsItem;
    tagsList: TagsItem[];
    groupIndex: number;
    groupLabel: string;
    tagIndex: number;
    editable: boolean;
    options: TagsResp[];
    handleChangeTags: (tags: TagsItem[]) => void;
}) => {
    const [edit, setEdit] = useState(false);
    const [addTagsData, setAddTagsData] = useState<number[]>([]);

    useEffect(() => {
        setAddTagsData(tagsList?.[groupIndex]?.children?.[tagIndex]?.value?.map((item) => item.id) ?? []);
    }, [groupIndex, tagIndex, tagsList]);

    // 处理编辑状态
    const handleChangeEdit = async () => {
        setEdit(true);
    };

    const handleAddTags = (list: number[]) => {
        // 构建新的标签数据
        const newTags: TagsResp[] = [];
        list?.forEach((item) => {
            const result = options.find((option) => option.id === item);

            if (result) {
                newTags.push(result);
            }
        });
        if (Array.isArray(tagsList?.[groupIndex]?.children?.[tagIndex]?.value)) {
            const valueRef = tagsList?.[groupIndex]?.children?.[tagIndex];
            if (valueRef) {
                valueRef.value = newTags;
            }
        }

        handleChangeTags([...tagsList]);
        setAddTagsData(tagsList?.[groupIndex]?.children?.[tagIndex]?.value?.map((item) => item.id) ?? []);
        setEdit(false);
    };

    const handleCancel = () => {
        setAddTagsData(tagsList?.[groupIndex]?.children?.[tagIndex]?.value?.map((item) => item.id) ?? []);
        setEdit(false);
    };

    return (
        <div className={styles["tags-item-wrapper"]}>
            <div className={styles["tags-item-wrapper-label"]}>{tagItem.label}</div>
            <div className={styles["tags-item-wrapper-content"]}>
                {tagItem.value?.map((item, index) => (
                    <Tag
                        key={`value-${index}`}
                        closable={!edit && editable}
                        color="blue"
                        onClose={(e) => {
                            // 阻止默认tag删除事件，避免点击删除后同时触发tag删除事件与自定义删除事件
                            e.preventDefault();

                            const list = addTagsData.filter((item2) => item2 !== item.id);
                            handleAddTags(list);
                        }}
                    >
                        {item.name}
                    </Tag>
                ))}

                {!edit && editable && (
                    <Tooltip title="添加标签">
                        <AddIcon onClick={handleChangeEdit} />
                    </Tooltip>
                )}
                {edit && (
                    <div className={styles["tags-item-wrapper-content-edit"]}>
                        <Select
                            size="small"
                            maxTagCount={2}
                            popupMatchSelectWidth={false}
                            style={{ width: "100%" }}
                            options={options.filter(
                                (item2) => item2.label === tagItem.label && item2.layer === groupLabel
                            )}
                            fieldNames={{
                                label: "name",
                                value: "id",
                            }}
                            value={addTagsData}
                            showSearch
                            filterOption={(input, option) =>
                                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
                            }
                            mode="multiple"
                            allowClear
                            onChange={(value, selection) => setAddTagsData(value)}
                        />
                        <Tooltip title="取消">
                            <CloseOutlined style={{ color: "#ff4d4f", marginLeft: "12px" }} onClick={handleCancel} />
                        </Tooltip>
                        <Tooltip title="确认">
                            <CheckOutlined
                                style={{ color: "#52c41a", marginLeft: "12px" }}
                                onClick={() => handleAddTags(addTagsData)}
                            />
                        </Tooltip>
                    </div>
                )}
            </div>
        </div>
    );
};

// TagGroup 组件，用于渲染标签组
const TagGroup = ({
    group,
    groupIndex,
    tagsList,
    editable,
    options,
    handleChangeTags,
}: {
    group: TagsItem;
    groupIndex: number;
    tagsList: TagsItem[];
    editable: boolean;
    options: TagsResp[];
    handleChangeTags: (tags: TagsItem[]) => void;
}) => {
    return (
        <Card key={`group-${groupIndex}`} title={group.label} className={styles["tags-group-card"]}>
            <div>
                {group.children?.map((tagItem, tagIndex) => (
                    <TagItem
                        key={`tag-${groupIndex}-${tagIndex}`}
                        tagItem={tagItem}
                        tagsList={tagsList}
                        groupIndex={groupIndex}
                        groupLabel={group.label}
                        tagIndex={tagIndex}
                        editable={editable}
                        options={options}
                        handleChangeTags={handleChangeTags}
                    />
                ))}
            </div>
        </Card>
    );
};

// 主 JobTags 组件
const JobTags = ({
    tagsList,
    editable = true,
    handleChangeTags,
}: {
    tagsList: TagsItem[];
    editable?: boolean;
    handleChangeTags: (tags: TagsItem[]) => void;
}) => {
    const [options, setOptions] = useState<TagsResp[]>([]);

    const getAllTags = async () => {
        const res: RespParams<TagsResp[]> = await (await getAllTagsApi({ layer: "", label: "" })).json();
        if (res.code === 200) {
            setOptions(res.data);
        }
    };

    useEffect(() => {
        getAllTags();
    }, []);
    return (
        <div className={styles["job-tags-wrapper"]}>
            {tagsList.map((group: TagsItem, groupIndex: number) => (
                <TagGroup
                    key={`group-${groupIndex}`}
                    group={group}
                    groupIndex={groupIndex}
                    tagsList={tagsList}
                    editable={editable}
                    options={options}
                    handleChangeTags={handleChangeTags}
                />
            ))}
        </div>
    );
};

export default JobTags;
