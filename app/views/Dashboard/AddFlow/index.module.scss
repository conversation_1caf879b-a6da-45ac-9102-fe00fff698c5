.add-flow-wrapper {
    :global {
        .ant-collapse-header {
            display: flex;
            align-items: center;
        }
    }
    .flow-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .flow-header-title {
            font-weight: 600;
            font-size: 16px;
            color: var(--main-text-color);
        }
    }
}

.flow-item-wrapper {
    .flow-item {
        display: grid;
        grid-template-columns: repeat(4, minmax(0, 1fr));
        gap: 20px;
    }
}

.job-tags-wrapper {
    .tags-group-card {
        margin-bottom: 12px;
    }
    .tags-item-wrapper {
        display: flex;
        align-items: center;
        padding: 4px 0;

        .tags-item-wrapper-label {
            font-size: 14px;
            color: var(--main-text-color);
            width: 100px;
        }

        .tags-item-wrapper-content {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
            flex: 1;

            svg {
                cursor: pointer;
            }

            .tags-item-wrapper-content-edit {
                width: 200px;
                display: flex;
                align-items: center;
            }
        }
    }
}
