.tabs-wrapper {
    width: 100%;
    height: 100%;
    padding: 16px;
    box-sizing: border-box;

    :global {
        .ant-tabs {
            width: 100%;
            height: calc(100% - 32px);
            overflow: hidden;
        }
        .ant-tabs-content-holder {
            width: 100%;
            height: 100%;
            overflow: auto;
        }
    }
}

.dashboard-container {
    width: calc(100% - 32px);
    height: 100%;
    padding: 0 16px;
    display: flex;
    flex-direction: column;

    :global {
        .ant-btn {
            border-radius: 2px;
            border: 1px solid #ebebeb;
            height: auto;
            padding: 6px;

            svg {
                width: 16px;
                height: 16px;
                color: #9ea0a3;
            }
        }
    }
}

.title-content {
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title-content-title {
        color: #000;
        font-family: "PingFang SC";
        font-size: 20px;
        font-weight: 500;
        line-height: 20px;
    }
    .title-content-search {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .title-content-search-message {
        cursor: pointer;
    }
}

.card-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin: 20px 0;
    gap: 20px;
}

.top-content {
    width: 100%;
    height: 144px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;

    .top-content-item {
        border-radius: 8px;
        border: 1px solid rgba(0, 0, 0, 0.08);
        background: #fff;
        padding: 20px;
        box-sizing: border-box;
    }

    .top-content-item-left {
        width: 30.97%;
        height: 100%;
    }

    .top-content-item-center {
        width: 38.5%;
        height: 100%;
    }

    .top-content-item-right {
        width: 30.53%;
        height: 100%;
    }
}

.bottom-content {
    width: 100%;
    height: 328px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;

    .bottom-content-item {
        border-radius: 4px;
        border: 1px solid rgba(0, 0, 0, 0.08);
        background: #fff;
        width: 48.5%;
        height: 100%;
        padding: 20px;
        box-sizing: border-box;
    }

    .bottom-content-item-left {
        .bottom-content-item-left-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .bottom-content-item-left-title-right {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    }
    .bottom-content-item-right {
        .bottom-content-item-right-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .bottom-content-item-right-title-right {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    }
}

.table-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    background: #fff;
    overflow-y: auto;
    flex: 0 0 auto;

    .table-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 24px;
        color: var(--main-text-color);
        font-size: 18px;
        font-weight: 600;
    }
}

.text-title {
    color: var(---Text-4, rgba(0, 0, 0, 0.92));
    font-size: 18px;
    font-weight: 600;
}

.warning-text {
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.45);
    font-family: "PingFang SC";
    font-size: 14px;
    font-weight: 400;
    gap: 8px;
}

.flow-wrapper {
    &-header {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;

        &-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            border: 1px solid rgba(0, 0, 0, 0.08);
            border-radius: 4px;
            cursor: pointer;

            .item-icon {
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8px;
                border: 1px solid;
            }

            .item-title {
                font-size: 16px;
                font-weight: 500;
                color: rgba(0, 0, 0, 0.92);
                margin-bottom: 8px;
            }

            .item-content {
                font-size: 12px;
                color: rgba(0, 0, 0, 0.68);
            }
        }
    }

    &-content {
        // background: #fff;
        border-radius: 4px;
        padding: 0 16px;

        &-tabs {
            display: flex;
            gap: 32px;
            border-bottom: 1px solid #e5e6eb;
            padding: 0 16px;
            margin-bottom: 16px;

            .tabs-item {
                padding: 9px 0;
                font-size: 14px;
                color: #4e5969;
                cursor: pointer;
                position: relative;

                &.active {
                    color: #0099f2;
                    font-weight: 600;

                    &::after {
                        content: "";
                        position: absolute;
                        bottom: -1px;
                        left: 0;
                        width: 100%;
                        height: 2px;
                        background: #0099f2;
                    }
                }
            }
        }

        &-list {
            .list-item {
                display: flex;
                gap: 16px;
                padding: 16px;
                border: 1px solid #f5f5f5;
                border-radius: 4px;
                margin-bottom: 12px;
                cursor: pointer;

                &-left {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 8px;

                    .time {
                        font-size: 14px;
                        font-weight: 600;
                        color: rgba(0, 0, 0, 0.92);
                    }

                    .status {
                        line-height: 18px;
                        font-size: 12px;
                        color: #ff7214;
                    }
                }

                &-right {
                    flex: 1;

                    .title {
                        font-size: 14px;
                        font-weight: 600;
                        color: rgba(0, 0, 0, 0.92);
                        margin-bottom: 8px;
                    }

                    .info {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        font-size: 12px;
                        color: rgba(0, 0, 0, 0.68);

                        .owner {
                            display: flex;
                            align-items: center;
                            gap: 4px;

                            .avatar {
                                overflow: hidden;
                                width: 16px;
                                height: 16px;
                                background: #fcedd6;
                                border: 1px solid #fff;
                                border-radius: 35px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 12px;
                            }
                        }
                    }
                }

                .tags {
                    display: flex;
                    gap: 8px;
                    font-size: 12px;
                    color: #000;

                    .priority {
                        display: flex;
                        align-items: center;
                        gap: 8px;

                        &::before {
                            content: "";
                            width: 6px;
                            height: 6px;
                            background: #ff765f;
                            border-radius: 50%;
                        }
                    }
                    .progress {
                        display: flex;
                        align-items: center;
                    }
                }
            }
        }
    }

    width: 100%;
    height: 100%;

    .flow-wrapper-header {
        display: flex;
        align-items: center;
        gap: 20px;
        padding: 16px;

        .flow-wrapper-header-item {
            flex: 1;
            padding: 20px;
            border-radius: 4px;
            border: 1px solid rgba(0, 0, 0, 0.08);
            min-height: 70px;
            display: flex;
            align-items: center;
            cursor: pointer;

            .item-icon {
                border-radius: 8px;
                padding: 4px;
                border-width: 1px;
                border-style: solid;
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 20px;
            }

            .item-title {
                color: var(--main-text-color);
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 6px;
            }

            .item-content {
                color: var(--sub-text-color-2);
                font-size: 12px;
                font-weight: 400;
            }
        }
    }
}

.add-flow-dialog-wrapper {
    padding: 0 24px !important;
    :global {
        .ant-collapse {
            max-height: 55vh;
            overflow: auto;
        }
    }

    .upload-btn-wrapper {
        margin-bottom: 12px;
    }
}

.todo-wrapper {
    .flow-wrapper-content-list {
        max-height: 52vh;
        overflow: auto;
    }
    .todo-pagination {
        border-top: 1px solid var(--border-color);
        padding-top: 12px;
    }
}
