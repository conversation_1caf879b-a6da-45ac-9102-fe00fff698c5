import ChatDialog from "@/app/components/ChatDialog";
import { MyFlowResp } from "@/app/store/modules/dashboard/jobFlow";
import { Button, Collapse, Empty, Flex, InputNumber, Table } from "antd";
import { useEffect, useState } from "react";

const AddFlowDetailChildren = ({
    tableData,
    isEdit,
    allocationMethod,
}: {
    tableData: MyFlowResp[];
    isEdit: boolean;
    allocationMethod: number;
}) => {
    const [showJDDetail, setShowJDDetail] = useState(false);
    const [currentAllocationMethod, setCurrentAllocationMethod] = useState<number>();
    const [currentTableData, setCurrentTableData] = useState<MyFlowResp[]>([]);

    useEffect(() => {
        setCurrentTableData(tableData);
    }, [tableData]);
    useEffect(() => {
        setCurrentAllocationMethod(allocationMethod);
    }, [allocationMethod]);

    return (
        <Flex vertical gap={12}>
            <Flex justify="end">
                <Button type="primary" color="blue" onClick={() => setShowJDDetail(true)}>
                    查看岗位JD
                </Button>
            </Flex>
            <Table rowKey="id" pagination={false} dataSource={currentTableData}>
                <Table.Column title="需求部门" dataIndex="orgName" key="orgName" />
                <Table.Column title="需求人数" dataIndex="hiringPersonNum" key="hiringPersonNum" />
                <Table.Column
                    title="分配方式"
                    dataIndex="assignment "
                    key="assignment"
                    render={(_, record) => {
                        if (!isEdit) {
                            if (currentAllocationMethod === 1 || !currentAllocationMethod) {
                                return <div>平均分配</div>;
                            }
                            if (currentAllocationMethod === 2) {
                                return <div>手动分配</div>;
                            }
                            if (currentAllocationMethod === 3) {
                                return <div>{record.assignment}</div>;
                            }
                        } else {
                            if (currentAllocationMethod === 1 || !currentAllocationMethod) {
                                return <div key={record.id}>平均分配</div>;
                            }
                            if (currentAllocationMethod === 2) {
                                return <div key={record.id}>手动分配</div>;
                            }
                            if (currentAllocationMethod === 3) {
                                return (
                                    <InputNumber
                                        value={record.assignment}
                                        placeholder="请输入分配比例"
                                        min={1}
                                        precision={0}
                                        onChange={(val) => {
                                            setCurrentTableData((prev) => {
                                                prev?.forEach((item2) => {
                                                    if (item2.id === record.id) {
                                                        item2.assignment = val ?? 1;
                                                    }
                                                });
                                                return [...prev];
                                            });
                                        }}
                                    />
                                );
                            }
                        }
                    }}
                ></Table.Column>
            </Table>
            <ChatDialog
                width="50%"
                title="岗位需求JD"
                open={showJDDetail}
                confirmLoading={false}
                footer={null}
                onCancel={() => setShowJDDetail(false)}
            >
                <Collapse
                    defaultActiveKey={currentTableData?.map((item) => item.jobId)}
                    bordered={false}
                    items={currentTableData?.map((item) => {
                        return {
                            key: item.jobId,
                            label: (
                                <div style={{ fontWeight: "600" }}>
                                    {item.orgName}-{item.jobNameInner}
                                </div>
                            ),
                            children: item.jobJD ? (
                                <div style={{ whiteSpace: "pre-wrap" }}>{item.jobJD}</div>
                            ) : (
                                <Empty />
                            ),
                        };
                    })}
                ></Collapse>
            </ChatDialog>
        </Flex>
    );
};

export default AddFlowDetailChildren;
