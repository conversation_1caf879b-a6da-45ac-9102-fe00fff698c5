import { Button, Flex, Layout, Modal, Steps, Tag } from "antd";
import Header from "@/app/components/Header";
import ProTable, { ActionType, ProColumnType } from "@ant-design/pro-table";
import { useEffect, useMemo, useRef, useState } from "react";
import useJobFlowStore, { FlowDetailExposeHandle, MyFlowReq, MyFlowResp } from "@/app/store/modules/dashboard/jobFlow";
import {
    checkIsSameInterviewApi,
    deprecateJobFlowApi,
    exportInterviewFlowByJobSpecApi,
    exportJobSpecsApi,
    getMyJobListApi,
} from "@/app/request/modules/dashboard";
import { RespPaginationParams, RespParams } from "@/app/typing";
import { Department, Job } from "@/app/store/modules/dashboard/jobFlow";
import messageService from "@/app/lib/message";
import { nanoid } from "nanoid";
import { arrayToTree, getTableScroll, treeToArray } from "@/app/utils";
import ChatDrawer from "../../../components/ChatDrawer";
import Detail from "./Detail";
import { statusList } from "../constant";
import ChatDialog from "@/app/components/ChatDialog";
import { ExclamationCircleFilled } from "@ant-design/icons";
import { FormInstance } from "antd/lib";
import { BatchJobResp, JobDetailExpose } from "@/app/store/modules/dashboard/jobDetail";
import FlowDetail from "../FlowHistory/FlowDetail";
import { getDepartmentListApi, getJobListApi } from "@/app/request/modules/common";
import { useLocation, useNavigate } from "react-router-dom";
import { Path } from "@/app/constant";
import UseCandidateStore, { JobResp } from "@/app/store/modules/candidate";
import useKeepAliveStore from "@/app/store/modules/keepAlive";
import { usePathname } from "next/navigation";
import createKeepAliveStore from "@/app/store/modules/keepAlive";

const JobManage = () => {
    const navigate = useNavigate();
    const location = useLocation();

    const { departmentList, setDepartmentList } = useJobFlowStore((state) => ({
        departmentList: state.departmentList,
        setDepartmentList: state.setDepartmentList,
        flowItems: state.flowItems,
        setFlowItems: state.setFlowItems,
    }));
    const { setCurrentJob } = UseCandidateStore((state) => ({
        setCurrentJob: state.setCurrentJob,
    }));
    const { setCachedData, getCachedData } = useKeepAliveStore((state) => ({
        setCachedData: state.setCachedData,
        getCachedData: state.getCachedData,
    }));

    const [scrollY, setScrollY] = useState<number>();
    const [selectedData, setSelectedData] = useState<MyFlowResp[]>([]);
    const [showPublic, setShowPublic] = useState(false);
    const [detailInfo, setDetailInfo] = useState<MyFlowResp | null>(null);
    const [showJobDetail, setShowJobDetail] = useState<boolean>(false);
    const [detailLoading, setDetailLoading] = useState<boolean>(false);
    const [allJobList, setAllJobList] = useState<Job[]>([]);
    const [currentJobList, setCurrentJobList] = useState<Job[]>([]);
    // 点击详情获取当前需求流程ID
    const [currentFlowId, setCurrentFlowId] = useState<string>("");
    // 点击详情获取当前需求ID
    const [currentJobId, setCurrentJobId] = useState<number>();
    const [firstLoad, setFirstLoad] = useState<boolean>(true);

    const tableRef = useRef<ActionType>(null);
    const formRef = useRef<FormInstance>();
    const detailRef = useRef<JobDetailExpose>(null);
    const flowDetailRef = useRef<FlowDetailExposeHandle>(null);

    const columns: ProColumnType<MyFlowResp>[] = [
        {
            title: "职位名称",
            dataIndex: "jobNameInner",
            width: 300,
            ellipsis: true,
            valueType: "cascader",
            fieldProps: {
                expandTrigger: "hover",
                placeholder: "请选择职位名称",
                showSearch: {
                    limit: Infinity,
                },
                options: allJobList,
                changeOnSelect: true,
                fieldNames: {
                    label: "desc",
                    value: "code",
                },
                onChange: (val: string[], options: Job[]) => {
                    setCurrentJobList(options);
                },
            },
            render: (_, record) => {
                return (
                    <Button
                        type="link"
                        onClick={(e) => {
                            e.stopPropagation();
                            if (record.id) {
                                setCurrentJobId(record?.id);
                            }
                            handleDetail(record.flowInstanceId);
                        }}
                    >
                        {record.jobNameInner}
                    </Button>
                );
            },
        },
        {
            title: "招聘负责人",
            dataIndex: "createUser",
            search: false,
            width: 200,
            render: (_, record) => {
                return record.createUser?.empId + "-" + record.createUser?.empName;
            },
        },
        {
            title: "需求部门",
            dataIndex: "orgName",
            order: 1,
            ellipsis: true,
            width: 300,
            valueType: "cascader",
            fieldProps: {
                expandTrigger: "hover",
                placeholder: "请选择需求部门",
                showSearch: {
                    limit: Infinity,
                },
                options: departmentList,
                changeOnSelect: true,
                fieldNames: {
                    label: "dept_name",
                    value: "dept_code",
                },
                optionRender: (option: Department) => {
                    return (
                        <div
                            onClick={(e) => {
                                // 禁用节点，禁止点击后选中
                                if (option.isDisabled) {
                                    e.stopPropagation();
                                }
                            }}
                            style={{
                                color: option.isDisabled ? "var(--sub-text-color-2)" : "inherit",
                                cursor: option.isDisabled ? "not-allowed" : "pointer",
                            }}
                        >
                            {option.dept_name}
                        </div>
                    );
                },
                onBlur: async () => {
                    const value = formRef.current?.getFieldValue("orgName");
                    if (value && value.length > 0) {
                        const code = value?.[value?.length - 1];
                        const res: RespParams<Job[]> = await (await getJobListApi({ orgNo: code })).json();
                        if (res.code === 200) {
                            setAllJobList(res.data);
                        }
                    }
                },
                onChange: (value) => {
                    if (!value) {
                        setAllJobList([]);
                        formRef.current?.setFieldsValue({ jobNameInner: undefined });
                    }
                },
            },
        },
        {
            title: "招聘人数",
            dataIndex: "hiringPersonNum",
            search: false,
            minWidth: 100,
        },
        {
            title: "状态",
            dataIndex: "status",
            valueType: "select",
            width: 120,
            render: (_, record) => {
                const obj = statusList.find((item) => item.value === record.status);
                return <Tag color={obj?.type}>{obj?.label}</Tag>;
            },
            fieldProps: {
                placeholder: "请选择状态",
                options: [...statusList],
            },
        },
        {
            title: "面试流程",
            dataIndex: "stageNames",
            search: false,
            minWidth: 300,
            render: (_, record) => {
                if (!record?.stageNames) {
                    return "-";
                } else {
                    const items = record.stageNames.map((item: string) => {
                        return {
                            title: item,
                        };
                    });
                    return <Steps progressDot current={record?.stageNames?.length} items={items} />;
                }
            },
        },
        {
            title: "新增应聘者",
            dataIndex: "addInterviewers",
            search: false,
            minWidth: 100,
        },
        {
            title: "所有应聘者",
            dataIndex: "allInterviewers",
            search: false,
            minWidth: 100,
        },
        {
            title: "创建时间",
            dataIndex: "createTime",
            search: false,
            ellipsis: true,
            width: 200,
        },
        {
            title: "更新时间",
            dataIndex: "updateTime",
            search: false,
            ellipsis: true,
            width: 200,
        },
        {
            title: "操作",
            fixed: "right",
            width: 150,
            valueType: "option",
            dataIndex: "id",
            render: (text, record) => [
                <Flex align="center" key={record.id}>
                    <Button
                        key={record.id}
                        size="small"
                        color="primary"
                        variant="link"
                        disabled={record.status === -1}
                        onClick={(e) => handleShowDetail(record, e)}
                    >
                        详情
                    </Button>
                    <Button
                        color="danger"
                        variant="link"
                        disabled={record.status === -1}
                        onClick={async (e) => {
                            e.stopPropagation();

                            const result = await new Promise((resolve) => {
                                Modal.confirm({
                                    title: "是否废弃该需求?",
                                    icon: <ExclamationCircleFilled />,
                                    content: "点击确定后废弃，关闭弹窗",
                                    onOk() {
                                        return new Promise((resolve2, reject2) => {
                                            deprecateJobFlowApi({
                                                type: 1,
                                                ids: [record.id ?? 0],
                                            })
                                                .then((response) => response.json())
                                                .then((res: any) => {
                                                    if (res.code === 200) {
                                                        messageService.success("审批废弃");
                                                        resolve2(true);
                                                        resolve(true);
                                                    } else {
                                                        reject2(false);
                                                    }
                                                })
                                                .catch((err) => {
                                                    reject2(false);
                                                });
                                        });
                                    },
                                    onCancel() {
                                        resolve(false);
                                    },
                                });
                            });
                            if (result) {
                                tableRef.current?.reset?.();
                            }
                        }}
                    >
                        废弃
                    </Button>
                </Flex>,
            ],
        },
    ];

    const getDepartmentList = async () => {
        const res: RespParams<Department> = await (await getDepartmentListApi()).json();
        if (res.code === 200) {
            const origin = [res.data].map((item) => {
                if (item.dept_code === "100000") {
                    item.parent_dept_code = "-1";
                }
                return item;
            });
            const list = treeToArray(origin);
            list.forEach((item) => {
                item.isDisabled = item.dept_level === 90;
            });
            const treeData = arrayToTree(list, "dept_code", "parent_dept_code");
            setDepartmentList(treeData);
        }
    };

    useEffect(() => {
        getDepartmentList();
        setScrollY(getTableScroll());
    }, []);
    useEffect(() => {
        setScrollY(getTableScroll());
    }, [selectedData]);
    useEffect(() => {
        if (location.pathname) {
            if (currentCached) {
                formRef.current?.setFieldsValue(currentCached);
                formRef.current?.submit();
            } else {
                tableRef.current?.reset?.();
            }
        }
    }, [location.pathname]);
    const currentCached = useMemo(() => {
        return getCachedData(location.pathname);
    }, [location.pathname]);

    // 发布需求
    const handlePublish = async () => {
        if (selectedData.length === 0) {
            messageService.warning("请先选中要配置的职位");
            return;
        }

        const jobNameInnerSet = new Set(selectedData.map((item) => item?.jobNameInner));
        if (jobNameInnerSet.size > 1) {
            messageService.error("岗位名称不一致，请选择相同岗位名称");
            return;
        }
        const disabledStatusList = selectedData.filter((item) => item?.status === 0);
        if (disabledStatusList.length > 0) {
            messageService.error("存在未通过审批的职位，无法进行配置");
            return;
        }

        try {
            const res: RespParams<BatchJobResp[]> = await (
                await checkIsSameInterviewApi(selectedData?.map((item) => item?.id ?? -1))
            ).json();

            if (res.code === 200) {
                if (res.data.length === 0) {
                    messageService.error("选中的职位存在不同的面试流程数据，不允许配置");
                } else {
                    setDetailInfo({
                        jobId: nanoid(),
                        id: selectedData[0]?.id,
                        jobNameInner: selectedData[0]?.jobNameInner,
                        jobNameInnerPath: selectedData[0]?.jobNameInnerPath,
                        jobNameOuter: "",
                        orgId: selectedData[0]?.orgId,
                        orgName: "",
                        hiringBatch: "",
                        hiringPersonNum: null,
                        offerTarget: null,
                        topTalents: null,
                        excellentTalents: null,
                        regularTalents: null,
                        jobType: null,
                        jobLocation: [],
                        educational: [],
                        jobJD: "",
                        assignment: 1,
                        status: 0,
                        createUserId: "",
                        tags: [],
                        flowInstanceId: "",
                        createUser: {
                            empId: "",
                            empName: "",
                            avatar: "",
                            email: "",
                        },
                    });
                    setShowPublic(true);

                    const timer = setTimeout(() => {
                        detailRef.current?.init(res.data ?? [], "batch");
                        clearTimeout(timer);
                    }, 100);
                }
            }
        } catch (error: any) {
            console.log("error", error);
        }
    };

    const handleChangeSelectedData = (row: any) => {
        if (Array.isArray(row)) {
            setSelectedData(row);
        } else {
            const current = selectedData.find((item) => item.id === row.id);
            if (current) {
                setSelectedData((prev) => prev.filter((item) => item.id !== row.id));
            } else {
                setSelectedData([...selectedData, row]);
            }
        }
    };

    const handleShowDetail = (row: MyFlowResp, event: React.MouseEvent) => {
        event.stopPropagation();
        setCurrentJob(row as JobResp);
        navigate(Path.JobDetail, {
            state: {
                flowInstanceId: row.flowInstanceId,
                jobId: row.id,
                orgId: row.orgId,
            },
        });

        // setDetailInfo(row);
        // setShowPublic(true);
        // const timer = setTimeout(() => {
        //     detailRef.current?.init();
        //     clearTimeout(timer);
        // }, 100);
    };

    const handleExport = () => {
        const formValue = formRef.current?.getFieldsValue();
        const params = { orgId: "", position: "", status: "", outPosition: "", fileName: "职位管理.xlsx" };
        params.orgId = formValue.orgName?.[formValue?.orgName?.length - 1] ?? "";
        if (formValue.jobNameInner) {
            params.position = currentJobList?.map((item) => item.desc)?.join("/") ?? "";
        } else {
            params.position = "";
        }
        params.status = formValue?.status ?? "";
        params.outPosition = formValue?.jobNameOuter ?? "";

        exportJobSpecsApi(params);
    };

    const handleExportInterviewFlow = () => {
        if (selectedData.length === 0) return messageService.warning("请先选择要导出的数据！");

        exportInterviewFlowByJobSpecApi({
            ids: selectedData.map((item) => item.id).filter((id): id is number => id !== undefined),
            fileName: "面试流程配置.xlsx",
        });
    };

    // 获取详情数据
    const handleDetail = async (flowInstanceId: string) => {
        setCurrentFlowId(flowInstanceId);
        setShowJobDetail(true);
        const timer = setTimeout(() => {
            flowDetailRef.current?.refresh();
            clearInterval(timer);
        }, 100);
    };

    return (
        <Layout>
            <Header title="职位管理" />
            <Layout.Content>
                <ProTable
                    rowKey="id"
                    formRef={formRef}
                    actionRef={tableRef}
                    columns={columns}
                    scroll={{ x: "max-content", y: scrollY }}
                    options={{ reload: false }}
                    manualRequest={true}
                    pagination={{
                        defaultCurrent: 1,
                        defaultPageSize: 10,
                        hideOnSinglePage: false,
                        showSizeChanger: true,
                        pageSizeOptions: [10, 20, 50, 100],
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    toolBarRender={() => [
                        <Button key="publish" type="primary" onClick={handlePublish}>
                            配置
                        </Button>,
                        <Button key="export" type="primary" onClick={handleExport}>
                            导出职位
                        </Button>,
                        <Button key="export" type="primary" onClick={handleExportInterviewFlow}>
                            导出面试流程
                        </Button>,
                    ]}
                    rowSelection={{
                        type: "checkbox",
                        selectedRowKeys: selectedData.map((item) => item?.id ?? 0),
                        preserveSelectedRowKeys: true,
                        onChange: (_, selectedRows) => {
                            handleChangeSelectedData(selectedRows);
                        },
                    }}
                    onRow={(record) => {
                        return {
                            onClick: () => {
                                handleChangeSelectedData(record);
                            },
                        };
                    }}
                    request={async (params) => {
                        const form: MyFlowReq = {
                            page: (params.current ?? 1) - 1,
                            size: params.pageSize ?? 10,
                        };
                        form.orgId = params.orgName?.[params?.orgName?.length - 1] ?? "";
                        if (params.jobNameInner) {
                            form.position = currentJobList?.map((item) => item.desc)?.join("/") ?? "";
                        } else {
                            form.position = "";
                        }
                        form.status = params.status ?? "";
                        setCachedData(location.pathname, form);
                        const res: RespPaginationParams<MyFlowResp> = await (await getMyJobListApi(form)).json();
                        setFirstLoad(false);

                        if (res.code === 200) {
                            const list: MyFlowResp[] = res.data?.records ?? [];

                            return {
                                data: list.map((item) => {
                                    item.assignment = 1;
                                    return item;
                                }),
                                success: true,
                                total: res.data?.total,
                            };
                        }

                        return {
                            data: [],
                            success: false,
                            total: 0,
                        };
                    }}
                    onSubmit={() => tableRef.current?.clearSelected?.()}
                    onReset={() => tableRef.current?.clearSelected?.()}
                />
            </Layout.Content>
            <ChatDrawer
                title="配置面试流程"
                width={1000}
                open={showPublic}
                confirmLoading={false}
                onCancel={() => {
                    setShowPublic(false);
                    tableRef.current?.reload?.();
                }}
                onOk={() => {}}
                showFooter={false}
            >
                <Detail
                    ref={detailRef}
                    detailInfo={detailInfo}
                    ids={selectedData.map((item) => item?.id ?? -1) ?? []}
                    onCancel={() => {
                        setShowPublic(false);
                    }}
                    onOk={() => {
                        setShowPublic(false);
                        tableRef.current?.reload?.();
                    }}
                />
            </ChatDrawer>
            <ChatDialog
                width="75%"
                title="需求详情"
                open={showJobDetail}
                confirmLoading={detailLoading}
                footer={null}
                onCancel={() => {
                    tableRef.current?.reload?.();
                    setShowJobDetail(false);
                }}
            >
                <FlowDetail
                    ref={flowDetailRef}
                    flowId={currentFlowId}
                    jobId={currentJobId}
                    handleChangeLoading={(val) => setDetailLoading(val)}
                    type="JobFlowTodo"
                    mode="editChildrenJob"
                />
            </ChatDialog>
        </Layout>
    );
};

export default JobManage;
