import { Button, Form, Select } from "antd";
import { forwardRef, useImperativeHandle, useRef, useState } from "react";
import UserSelect from "@/app/components/UserSelect";
import ChatDialog from "@/app/components/ChatDialog";
import { RespUser, UserSelectExpose } from "@/app/components/typing";
import messageService from "@/app/lib/message";
import styles from "./index.module.scss";
import { DictData } from "@/app/typing";
import { AddInterviewExpose } from "@/app/store/modules/dashboard/jobDetail";

const AddInterview = forwardRef<AddInterviewExpose, { interviewTypeList: DictData[]; interviewTemplateList: any[] }>(
    (props, ref) => {
        const { interviewTypeList, interviewTemplateList } = props;

        useImperativeHandle(ref, () => ({
            init: (record?: any) => {
                userSelectRef.current?.tableRef?.clearSelected?.();
                if (record) {
                    let selected: RespUser[] = [];
                    const interviewerList = record.interviewers?.split(",").filter((item: string) => Bo<PERSON>an(item));
                    if (interviewerList.length > 0) {
                        selected = interviewerList?.map((item: string) => ({
                            empId: item.split("-")[0],
                            empName: item.split("-")[1],
                        }));
                    }

                    setSelectedUserData(selected);
                    form.setFieldsValue({
                        stageName: record.stageName,
                        beisenTemplateId: record.beisenTemplateId,
                    });
                } else {
                    setSelectedUserData([]);
                    userSelectRef.current?.tableRef?.clearSelected?.();
                    form.resetFields();
                }
            },
            form: form,
            selectedUserData: selectedUserData,
        }));

        const [selectedUserData, setSelectedUserData] = useState<RespUser[]>([]);
        const [showUserSelect, setShowUserSelect] = useState(false);

        const userSelectRef = useRef<UserSelectExpose>(null);

        const [form] = Form.useForm();
        const stageName = Form.useWatch("stageName", form);

        const handleSelectUser = () => {
            const user = userSelectRef.current?.getSelectedUser;

            if (user) {
                setSelectedUserData(user);
                setShowUserSelect(false);
            } else {
                messageService.warning("请选择面试官！");
            }
        };

        return (
            <div className={styles["add-interview"]}>
                <Form form={form} layout="vertical">
                    <Form.Item label="面试类型" name="stageName" rules={[{ required: true }]}>
                        <Select
                            placeholder="请选择面试类型"
                            options={interviewTypeList}
                            fieldNames={{ label: "key", value: "key" }}
                            onChange={() => {
                                if (form.getFieldValue("stageName") === "简历初筛") {
                                    form.setFieldsValue({ beisenTemplateId: null });
                                }
                            }}
                        />
                    </Form.Item>
                    <Form.Item
                        label="面试评价表"
                        name="beisenTemplateId"
                        dependencies={["stageName"]}
                        rules={[
                            {
                                required: stageName !== "简历初筛",
                            },
                        ]}
                    >
                        <Select
                            disabled={stageName === "简历初筛"}
                            placeholder="请选择面试评价表"
                            options={interviewTemplateList}
                        />
                    </Form.Item>
                </Form>

                {selectedUserData && (
                    <div className={styles["add-interview__user"]}>
                        {selectedUserData?.map((item: RespUser) => (
                            <span key={item.empId}>
                                {item.empName}-{item.empId}
                            </span>
                        ))}
                    </div>
                )}
                <Button
                    className={styles["add-interview__btn"]}
                    type="primary"
                    onClick={() => {
                        setShowUserSelect(true);

                        const timer = setTimeout(() => {
                            userSelectRef.current?.tableRef?.reset?.();
                            userSelectRef.current?.setSelectedUser?.(selectedUserData);
                            clearTimeout(timer);
                        }, 100);
                    }}
                >
                    选择面试官
                </Button>
                <ChatDialog
                    open={showUserSelect}
                    title="选择面试官"
                    width="75%"
                    onOk={handleSelectUser}
                    onCancel={() => setShowUserSelect(false)}
                    confirmLoading={false}
                >
                    <UserSelect mode="checkbox" ref={userSelectRef} />
                </ChatDialog>
            </div>
        );
    }
);

AddInterview.displayName = "AddInterview";

export default AddInterview;
