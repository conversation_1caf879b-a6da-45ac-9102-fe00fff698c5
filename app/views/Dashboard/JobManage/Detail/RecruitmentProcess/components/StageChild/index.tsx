import React, { ChangeEvent, useMemo, useState, useRef, useEffect } from "react";
import { Input, Checkbox, Popover, Button, Form, InputNumber, Space, Select, CheckboxChangeEvent } from "antd";
import { draggable, dropTargetForElements } from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { combine } from "@atlaskit/pragmatic-drag-and-drop/combine";
import { DropIndicator } from "@atlaskit/pragmatic-drag-and-drop-react-drop-indicator/box";
import DraggableItem from "../DraggableItem";
import styles from "./index.module.scss";
import { PlusOutlined } from "@ant-design/icons";
import PlusPopover from "../PlusPopover";
import { useFlowActions } from "../../../../../hooks/useFlowActions";
import { setCustomNativeDragPreview } from "@atlaskit/pragmatic-drag-and-drop/element/set-custom-native-drag-preview";
import { pointerOutsideOfPreview } from "@atlaskit/pragmatic-drag-and-drop/element/pointer-outside-of-preview";
import {
    getStatusItemData,
    isDragData,
    attachClosestEdge,
    extractClosestEdge,
    idleState,
    draggingState,
    type DraggableState,
    type Edge,
} from "../../dragUtils";
import clsx from "clsx";
import { createPortal } from "react-dom";
import useJobDetailStore from "@/app/store/modules/dashboard/jobDetail";

/**
 * 阶段状态数据接口
 */
interface StageStatusData {
    /** 状态唯一标识 */
    id: string;
    /** 状态编号 */
    code: string;
    /** 状态名称 */
    name: string;
    /** 对外显示名称 */
    alias: string;
    /** 是否必填原因 */
    requireReason: boolean;
    /** 是否锁定应聘者 */
    lockCandidate: boolean;
}

/**
 * 阶段子状态管理组件属性接口
 */
interface StageChildProps {
    /** 子状态列表 */
    subStatuses?: StageStatusData[];
    stage: any;
    readonly?: boolean;
}

/**
 * 阶段子状态管理组件
 * @param props 组件属性
 * @returns 阶段子状态组件
 */
const StageChild: React.FC<StageChildProps> = ({ subStatuses = [], stage, readonly = false }) => {
    const { deleteStageStatus, updateStageStatus } = useFlowActions();

    const handleDelete = (statusCode: string) => {
        deleteStageStatus(stage.dictId, statusCode);
    };

    const handleReasonRequired = (e: CheckboxChangeEvent, statusItem: any) => {
        const isChecked = e.target.checked;
        updateStageStatus(stage.dictId, statusItem.dictId, { requireReason: isChecked });
    };

    const handleOuterNameChange = (e: ChangeEvent<HTMLInputElement>, statusItem: any) => {
        const outerName = e.target.value;
        updateStageStatus(stage.dictId, statusItem.dictId, { outerName });
    };

    return (
        <div className={styles["child-container"]}>
            <div className={styles["child-container-header"]}>
                <span className={styles["code-label"]}>编号</span>
                <span className={styles["name-label"]}>状态名称</span>
                <span className={styles["alias-label"]}>对外名称</span>
                <span className={styles["reason-label"]}>原因必填</span>
                <span className={styles["sill-label"]}>职位锁定应聘者</span>
            </div>
            <div className={styles["child-container-content"]}>
                {subStatuses.map((statusItem: any, index: number) => {
                    return (
                        <React.Fragment key={statusItem.id}>
                            <StatusItem
                                statusItem={statusItem}
                                index={index}
                                stageId={stage.dictId}
                                onDelete={() => handleDelete(statusItem.dictId)}
                                onReasonRequired={(e) => handleReasonRequired(e, statusItem)}
                                onOuterNameChange={(e) => handleOuterNameChange(e, statusItem)}
                                stage={stage}
                                readonly={readonly}
                            />
                        </React.Fragment>
                    );
                })}
                {!readonly && (
                    <div>
                        <PlusButton stage={stage} subStatuses={subStatuses} />
                    </div>
                )}
            </div>
        </div>
    );
};

export default StageChild;

interface StatusItemProps {
    statusItem: any;
    index: number;
    stageId: string;
    onDelete: () => void;
    onReasonRequired: (e: CheckboxChangeEvent) => void;
    onOuterNameChange: (e: ChangeEvent<HTMLInputElement>) => void;
    stage: any;
    readonly: boolean;
}

const StatusItem: React.FC<StatusItemProps> = ({
    statusItem,
    index,
    stageId,
    onDelete,
    onReasonRequired,
    onOuterNameChange,
    stage,
    readonly,
}) => {
    const ref = useRef<HTMLDivElement>(null);
    const dragHandleRef = useRef<HTMLDivElement>(null);
    const [draggableState, setDraggableState] = useState<DraggableState>(idleState);
    const [closestEdge, setClosestEdge] = useState<Edge | null>(null);

    useEffect(() => {
        const element = ref.current;
        const dragHandle = dragHandleRef.current;
        if (!element || !dragHandle) return;

        const dragData = getStatusItemData({ item: statusItem, stageId, index });

        return combine(
            draggable({
                element: dragHandle,
                getInitialData: () => dragData,
                onGenerateDragPreview({ nativeSetDragImage }) {
                    setCustomNativeDragPreview({
                        nativeSetDragImage,
                        getOffset: pointerOutsideOfPreview({
                            x: "16px",
                            y: "8px",
                        }),
                        render({ container }) {
                            setDraggableState({ type: "preview", container });
                            return () => setDraggableState(draggingState);
                        },
                    });
                },
                onDragStart() {
                    setDraggableState(draggingState);
                },
                onDrop() {
                    setDraggableState(idleState);
                },
            }),
            dropTargetForElements({
                element,
                canDrop({ source }) {
                    return isDragData(source.data) && source.data.type === "status" && source.data.stageId === stageId;
                },
                getData({ input }) {
                    return attachClosestEdge(dragData, {
                        element,
                        input,
                        allowedEdges: ["top", "bottom"],
                    });
                },
                getIsSticky() {
                    return true;
                },
                onDragEnter({ source, self }) {
                    if (source.element === dragHandle) {
                        setClosestEdge(null);
                        return;
                    }
                    const edge = extractClosestEdge(self.data);
                    setClosestEdge(edge);
                },
                onDrag({ source, self }) {
                    if (source.element === dragHandle) {
                        setClosestEdge(null);
                        return;
                    }
                    const edge = extractClosestEdge(self.data);
                    setClosestEdge(edge);
                },
                onDragLeave() {
                    setClosestEdge(null);
                },
                onDrop() {
                    setClosestEdge(null);
                },
            })
        );
    }, [statusItem, index, stageId]);

    return (
        <>
            <div ref={ref} style={{ position: "relative" }}>
                <DraggableItem
                    draggable={true}
                    deletable={true}
                    readonly={readonly}
                    size="small"
                    className={clsx(
                        styles["draggable-content"],
                        draggableState.type === "dragging" ? styles["draggable-content-dragging"] : ""
                    )}
                    handleDelete={onDelete}
                    dragHandleRef={dragHandleRef}
                    style={{
                        opacity: draggableState.type === "dragging" ? 0.5 : 1,
                        cursor: draggableState.type === "dragging" ? "grabbing" : "default",
                    }}
                >
                    <div className={styles["child-container-content-item"]}>
                        <span className={styles["code-value"]}>{statusItem?.trackStateDict?.code}</span>
                        <span className={styles["name-value"]}>{statusItem?.trackStateDict?.name}</span>
                        <span className={styles["alias-value"]}>
                            <Input
                                placeholder="请输入中文"
                                value={statusItem.outerName}
                                onChange={onOuterNameChange}
                                disabled={readonly}
                            />
                        </span>
                        <span className={styles["reason-value"]}>
                            <Checkbox
                                checked={statusItem.requireReason}
                                onChange={onReasonRequired}
                                disabled={readonly}
                            />
                            <span style={{ marginLeft: 5 }}>是否必填</span>
                        </span>
                        <span className={styles["sill-value"]}>
                            <LockApplicant stage={stage} statusItem={statusItem} readonly={readonly} />
                        </span>
                    </div>
                </DraggableItem>
                {closestEdge && <DropIndicator edge={closestEdge} gap="8px" />}
            </div>
            {draggableState.type === "preview" &&
                createPortal(<DragPreview status={statusItem} />, draggableState.container)}
        </>
    );
};

interface LockApplicantProps {
    statusItem: any;
    stage: any;
    readonly?: boolean;
    // statusItem: StageStatusData;
}

// 锁定应聘者
const LockApplicant = ({ stage, statusItem, readonly }: LockApplicantProps) => {
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    const { updateStageStatus } = useFlowActions();

    // lockType 1 定时锁定  2 永久锁定
    const isChecked = statusItem.lockType === 1 || statusItem.lockType === 2;

    const lockTitle = isChecked
        ? statusItem.lockType === 1
            ? statusItem.unlockDays + "天"
            : "永久锁定"
        : "锁定应聘者";

    /** 处理显示/隐藏 popover */
    const handleOpenChange = () => {
        if (readonly) return;
        setOpen((open) => !open);
    };

    /** 处理表单提交 */
    const handleConfirm = async () => {
        try {
            const values = await form.validateFields();
            updateStageStatus(stage.dictId, statusItem.dictId, values);
            // 这里可以添加提交逻辑
            setOpen(false);
        } catch (error) {
            console.error("表单验证失败:", error);
        }
    };

    /** 处理取消操作 */
    const handleCancel = () => {
        form.resetFields();
        setOpen(false);
    };

    const handleClickCheckbox = (e: CheckboxChangeEvent) => {
        if (readonly) return;
        const isChecked = e.target.checked;
        //isChecked 为false，表明原来为选中状态
        if (!isChecked) {
            updateStageStatus(stage.dictId, statusItem.dictId, {
                lockType: 0,
                unlockDays: 0,
            });
        } else {
            setOpen(true);
        }
    };

    /** popover 内容 */
    const content = (
        <div style={{ width: 280 }}>
            <Form
                form={form}
                layout="vertical"
                initialValues={{ lockType: statusItem.lockType || 2, unlockDays: statusItem.unlockDays }}
            >
                <Form.Item label="锁定时效" name="lockType" rules={[{ required: true, message: "请输入锁定时效" }]}>
                    <Select
                        size="small"
                        options={[
                            { label: "永久锁定", value: 2 },
                            { label: "自定义锁定时长", value: 1 },
                        ]}
                    />
                </Form.Item>
                <Form.Item shouldUpdate noStyle>
                    {({ getFieldValue }) => {
                        const lockType = getFieldValue("lockType");
                        // 只有当 lockType 不是永久锁定(2)时才显示自动解锁天数
                        return lockType !== 2 ? (
                            <Form.Item
                                label="自动解锁天数"
                                name="unlockDays"
                                style={{ width: 150 }}
                                rules={[
                                    { required: true, message: "请输入自动解锁天数" },
                                    { type: "number", min: 1, message: "天数必须大于0" },
                                ]}
                            >
                                <InputNumber size="small" placeholder="请输入天数" min={1} precision={0} />
                            </Form.Item>
                        ) : null;
                    }}
                </Form.Item>
                <Form.Item style={{ marginBottom: 0 }}>
                    <Space className={styles["popover-footer"]}>
                        <Button size="small" onClick={handleCancel}>
                            取消
                        </Button>
                        <Button size="small" type="primary" onClick={handleConfirm}>
                            确认
                        </Button>
                    </Space>
                </Form.Item>
            </Form>
        </div>
    );

    return (
        <Popover fresh content={content} trigger={readonly ? [] : "click"} open={readonly ? false : open}>
            <div
                style={{
                    cursor: readonly ? "default" : "pointer",
                    opacity: readonly ? 0.7 : 1,
                    display: "flex",
                    alignItems: "center",
                }}
            >
                <Checkbox checked={isChecked} onChange={handleClickCheckbox} disabled={readonly} />
                <span
                    onClick={handleOpenChange}
                    style={{
                        marginLeft: 5,
                        color: readonly ? (isChecked ? "#0054f0" : "#666") : isChecked ? "#0054f0" : "black",
                        cursor: readonly ? "default" : "pointer",
                    }}
                >
                    {lockTitle}
                </span>
            </div>
        </Popover>
    );
};

/**
 * 状态选择器组件
 */
const PlusButton = ({ stage, subStatuses }: { stage: any; subStatuses: any[] }) => {
    const { statusList } = useJobDetailStore();
    const { addStatus } = useFlowActions();

    const noSelectedStatusList = useMemo(() => {
        const selectedStatusCodeList = subStatuses?.map((item) => item.dictId);
        return statusList.filter((item) => !selectedStatusCodeList?.includes(item.code));
    }, [statusList, subStatuses]);

    /** 处理状态选择 */
    const handleStatusSelect = (item: { id: string; code: string; name: string }) => {
        console.log("选择状态:", item);
        addStatus(stage.dictId, {
            dictId: item.code,
            trackStateDict: item,
            lockType: null,
            unlockDays: null,
            requireReason: false,
            outerName: "",
        });
    };

    /** 处理新建状态 */
    const handleCreateItem = () => {
        console.log("新建状态");
    };

    return (
        <PlusPopover
            createItemLabel="新建并选择状态"
            list={noSelectedStatusList}
            onSelect={handleStatusSelect}
            onCreateItem={handleCreateItem}
            creatable={false}
        >
            <Button size="small" icon={<PlusOutlined />} className={styles["plus-btn"]}>
                选择状态
            </Button>
        </PlusPopover>
    );
};

function DragPreview({ status }: { status: any }) {
    return <div className={styles["drag-preview"]}>{status.trackStateDict?.name}</div>;
}
