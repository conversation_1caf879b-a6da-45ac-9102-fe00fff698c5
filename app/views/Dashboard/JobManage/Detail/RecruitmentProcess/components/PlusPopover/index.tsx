import { Input, List, Space, Button, Popover } from "antd";
import { useState } from "react";
import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";

interface PlusPopoverProps {
    children: React.ReactNode;
    createItemLabel: string;
    list: Array<ListItem>;
    creatable?: boolean;
    onSelect: (item: ListItem) => void;
    onCreateItem: () => void;
}

interface ListItem {
    id: string;
    code: string;
    name: string;
}
/**
 * 状态选择器组件
 */
const PlusPopover: React.FC<PlusPopoverProps> = ({
    children,
    createItemLabel,
    list,
    onSelect,
    onCreateItem,
    creatable = true,
}) => {
    const [open, setOpen] = useState(false);
    const [searchValue, setSearchValue] = useState("");

    /** 处理显示/隐藏 popover */
    const handleOpenChange = (newOpen: boolean) => {
        setOpen(newOpen);
        if (!newOpen) {
            setSearchValue("");
        }
    };

    /** 处理搜索输入变化 */
    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchValue(e.target.value);
    };

    /** 处理状态选择 */
    const handleStatusSelect = (item: ListItem) => {
        onSelect(item);
        setOpen(false); // 在选择后关闭气泡
    };

    /** 处理新建并选择状态 */
    const handleCreateAndSelect = () => {
        setOpen(false);
        onCreateItem();
    };

    /** 过滤状态列表 */
    const filteredStatuses = list.filter(
        (status) =>
            status.code.toLowerCase().includes(searchValue.toLowerCase()) ||
            status.name.toLowerCase().includes(searchValue.toLowerCase())
    );

    /** popover 内容 */
    const content = (
        <div style={{ width: 280 }}>
            {/* 搜索框 */}
            <div style={{ marginBottom: 4 }}>
                <Input
                    placeholder="搜索"
                    prefix={<SearchOutlined />}
                    value={searchValue}
                    onChange={handleSearchChange}
                    allowClear
                />
            </div>

            {/* 状态列表 */}
            <div
                style={{
                    maxHeight: 200,
                    overflowY: "auto",
                }}
            >
                <List
                    size="small"
                    split={false}
                    dataSource={filteredStatuses}
                    renderItem={(item) => (
                        <List.Item
                            className={styles["plus-popover-list-item"]}
                            onClick={() => handleStatusSelect(item)}
                        >
                            <Space>
                                <span style={{ color: "#666", fontSize: "12px" }}>{item.code}</span>
                                <span style={{ color: "#666", fontSize: "12px" }}>{item.name}</span>
                            </Space>
                        </List.Item>
                    )}
                />
            </div>

            {/* 新建功能 */}
            {creatable && (
                <Button
                    type="text"
                    size="small"
                    icon={<PlusOutlined />}
                    style={{ color: "#2a61f1", fontSize: 12 }}
                    onClick={handleCreateAndSelect}
                >
                    {createItemLabel}
                </Button>
            )}
        </div>
    );

    return (
        <Popover
            fresh
            content={content}
            trigger="click"
            open={open}
            onOpenChange={handleOpenChange}
            placement="bottomLeft"
        >
            {children}
        </Popover>
    );
};

export default PlusPopover;
