import { Select, Spin } from "antd";
import { useEffect, useState } from "react";
import { useInterviewTemplateSelect } from "../../hooks/useInterviewTemplateSelect";
import { getInterviewTemplateListApi } from "@/app/request/modules/dashboard/jobManage";

/** 组件Props接口 */
interface BindInterviewTemplateProps {
    data: any;
    orgId: string;
    onTemplateChange?: (templateId: string | undefined) => void;
    readonly?: boolean;
}

/**
 * 绑定面试模板组件
 * @param props 组件属性
 * @returns JSX元素
 */
const BindInterviewTemplate = ({ data, orgId, onTemplateChange, readonly = false }: BindInterviewTemplateProps) => {
    const { options, fetching, handleSearch, handleClear, handlePopupScroll, handleDropdownVisibleChange } =
        useInterviewTemplateSelect(orgId);
    const [displayOptions, setDisplayOptions] = useState(options);
    const [loadingTemplate, setLoadingTemplate] = useState(false);

    /**
     * 根据模板ID获取模板标题
     * @param templateId 模板ID
     */
    const fetchTemplateById = async (templateId: string) => {
        try {
            setLoadingTemplate(true);
            const response = await getInterviewTemplateListApi({
                page: 0,
                size: 50,
                title: "",
                orgId,
            });
            const res: any = await response.json();

            if (res.code === 200 && res.data?.content) {
                // 查找匹配的模板
                const template = res.data.content.find((item: any) => item.id === templateId);
                if (template) {
                    return { label: template.title, value: template.id };
                }

                const searchResponse = await getInterviewTemplateListApi({
                    page: 0,
                    size: 100,
                    title: "",
                    orgId,
                });
                const searchRes: any = await searchResponse.json();

                if (searchRes.code === 200 && searchRes.data?.content) {
                    const foundTemplate = searchRes.data.content.find((item: any) => item.id === templateId);
                    if (foundTemplate) {
                        return { label: foundTemplate.title, value: foundTemplate.id };
                    }
                }
            }

            // 如果都没找到，返回ID作为显示值
            return { label: `${templateId}`, value: templateId };
        } catch (error) {
            console.error("获取模板信息失败:", error);
            return { label: `${templateId}`, value: templateId };
        } finally {
            setLoadingTemplate(false);
        }
    };

    /**
     * 检查当前值是否在选项中，如果不在则异步获取
     */
    useEffect(() => {
        const checkAndLoadTemplate = async () => {
            if (data.reviewTemplate && orgId) {
                // 检查当前值是否已经在选项中
                const existingOption = options.find((option) => option.value === data.reviewTemplate);

                if (!existingOption) {
                    // 如果不在选项中，异步获取模板信息
                    const templateOption = await fetchTemplateById(data.reviewTemplate);

                    // 将获取到的模板选项添加到显示选项中
                    setDisplayOptions((prevOptions) => {
                        const newOptions = [...prevOptions];
                        const existingIndex = newOptions.findIndex((opt) => opt.value === templateOption.value);

                        if (existingIndex === -1) {
                            newOptions.unshift(templateOption); // 添加到开头
                        } else {
                            newOptions[existingIndex] = templateOption; // 更新现有项
                        }

                        return newOptions;
                    });
                } else {
                    // 如果已经在选项中，直接使用现有选项
                    setDisplayOptions(options);
                }
            } else {
                setDisplayOptions(options);
            }
        };

        checkAndLoadTemplate();
    }, [data.reviewTemplate, orgId, options]);

    /**
     * 当 options 变化时，更新 displayOptions
     */
    useEffect(() => {
        if (!data.reviewTemplate) {
            setDisplayOptions(options);
        } else {
            // 保持当前模板在选项中
            const currentTemplateExists = options.find((option) => option.value === data.reviewTemplate);
            if (currentTemplateExists) {
                setDisplayOptions(options);
            }
        }
    }, [options, data.reviewTemplate]);

    /**
     * 处理选择变更
     * @param value 选中的值
     */
    const handleChange = (value: string | undefined) => {
        // 触发回调
        onTemplateChange?.(value);
    };

    return (
        <Select
            value={data.reviewTemplate}
            placeholder="绑定面试模板"
            size="small"
            style={{ fontSize: 12, width: 180 }}
            filterOption={false}
            allowClear
            onSearch={handleSearch}
            onClear={handleClear}
            showSearch
            options={displayOptions}
            loading={fetching || loadingTemplate}
            notFoundContent={fetching || loadingTemplate ? <Spin size="small" /> : "暂无数据"}
            onOpenChange={handleDropdownVisibleChange}
            onChange={handleChange}
            onBlur={() => handleDropdownVisibleChange(false)}
            disabled={readonly}
        />
    );
};

export default BindInterviewTemplate;
