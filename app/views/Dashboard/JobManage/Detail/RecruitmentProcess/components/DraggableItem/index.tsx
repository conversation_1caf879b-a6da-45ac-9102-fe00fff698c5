import DragIcon from "@/app/icons/dashboard/drag_icon.svg";
import DeleteIcon from "@/app/icons/dashboard/delete_icon.svg";
import styles from "./index.module.scss";
import clsx from "clsx";

interface DraggableItemProps {
    // draggableId: string;
    deletable: boolean;
    draggable: boolean;
    children: React.ReactNode;
    readonly?: boolean;
    size?: "small" | "large";
    className?: string;
    handleDelete?: () => void;
    dragHandleRef?: React.RefObject<HTMLDivElement>;
    style?: React.CSSProperties;
}
const DraggableItem = ({
    deletable,
    draggable,
    children,
    readonly = false,
    size = "large",
    className = "",
    handleDelete,
    dragHandleRef,
    style,
}: DraggableItemProps) => {
    return (
        <div className={styles["process-stage-item"]} style={style}>
            {deletable && !readonly && (
                <DeleteIcon
                    onClick={() => {
                        handleDelete?.();
                    }}
                    className={styles["process-stage-item-delete"]}
                    style={{ top: size === "large" ? "13px" : "10px" }}
                />
            )}
            <div className={clsx(styles["process-stage-item-header"], className)}>
                <div
                    ref={dragHandleRef}
                    style={{
                        cursor: draggable && !readonly ? "move" : "not-allowed",
                        display: "flex",
                        alignItems: "center",
                        padding: "0 16px",
                    }}
                >
                    <DragIcon />
                </div>
                {children}
            </div>
        </div>
    );
};

export default DraggableItem;
