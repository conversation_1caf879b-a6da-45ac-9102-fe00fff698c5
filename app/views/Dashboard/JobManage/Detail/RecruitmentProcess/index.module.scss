.process-item {
    display: flex;
    width: 100%;
    position: relative;
    --process-gap: 24px;

    .process-item-title {
        padding-bottom: var(--process-gap);
        position: relative;

        &::before {
            content: "";
            position: absolute;
            right: -1px;
            top: 0;
            width: 1px;
            background-color: #1890ff;
            // 默认高度为完整高度
            height: 100%;
        }

        &::after {
            content: "";
            display: inline-block;
            margin-left: 12px;
            position: relative;
            right: -3px;
            width: 5px;
            height: 5px;
            background-color: #1890ff;
            border-radius: 50%;
            // margin-right: 8px;
            vertical-align: middle;
        }
    }

    .process-item-content {
        flex: 1;
        padding-left: 12px;
        padding-bottom: var(--process-gap);
    }
}

.process-item {
    &:first-child {
        .process-item-title {
            &::before {
                top: 8px;
            }
        }
    }

    &:last-child {
        .process-item-title {
            &::before {
                height: 8px;
            }
        }
    }
}

.process-stage-item {
    border: 1px solid rgba(240, 242, 245, 0.7);
    border-radius: 5px;

    .process-stage-item-header {
        padding: 12px;
        background-color: #f6f7f8;
        display: flex;
        justify-content: space-between;
    }
}
