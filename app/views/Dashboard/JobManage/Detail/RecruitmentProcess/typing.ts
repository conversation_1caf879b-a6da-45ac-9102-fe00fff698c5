/**
 * 招聘流程相关的TypeScript类型定义
 */

/** 流程状态配置接口 */
export interface ProcessStatus {
    /** 状态唯一标识 */
    id: string;
    /** 状态名称 */
    name: string;
    /** 对外显示名称 */
    externalName: string;
    /** 状态类型 */
    type: "pending" | "processing" | "completed" | "rejected" | "custom";
    /** 状态颜色 */
    color: string;
    /** 是否需要标记原因 */
    requireReason: boolean;
    /** 是否启用职位锁定定位器 */
    positionLock: boolean;
    /** 是否启用状态准入条件 */
    entryCondition: boolean;
}

/** 基础流程阶段接口 */
export interface BaseStage {
    /** 阶段唯一标识 */
    id: string;
    /** 阶段名称 */
    // name: string;
    /** 阶段编码 */
    // code: string;
    /** 阶段包含的状态列表 */
    // statuses: ProcessStatus[];
}

/** 自定义流程阶段接口 */
export interface ProcessStage extends BaseStage {
    title: string;
    /** 阶段类型标识 */
    type: "pending" | "final";
    /** 阶段排序 */
    // order: number;
    draggable: boolean;
    deletable: boolean;
}

/** 最终阶段接口（正式录用） */
export interface FinalStage extends BaseStage {
    /** 阶段类型标识 */
    type: "final";
    /** 是否只读（不可删除） */
    readonly: true;
}

/** 阶段模板接口 */
export interface StageTemplate {
    /** 模板唯一标识 */
    id: string;
    /** 模板名称 */
    name: string;
    /** 模板图标 */
    icon: string;
    /** 模板描述 */
    description: string;
    /** 是否允许自定义状态 */
    allowCustomStatus: boolean;
    /** 默认状态配置 */
    defaultStatuses: Omit<ProcessStatus, "id">[];
}

/** 完整的流程数据接口 */
export interface ProcessData {
    /** 自定义阶段列表 */
    customStages: ProcessStage[];
    /** 最终阶段（正式录用） */
    finalStage: FinalStage;
    /** 是否启用阶段录入条件 */
    enableStageEntryCondition: boolean;
}

/** 组件Props接口 */
export interface RecruitmentProcessProps {
    /** 初始流程数据 */
    flowList: any;
    /** 数据变更回调 */
    onChange?: (data: ProcessData) => void;
    /** 是否只读模式 */
    readonly?: boolean;
}

/**
 * 流程时间线组件属性
 */
export interface ProcessTimelineProps {
    customStages: ProcessStage[]; // 自定义阶段列表
    finalStage: FinalStage; // 最终阶段
    stageTemplates: StageTemplate[]; // 阶段模板
    onAddStage: (stageData: Omit<ProcessStage, "id" | "order">) => void; // 添加阶段
    onUpdateStage: (stage: ProcessStage | FinalStage) => void; // 更新阶段
    onDeleteStage: (stageId: string) => void; // 删除阶段
    onReorderStages: (dragIndex: number, hoverIndex: number) => void; // 重排序
    readonly?: boolean; // 是否只读
}

/**
 * 流程阶段项组件属性
 */
export interface ProcessStageItemProps {
    data: ProcessStage;
    deletable: boolean;
    draggable: boolean;
    type: string;
    label: string;
}

/**
 * 最终阶段项组件属性
 */
export interface FinalStageItemProps {
    stage: FinalStage; // 最终阶段数据
    onUpdate: (stage: FinalStage) => void; // 更新阶段
    readonly?: boolean; // 是否只读
}

/**
 * 流程状态列表组件属性
 */
export interface ProcessStatusListProps {
    statuses: ProcessStatus[]; // 状态列表
    allowCustomStatus: boolean; // 是否允许自定义状态
    showEditor: boolean; // 是否显示编辑器
    onUpdate: (statuses: ProcessStatus[]) => void; // 更新状态
    onCloseEditor: () => void; // 关闭编辑器
    readonly?: boolean; // 是否只读
}

/**
 * 全局配置组件属性
 */
export interface GlobalConfigProps {
    enableStageEntryCondition: boolean; // 是否启用阶段录入条件
    onUpdate: (enableStageEntryCondition: boolean) => void; // 更新配置
    readonly?: boolean; // 是否只读
}

/** 阶段组件Props接口 */
export interface ProcessStageProps {
    /** 阶段数据 */
    stage: ProcessStage | FinalStage;
    /** 更新阶段回调 */
    onUpdate: (stage: ProcessStage | FinalStage) => void;
    /** 删除阶段回调（仅自定义阶段） */
    onDelete?: (stageId: string) => void;
    /** 是否只读模式 */
    readonly?: boolean;
}

/** 状态组件Props接口 */
export interface ProcessStatusProps {
    /** 状态数据 */
    status: ProcessStatus;
    /** 更新状态回调 */
    onUpdate: (status: ProcessStatus) => void;
    /** 删除状态回调 */
    onDelete: (statusId: string) => void;
    /** 是否只读模式 */
    readonly?: boolean;
}

/** 阶段选择器Props接口 */
export interface StageSelectorProps {
    /** 可用的阶段模板 */
    templates: StageTemplate[];
    /** 添加阶段回调 */
    onAddStage: (stage: Omit<ProcessStage, "id" | "order">) => void;
    /** 是否只读模式 */
    readonly?: boolean;
}

/** 默认的最终阶段配置 */
// export const DEFAULT_FINAL_STAGE: FinalStage = {
//   id: 'final-stage',
//   name: '正式录用',
//   code: 'S012',
//   type: 'final',
//   readonly: true,
//   statuses: [
//     {
//       id: 'pending-onboard',
//       name: '待入职',
//       externalName: '待入职',
//       type: 'pending',
//       color: '#faad14',
//       requireReason: false,
//       positionLock: false,
//       entryCondition: false,
//     },
//     {
//       id: 'onboarded',
//       name: '已入职',
//       externalName: '已入职',
//       type: 'completed',
//       color: '#52c41a',
//       requireReason: false,
//       positionLock: true,
//       entryCondition: false,
//     },
//     {
//       id: 'offer-declined',
//       name: 'Offer被拒',
//       externalName: 'Offer被拒',
//       type: 'rejected',
//       color: '#ff4d4f',
//       requireReason: true,
//       positionLock: false,
//       entryCondition: false,
//     },
//   ],
// };

/** 默认阶段模板 */
export const DEFAULT_STAGE_TEMPLATES: StageTemplate[] = [
    {
        id: "resume-screening",
        name: "简历筛选",
        icon: "📄",
        description: "对候选人简历进行初步筛选",
        allowCustomStatus: true,
        defaultStatuses: [
            {
                name: "待筛选",
                externalName: "待筛选",
                type: "pending",
                color: "#faad14",
                requireReason: false,
                positionLock: false,
                entryCondition: false,
            },
            {
                name: "通过筛选",
                externalName: "通过筛选",
                type: "completed",
                color: "#52c41a",
                requireReason: false,
                positionLock: false,
                entryCondition: false,
            },
            {
                name: "筛选淘汰",
                externalName: "筛选淘汰",
                type: "rejected",
                color: "#ff4d4f",
                requireReason: true,
                positionLock: false,
                entryCondition: false,
            },
        ],
    },
    {
        id: "phone-interview",
        name: "电话面试",
        icon: "📞",
        description: "通过电话进行初步面试",
        allowCustomStatus: true,
        defaultStatuses: [
            {
                name: "待面试",
                externalName: "待面试",
                type: "pending",
                color: "#faad14",
                requireReason: false,
                positionLock: false,
                entryCondition: false,
            },
            {
                name: "面试中",
                externalName: "面试中",
                type: "processing",
                color: "#1890ff",
                requireReason: false,
                positionLock: false,
                entryCondition: false,
            },
            {
                name: "面试通过",
                externalName: "面试通过",
                type: "completed",
                color: "#52c41a",
                requireReason: false,
                positionLock: false,
                entryCondition: false,
            },
            {
                name: "面试淘汰",
                externalName: "面试淘汰",
                type: "rejected",
                color: "#ff4d4f",
                requireReason: true,
                positionLock: false,
                entryCondition: false,
            },
        ],
    },
    {
        id: "technical-interview",
        name: "技术面试",
        icon: "💻",
        description: "技术能力评估面试",
        allowCustomStatus: true,
        defaultStatuses: [
            {
                name: "待面试",
                externalName: "待面试",
                type: "pending",
                color: "#faad14",
                requireReason: false,
                positionLock: false,
                entryCondition: false,
            },
            {
                name: "面试中",
                externalName: "面试中",
                type: "processing",
                color: "#1890ff",
                requireReason: false,
                positionLock: false,
                entryCondition: false,
            },
            {
                name: "面试通过",
                externalName: "面试通过",
                type: "completed",
                color: "#52c41a",
                requireReason: false,
                positionLock: false,
                entryCondition: false,
            },
            {
                name: "面试淘汰",
                externalName: "面试淘汰",
                type: "rejected",
                color: "#ff4d4f",
                requireReason: true,
                positionLock: false,
                entryCondition: false,
            },
        ],
    },
    {
        id: "hr-interview",
        name: "HR面试",
        icon: "👥",
        description: "HR综合面试评估",
        allowCustomStatus: true,
        defaultStatuses: [
            {
                name: "待面试",
                externalName: "待面试",
                type: "pending",
                color: "#faad14",
                requireReason: false,
                positionLock: false,
                entryCondition: false,
            },
            {
                name: "面试中",
                externalName: "面试中",
                type: "processing",
                color: "#1890ff",
                requireReason: false,
                positionLock: false,
                entryCondition: false,
            },
            {
                name: "面试通过",
                externalName: "面试通过",
                type: "completed",
                color: "#52c41a",
                requireReason: false,
                positionLock: false,
                entryCondition: false,
            },
            {
                name: "面试淘汰",
                externalName: "面试淘汰",
                type: "rejected",
                color: "#ff4d4f",
                requireReason: true,
                positionLock: false,
                entryCondition: false,
            },
        ],
    },
    {
        id: "background-check",
        name: "背景调查",
        icon: "🔍",
        description: "候选人背景信息调查",
        allowCustomStatus: false,
        defaultStatuses: [
            {
                name: "待调查",
                externalName: "待调查",
                type: "pending",
                color: "#faad14",
                requireReason: false,
                positionLock: false,
                entryCondition: false,
            },
            {
                name: "调查中",
                externalName: "调查中",
                type: "processing",
                color: "#1890ff",
                requireReason: false,
                positionLock: false,
                entryCondition: false,
            },
            {
                name: "调查通过",
                externalName: "调查通过",
                type: "completed",
                color: "#52c41a",
                requireReason: false,
                positionLock: false,
                entryCondition: false,
            },
            {
                name: "调查异常",
                externalName: "调查异常",
                type: "rejected",
                color: "#ff4d4f",
                requireReason: true,
                positionLock: false,
                entryCondition: false,
            },
        ],
    },
];
