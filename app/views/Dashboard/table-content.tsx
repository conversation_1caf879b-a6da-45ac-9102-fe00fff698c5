import React from "react";
import Icon from "@ant-design/icons";
import { Table, But<PERSON>, Tooltip } from "antd";
import DownloadIcon from "../../icons/dashboard/download.svg";
import styles from "./index.module.scss";
import ProTable from "@ant-design/pro-table";

interface DataType {
    key: React.ReactNode;
    name: string;
    age: number;
    address: string;
    children?: DataType[];
}

const columns = [
    {
        title: "需求部门",
        dataIndex: "name",
        key: "name",
        filters: [
            { text: "部门1", value: "1" },
            { text: "部门2", value: "2" },
        ],
        width: "12%",
    },
    {
        title: "在招岗位（人）",
        dataIndex: "age",
        key: "age",
        sorter: (a: DataType, b: DataType) => a.age - b.age,
    },
    {
        title: "招聘人数",
        dataIndex: "address",
        key: "address",
        sorter: (a: DataType, b: DataType) => a.age - b.age,
    },
    {
        title: "简历投递数",
        dataIndex: "address",
        key: "address",
        sorter: (a: DataType, b: DataType) => a.age - b.age,
    },
    {
        title: "用人部门筛选",
        dataIndex: "address",
        key: "address",
        sorter: (a: DataType, b: DataType) => a.age - b.age,
    },
    {
        title: "初试",
        dataIndex: "address",
        key: "address",
        sorter: (a: DataType, b: DataType) => a.age - b.age,
    },
    {
        title: "复试",
        dataIndex: "address",
        key: "address",
        sorter: (a: DataType, b: DataType) => a.age - b.age,
    },
    {
        title: "初试通过",
        dataIndex: "address",
        key: "address",
        sorter: (a: DataType, b: DataType) => a.age - b.age,
    },
    {
        title: "复试通过",
        dataIndex: "address",
        key: "address",
        sorter: (a: DataType, b: DataType) => a.age - b.age,
    },
    {
        title: "录用",
        dataIndex: "address",
        key: "address",
        sorter: (a: DataType, b: DataType) => a.age - b.age,
    },
];

const data: DataType[] = [
    {
        key: 1,
        name: "研发管理部",
        age: 60,
        address: "1121",
        children: [
            {
                key: 11,
                name: "研发管理子部门",
                age: 42,
                address: "222",
            },
            {
                key: 12,
                name: "J1212",
                age: 30,
                address: "3434",
                children: [
                    {
                        key: 121,
                        name: "3434",
                        age: 16,
                        address: "343434",
                    },
                ],
            },
            {
                key: 13,
                name: "Jim Green sr.",
                age: 72,
                address: "233",
                children: [
                    {
                        key: 131,
                        name: "2323",
                        age: 42,
                        address: "34343",
                        children: [
                            {
                                key: 1311,
                                name: "2323.",
                                age: 25,
                                address: "32323",
                            },
                            {
                                key: 1312,
                                name: "232332",
                                age: 18,
                                address: "34343",
                            },
                        ],
                    },
                ],
            },
        ],
    },
    {
        key: 2,
        name: "产品竞争力中心",
        age: 32,
        address: "22323",
    },
];

export function TableContent() {
    return (
        <div className={styles["table-content"]}>
            <div className={styles["table-top"]}>
                <div>一级组织管理分析</div>
                <Tooltip title="导出">
                    <Button>
                        <Icon component={() => <DownloadIcon />} size={16}></Icon>
                    </Button>
                </Tooltip>
            </div>
            <ProTable
                search={false}
                columns={columns}
                dataSource={data}
                pagination={false}
                scroll={{ y: 450 }}
                expandable={{
                    defaultExpandAllRows: false,
                }}
                summary={(pageData) => {
                    pageData.forEach((item) => {});

                    return (
                        <>
                            <Table.Summary.Row>
                                <Table.Summary.Cell index={0}>合计</Table.Summary.Cell>
                                <Table.Summary.Cell index={1}>100</Table.Summary.Cell>
                                <Table.Summary.Cell index={2}>200</Table.Summary.Cell>
                            </Table.Summary.Row>
                        </>
                    );
                }}
            />
        </div>
    );
}
