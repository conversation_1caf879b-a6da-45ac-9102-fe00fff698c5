import { Button, Flex, Layout, Popover, Select, Input, Cascader, Badge } from "antd";
import { useEffect, useMemo, useState, useCallback, useRef } from "react";
import { treeToArray } from "@/app/utils";
import CandidateMain from "@/app/views/Candidate/CandidateMain";
import ResizableContainer from "@/app/components/ResizableContainer";
import UseCandidateStore, { InterviewerJobResp, JobResp } from "@/app/store/modules/candidate";
import { TextView } from "@/app/components/TextView";
import { IconButton } from "@/app/components/Button";
import FilterIcon from "@/app/icons/talentTool/filter-icon.svg";
import SearchIcon from "@/app/icons/SearchIcon.svg";
import { getDepartmentListApi } from "@/app/request/modules/common";
import { recruitmentType } from "@/app/constant";
import { RespParams } from "@/app/typing";
import { getSubJobListByInterviewer<PERSON><PERSON> } from "@/app/request/modules/candidate";
import { Department } from "@/app/store/modules/dashboard/jobFlow";
import React from "react";
import styles from "@/app/views/Candidate/index.module.scss";

const FilterCandidate = () => {
    const {
        defaultTreeData,
        defaultFlatTreeData,
        currentDept,
        setDefaultTreeData,
        setDefaultFlatTreeData,
        setCurrentDept,
        setCurrentJob,
        setCurrentStage,
        currentJob,
    } = UseCandidateStore((state) => ({
        defaultTreeData: state.defaultTreeData,
        defaultFlatTreeData: state.defaultFlatTreeData,
        currentDept: state.currentDept,
        currentJob: state.currentJob,
        setDefaultTreeData: state.setDefaultTreeData,
        setDefaultFlatTreeData: state.setDefaultFlatTreeData,
        setStatusList: state.setStatusList,
        setCurrentDept: state.setCurrentDept,
        setCurrentStage: state.setCurrentStage,
        setCurrentJob: state.setCurrentJob,
        setSelectedCandidate: state.setSelectedCandidate,
        setCandidateList: state.setCandidateList,
    }));
    const [showFilter, setShowFilter] = useState(false);
    const [jobList, setJobList] = useState<JobResp[]>([]);
    const [filteredJobList, setFilteredJobList] = useState<JobResp[]>([]);
    const [searchValue, setSearchValue] = useState("");
    const [selectedRecruitmentType, setSelectedRecruitmentType] = useState(1); // 默认校园招聘
    const [selectedDepartment, setSelectedDepartment] = useState<string | undefined>(undefined);
    const [isFilterOperation, setIsFilterOperation] = useState(false);

    // 使用 ref 存储临时的部门选择值，避免状态更新导致 Popover 重新渲染
    const tempSelectedDepartment = useRef<string | undefined>(undefined);
    const itemRefs = useRef<{ [key: number]: HTMLDivElement | null }>({});

    // 临时选中的部门状态，用于 Cascader 显示
    const [tempSelectedDeptState, setTempSelectedDeptState] = useState<string | undefined>(undefined);

    // 独立的部门选择器组件
    const DepartmentCascader = React.memo<{
        defaultTreeData: any[];
        findDepartmentPath: (code: string) => string[] | null;
        handleCascaderChange: (value: string[], selectedOptions: any[]) => void;
        selectedValue?: string[] | string;
    }>((props) => {
        // 处理 selectedValue，支持字符串或数组
        const getValue = () => {
            if (!props.selectedValue) return undefined;

            // 如果是数组，直接返回
            if (Array.isArray(props.selectedValue)) {
                return props.selectedValue;
            }

            // 如果是字符串，通过 findDepartmentPath 获取完整路径
            return props.findDepartmentPath(props.selectedValue) || undefined;
        };

        return (
            <Cascader
                style={{ width: "100%" }}
                placeholder="请选择部门"
                value={getValue()}
                onChange={props.handleCascaderChange}
                options={props.defaultTreeData}
                fieldNames={{
                    label: "dept_name",
                    value: "dept_code",
                    children: "children" as keyof Department,
                }}
                showSearch
                changeOnSelect
                allowClear
                expandTrigger="hover"
            />
        );
    });

    DepartmentCascader.displayName = "DepartmentCascader";

    const getTreeData = async () => {
        const res: RespParams<Department> = await (await getDepartmentListApi()).json();
        if (res.code === 200) {
            const departmentList = [res.data].map((item) => {
                if (item.dept_code === "100000") {
                    item.parent_dept_code = "-1";
                }
                return item;
            });

            setDefaultTreeData(departmentList);
            const flatTreeData = treeToArray(departmentList);
            setDefaultFlatTreeData(flatTreeData);
            setCurrentDept(currentDept ?? flatTreeData?.[0]);
        }
    };

    const getJobList = async () => {
        try {
            const res: RespParams<InterviewerJobResp[]> = await (
                await getSubJobListByInterviewerApi({
                    deptCode: currentDept?.dept_code ?? "",
                    showMore: 0,
                })
            ).json();

            if (res.code === 200) {
                const list = res?.data?.map((item) => {
                    return { ...item.jobSpecs, count: item.count };
                });
                setJobList(list ?? []);
                const current = list?.find((item) => item.id === currentJob?.id);
                if (res?.data?.length > 0) {
                    const currentJob = current ?? list?.[0];
                    setCurrentJob(currentJob);
                }
                if (!res?.data || res?.data?.length === 0) {
                    setCurrentJob(undefined);
                }
            }
        } catch (error) {
            console.error("获取职位列表失败:", error);
        }
    };

    // 筛选职位列表
    useEffect(() => {
        let filtered = jobList;

        // 根据搜索关键词筛选
        if (searchValue) {
            filtered = filtered.filter((item) => {
                const formatStr = item.formatStr || "";
                const orgName = item.orgName || "";
                const jobNameInner = item.jobNameInner || "";
                const jobNameOuter = item.jobNameOuter || "";

                return (
                    formatStr.toLowerCase().includes(searchValue.toLowerCase()) ||
                    orgName.toLowerCase().includes(searchValue.toLowerCase()) ||
                    jobNameInner.toLowerCase().includes(searchValue.toLowerCase()) ||
                    jobNameOuter.toLowerCase().includes(searchValue.toLowerCase())
                );
            });
        }

        setFilteredJobList(filtered);
    }, [jobList, searchValue]);

    // 缓存路径查找函数
    const findDepartmentPath = useCallback(
        (targetCode: string) => {
            const findPath = (data: any[], targetCode: string, path: string[] = []): string[] | null => {
                for (const item of data) {
                    const currentPath = [...path, item.dept_code];
                    if (item.dept_code === targetCode) {
                        return currentPath;
                    }
                    if (item.children && item.children.length > 0) {
                        const result = findPath(item.children, targetCode, currentPath);
                        if (result) return result;
                    }
                }
                return null;
            };
            return findPath(defaultTreeData, targetCode);
        },
        [defaultTreeData]
    );

    useEffect(() => {
        getTreeData();
    }, []);

    // 同步当前部门到筛选状态（仅在非筛选操作时同步）
    useEffect(() => {
        if (currentDept && !isFilterOperation) {
            setSelectedDepartment(currentDept.dept_code);
        }
    }, [currentDept, isFilterOperation]);

    useEffect(() => {
        if (currentDept) {
            getJobList();
        }
    }, [currentDept]);

    // 筛选内容组件 - 使用更稳定的实现
    const FilterContent = useMemo(() => {
        const handleResetClick = () => {
            setSelectedRecruitmentType(1);
            setSelectedDepartment(undefined);
            tempSelectedDepartment.current = undefined;
            setTempSelectedDeptState(undefined);
            setSearchValue("");
            if (defaultFlatTreeData.length > 0) {
                setCurrentDept(defaultFlatTreeData[0]);
            }
            setShowFilter(false);
        };

        const handleCancelClick = () => {
            tempSelectedDepartment.current = selectedDepartment;
            setTempSelectedDeptState(selectedDepartment);
            setShowFilter(false);
        };

        const handleConfirmClick = () => {
            setShowFilter(false);
            setIsFilterOperation(true);
            const finalDepartment = tempSelectedDepartment.current;
            setSelectedDepartment(finalDepartment);
            if (finalDepartment) {
                const selectedDept = defaultFlatTreeData.find((dept) => dept.dept_code === finalDepartment);
                if (selectedDept) {
                    setCurrentDept(selectedDept);
                }
            }
            setTimeout(() => setIsFilterOperation(false), 100);
        };

        const handleInlineCascaderChange = (value: string[], selectedOptions: any[]) => {
            if (value && value.length > 0) {
                const selectedCode = value[value.length - 1];
                tempSelectedDepartment.current = selectedCode;
                setTempSelectedDeptState(selectedCode);
            } else {
                tempSelectedDepartment.current = undefined;
                setTempSelectedDeptState(undefined);
            }
        };

        return (
            <Flex vertical gap={16} className={styles["filter-content"]} style={{ width: 280 }}>
                <Flex vertical gap={12}>
                    <div>
                        <div style={{ marginBottom: 8, fontSize: 14, fontWeight: 500 }}>招聘场景</div>
                        <Select
                            style={{ width: "100%" }}
                            placeholder="请选择招聘场景"
                            value={selectedRecruitmentType}
                            onChange={setSelectedRecruitmentType}
                            options={recruitmentType}
                        />
                    </div>
                    <div>
                        <div style={{ marginBottom: 8, fontSize: 14, fontWeight: 500 }}>部门</div>
                        <DepartmentCascader
                            defaultTreeData={defaultTreeData}
                            findDepartmentPath={findDepartmentPath}
                            handleCascaderChange={handleInlineCascaderChange}
                            selectedValue={
                                tempSelectedDeptState
                                    ? (findDepartmentPath(tempSelectedDeptState) ?? undefined)
                                    : undefined
                            }
                        />
                    </div>
                </Flex>
                <Flex align="center" justify="space-between" gap={8}>
                    <Button onClick={handleResetClick}>重置</Button>
                    <Flex gap={8}>
                        <Button onClick={handleCancelClick}>取消</Button>
                        <Button type="primary" onClick={handleConfirmClick}>
                            确认
                        </Button>
                    </Flex>
                </Flex>
            </Flex>
        );
    }, [
        selectedRecruitmentType,
        defaultTreeData,
        tempSelectedDeptState,
        findDepartmentPath,
        selectedDepartment,
        defaultFlatTreeData,
    ]);

    const Filter = useMemo((): JSX.Element => {
        const handleIconClick = () => {
            // 打开 Popover 时，初始化临时选择值为当前状态值
            tempSelectedDepartment.current = selectedDepartment;
            setTempSelectedDeptState(selectedDepartment);
            setShowFilter(true);
        };

        const handleOpenChange = (open: boolean) => {
            setShowFilter(open);
        };

        const getContainer = () => document.body;

        return (
            <Popover
                classNames={{ root: `${styles["filter-popover"]}` }}
                title="高级筛选"
                placement="bottomLeft"
                trigger="click"
                open={showFilter}
                onOpenChange={handleOpenChange}
                content={FilterContent}
                getPopupContainer={getContainer}
            >
                <IconButton title="高级筛选" icon={<FilterIcon />} onClick={handleIconClick} />
            </Popover>
        );
    }, [showFilter, FilterContent, selectedDepartment]);

    useEffect(() => {
        // 如果没有当前职位，直接返回
        if (!currentJob?.id) return;

        // 定义检查和滚动函数
        const scrollToItem = () => {
            // 安全地检查和访问元素
            const element = itemRefs.current?.[currentJob?.id ?? -1];
            if (element) {
                // 执行滚动
                element.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                });
                return true; // 滚动成功
            }
            return false; // 没有找到元素
        };

        // 立即尝试滚动
        if (scrollToItem()) return;

        // 如果立即滚动失败，使用 requestAnimationFrame 在下一帧尝试
        const frameId = requestAnimationFrame(() => {
            if (scrollToItem()) return;

            // 仍然失败，设置一个短暂的延迟后再试一次
            const timeoutId = setTimeout(() => {
                scrollToItem();
            }, 100);

            // 清理 setTimeout
            return () => clearTimeout(timeoutId);
        });

        // 清理 requestAnimationFrame
        return () => cancelAnimationFrame(frameId);
    }, [currentJob]);

    return (
        <Layout className={styles["candidate-wrapper"]}>
            <ResizableContainer resizable={{ right: true }} minWidth="16%" maxWidth="26%" initialWidth="16%">
                <Layout.Sider width="100%" style={{ marginBottom: 20, paddingBottom: 0 }}>
                    <div style={{ width: "100%" }}>
                        <div style={{ width: "100%", marginBottom: 12 }}>
                            {/* 搜索和筛选区域 */}
                            <div style={{ marginBottom: 12 }}>
                                <Flex gap={8} align="center">
                                    <Input
                                        placeholder="搜索职位..."
                                        prefix={<SearchIcon />}
                                        value={searchValue}
                                        onChange={(e) => setSearchValue(e.target.value)}
                                        allowClear
                                        style={{ flex: 1 }}
                                    />
                                    {Filter}
                                </Flex>
                            </div>

                            {/* 职位列表 */}
                            {(filteredJobList.length === 0 && jobList.length === 0
                                ? ([{ formatStr: "默认职位", id: -1 }] as JobResp[])
                                : filteredJobList?.map((item: JobResp) => {
                                      let str = "";
                                      const list = item?.jobNameInner?.split("/");
                                      list.reverse();
                                      //   str = list?.[0] + "-" + item?.jobNameOuter;
                                      str = item?.jobNameOuter;
                                      item.formatStr = str;

                                      return item;
                                  })
                            )?.map((item: JobResp) => (
                                <div
                                    key={item.id}
                                    ref={(el) => {
                                        itemRefs.current[item?.id ?? new Date().getTime()] = el;
                                    }}
                                    onClick={() => setCurrentJob(item)}
                                    style={{
                                        position: "relative",
                                        padding: "8px 12px",
                                        cursor: "pointer",
                                        borderRadius: "6px",
                                        marginBottom: "4px",
                                        backgroundColor: currentJob?.id === item.id ? "#1890ff" : "transparent",
                                        color: currentJob?.id === item.id ? "white" : "inherit",
                                        border:
                                            currentJob?.id === item.id
                                                ? "1px solid #1890ff"
                                                : "1px solid var(--border-color)",
                                        transition: "all 0.2s ease",
                                    }}
                                    onMouseEnter={(e) => {
                                        if (currentJob?.id !== item.id) {
                                            e.currentTarget.style.backgroundColor = "var(--hover-color)";
                                        }
                                    }}
                                    onMouseLeave={(e) => {
                                        if (currentJob?.id !== item.id) {
                                            e.currentTarget.style.backgroundColor = "transparent";
                                        }
                                    }}
                                >
                                    <Badge count={item?.count ?? 0} className={styles["badge-wrapper"]} />
                                    <Flex vertical>
                                        <div>
                                            <TextView text={item.formatStr ?? ""} placement="right" />
                                        </div>

                                        <div
                                            style={{
                                                marginTop: 4,
                                                fontSize: 12,
                                                color:
                                                    currentJob?.id === item.id
                                                        ? "rgba(255,255,255,0.9)"
                                                        : "var(--sub-text-color)",
                                            }}
                                        >
                                            <TextView text={item.orgName ?? ""} lines={2} placement="right" />
                                        </div>
                                    </Flex>
                                </div>
                            ))}
                        </div>
                    </div>
                </Layout.Sider>
            </ResizableContainer>
            <Layout.Content>
                <CandidateMain mode="Interviewer" onJobListRefresh={getJobList} />
            </Layout.Content>
        </Layout>
    );
};

export default FilterCandidate;
