.elimination-pool-wrapper {
    height: 100%;
    display: flex;
    flex-direction: row;

    .elimination-pool-tree {
        margin: 8px 0;
        display: flex;
        flex-direction: column;
        gap: 12px;

        :global {
            .ant-tree-node-content-wrapper {
                max-width: 80%;
                overflow: hidden;
            }
        }
    }

    .elimination-pool-main-wrapper {
        width: 100%;
        height: 100%;
        position: relative;

        .elimination-pool-main__header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;

            .header-left {
                :global {
                    .ant-select-selector {
                        border-radius: 0;
                        border: none !important;
                        box-shadow: none !important;

                        &:hover,
                        &:active,
                        &:focus-within {
                            border-bottom: 1px solid var(--primary) !important;
                        }
                    }
                    .ant-select-selection-item,
                    .ant-select-selection-search-input {
                        font-size: 18px;
                        color: var(--main-text-color);
                        font-weight: 600;
                    }
                }
            }
        }

        .elimination-pool-main__content {
            height: calc(100% - 40px);
            overflow-y: auto;
            padding: 0 12px;

            .elimination-pool-list {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 16px;
                margin-top: 16px;
            }
        }
    }

    .filter-popover {
        width: 320px;
        z-index: 1050;

        :global {
            .ant-select-dropdown {
                z-index: 1051 !important;
            }
        }
    }
}

// EliminationPoolMain 组件样式
.elimination-pool-main {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    // padding: 16px;

    .candidate-list-container {
        flex: 1;
        overflow-y: auto;
        padding: 8px 0;

        // 候选人卡片间距
        :global {
            .talent-card {
                margin-bottom: 16px;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        gap: 16px;
        padding: 16px 0;
    }

    .bottom-area {
        padding: 12px 0;
        border-top: 1px solid #f0f0f0;
        background-color: #fff;
        position: sticky;
        bottom: 0;
        z-index: 5;

        :global {
            .ant-checkbox-wrapper {
                font-size: 14px;
                color: #666;
            }

            .ant-pagination {
                margin: 0;

                .ant-pagination-total-text {
                    color: #666;
                    font-size: 14px;
                }
            }
        }
    }
}
