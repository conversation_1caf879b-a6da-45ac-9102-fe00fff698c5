import React from "react";
import { Avatar } from "antd";
import Icon from "@ant-design/icons";
import FileIcon from "../../icons/chat/file.svg";
import ResetIcon from "../../icons/chat/reset.svg";
import { TextView } from "../../components/TextView";
import { ConsultCard } from "../../components/ConsultCard";
import styles from "./index.module.scss";

import TestCard1 from "../../icons/chat/test-card1.png";
import TestCard2 from "../../icons/chat/test-card2.png";
import TestCard3 from "../../icons/chat/test-card3.png";

export function CardContent() {
    return (
        <div className={styles["card-content"]}>
            <div className={styles["left-card"]}>
                <div className={styles["left-match"]}>
                    <div className={styles["match-top"]}>
                        <div className={styles["title"]}>岗位人员匹配</div>
                        <div className={styles["btn"]}>
                            <Icon className={styles["reset-logo"]} component={() => <ResetIcon />} />
                            换一换
                        </div>
                    </div>
                    <div className={styles["match-content"]}>
                        <div className={styles["match-desc"]}>
                            <div className={styles["match-logo"]}></div>
                            <div className={styles["content"]}>
                                <div className={styles["match-station"]}>
                                    <TextView text="终端产品设计师"></TextView>
                                </div>
                                <div className={styles["match-user"]}>
                                    <TextView text="范丞丞 ｜ 机器人行业"></TextView>
                                </div>
                            </div>
                        </div>
                        <div className={styles["match-count"]}>
                            <div className={styles["count"]}>98%</div>
                            <div className={styles["text"]}>匹配度</div>
                        </div>
                    </div>
                </div>
                <div className={styles["left-file"]}>
                    <div className={styles["user"]}>
                        <Icon className={styles["file-logo"]} component={() => <FileIcon />} />
                        <Avatar size={20} src={TestCard3.src}></Avatar>
                        张康乐的简历
                    </div>
                    <div className={styles["desc"]}>25岁 ｜ 女｜本科｜5年</div>
                </div>
            </div>
            <div className={styles["right-card"]}>
                <div className={styles["right-title"]}>最新资讯</div>
                <div className={styles["right-content"]}>
                    <ConsultCard
                        imageUrl={TestCard1.src}
                        title="数字化招聘合集"
                        content="2025数字化招聘合集正式签署, 2025数字化招聘合集正式签署"
                        time="2025-01-23"
                    ></ConsultCard>
                    <ConsultCard
                        imageUrl={TestCard2.src}
                        title="数字化招聘合集"
                        content="2025数字化招聘合集正式签署"
                        time="2025-01-23"
                    ></ConsultCard>
                </div>
            </div>
        </div>
    );
}
