@import "../../styles/animation.scss";

.attach-images {
    position: absolute;
    left: 30px;
    bottom: 32px;
    display: flex;
}

.attach-image {
    cursor: default;
    width: 64px;
    height: 64px;
    border: rgba($color: #888, $alpha: 0.2) 1px solid;
    border-radius: 5px;
    margin-right: 10px;
    background-size: cover;
    background-position: center;
    background-color: var(--white);

    .attach-image-mask {
        width: 100%;
        height: 100%;
        opacity: 0;
        transition: all ease 0.2s;
    }

    .attach-image-mask:hover {
        opacity: 1;
    }

    .delete-image {
        width: 24px;
        height: 24px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
        float: right;
        background-color: var(--white);
    }
}

.chat-input-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
    gap: 5px;

    &-end {
        display: flex;
        margin-left: auto;
        gap: 5px;
    }

    .chat-input-action {
        display: inline-flex;
        border-radius: 20px;
        font-size: 12px;
        background-color: var(--white);
        color: var(--black);
        border: var(--border-in-light);
        padding: 4px 10px;
        animation: slide-in ease 0.3s;
        box-shadow: var(--card-shadow);
        transition: width ease 0.3s;
        align-items: center;
        height: 16px;
        width: var(--icon-width);
        overflow: hidden;

        .text {
            white-space: nowrap;
            padding-left: 5px;
            opacity: 0;
            transform: translateX(-5px);
            transition: all ease 0.3s;
            pointer-events: none;
        }

        &:hover {
            --delay: 0.5s;
            width: var(--full-width);
            transition-delay: var(--delay);

            .text {
                transition-delay: var(--delay);
                opacity: 1;
                transform: translate(0);
            }
        }

        .text,
        .icon {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

.prompt-toast {
    position: absolute;
    bottom: -50px;
    z-index: 999;
    display: flex;
    justify-content: center;
    width: calc(100% - 40px);

    .prompt-toast-inner {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        background-color: var(--white);
        color: var(--black);

        border: var(--border-in-light);
        box-shadow: var(--card-shadow);
        padding: 10px 20px;
        border-radius: 100px;

        animation: slide-in-from-top ease 0.3s;

        .prompt-toast-content {
            margin-left: 10px;
        }
    }
}

.section-title {
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .section-title-action {
        display: flex;
        align-items: center;
    }
}

.context-prompt {
    .context-prompt-insert {
        display: flex;
        justify-content: center;
        padding: 4px;
        opacity: 0.2;
        transition: all ease 0.3s;
        background-color: rgba(0, 0, 0, 0);
        cursor: pointer;
        border-radius: 4px;
        margin-top: 4px;
        margin-bottom: 4px;

        &:hover {
            opacity: 1;
            background-color: rgba(0, 0, 0, 0.05);
        }
    }

    .context-prompt-row {
        display: flex;
        justify-content: center;
        width: 100%;

        &:hover {
            .context-drag {
                opacity: 1;
            }
        }

        .context-drag {
            display: flex;
            align-items: center;
            opacity: 0.5;
            transition: all ease 0.3s;
        }

        .context-role {
            margin-right: 10px;
        }

        .context-content {
            flex: 1;
            max-width: 100%;
            text-align: left;
        }

        .context-delete-button {
            margin-left: 10px;
        }
    }

    .context-prompt-button {
        flex: 1;
    }
}

.memory-prompt {
    margin: 20px 0;

    .memory-prompt-content {
        background-color: var(--white);
        color: var(--black);
        border: var(--border-in-light);
        border-radius: 10px;
        padding: 10px;
        font-size: 12px;
        user-select: text;
    }
}

.clear-context {
    margin: 20px 0 0 0;
    padding: 4px 0;

    border-top: var(--border-in-light);
    border-bottom: var(--border-in-light);
    box-shadow: var(--card-shadow) inset;

    display: flex;
    justify-content: center;
    align-items: center;

    color: var(--black);
    transition: all ease 0.3s;
    cursor: pointer;
    overflow: hidden;
    position: relative;
    font-size: 12px;

    animation: slide-in ease 0.3s;

    $linear: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
    mask-image: $linear;

    @mixin show {
        transform: translateY(0);
        position: relative;
        transition: all ease 0.3s;
        opacity: 1;
    }

    @mixin hide {
        transform: translateY(-50%);
        position: absolute;
        transition: all ease 0.1s;
        opacity: 0;
    }

    &-tips {
        @include show;
        opacity: 0.5;
    }

    &-revert-btn {
        color: var(--primary);
        @include hide;
    }

    &:hover {
        opacity: 1;
        border-color: var(--primary);

        .clear-context-tips {
            @include hide;
        }

        .clear-context-revert-btn {
            @include show;
        }
    }
}

.chat {
    display: flex;
    flex-direction: column;
    position: relative;
    height: 100%;
    
    .chat-header {
        padding: 20px;
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    :global {
        .ant-float-btn-icon {
            font-size: 0;
        }
    }
}

.chat-body {
    flex: 1;
    overflow: auto;
    overflow-x: hidden;
    padding: 20px;
    padding-bottom: 40px;
    position: relative;
    overscroll-behavior: none;
    
    // 默认隐藏滚动条或显示非常细的滚动条
    &::-webkit-scrollbar {
        width: 0;
        transition: width 0.3s ease;
    }
    
    // 鼠标悬停时显示滚动条
    &:hover {
        &::-webkit-scrollbar {
            width: 8px; // 纤细的滚动条宽度
        }
    }
    
    // 自定义滚动条轨道
    &::-webkit-scrollbar-track {
        background: transparent;
    }
    
    // 自定义滚动条滑块
    &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.15);
        border-radius: 10px;
    }
    
    // 滚动条滑块悬停效果
    &::-webkit-scrollbar-thumb:hover {
        background-color: rgba(0, 0, 0, 0.25);
    }
}

.chat-body-main-title {
    color: rgba(0, 0, 0, 0.75);
    font-family: "PingFang SC";
    font-size: 14px;
    font-weight: 500;
    // &:hover {
    //     text-decoration: underline;
    // }
}

.chat-body-title {
    display: flex;
    align-items: center;
}

.chat-message {
    display: flex;
    flex-direction: row;
    margin-top: 36px;

    &:last-child {
        animation: slide-in ease 0.3s;
    }
}

.chat-message-user {
    display: flex;
    flex-direction: row-reverse;

    .chat-message-header {
        flex-direction: row-reverse;
    }
}

.chat-message-header {
    display: flex;
    align-items: center;

    .chat-message-actions {
        display: flex;
        box-sizing: border-box;
        font-size: 12px;
        align-items: flex-end;
        justify-content: space-between;
        transition: all ease 0.3s;
        transform: scale(0.9) translateY(5px);
        margin: 0 10px;
        opacity: 0;
        pointer-events: none;

        .chat-input-actions {
            display: flex;
            flex-wrap: nowrap;
        }
    }

    .chat-model-name {
        font-size: 12px;
        color: var(--black);
        margin-left: 6px;
    }
}

.chat-message-container {
    max-width: 100%;

    display: flex;
    align-items: flex-start;
    // flex-direction: column;
    gap: 16px;

    &:hover {
        .chat-message-edit {
            opacity: 0.9;
        }

        .chat-message-actions {
            opacity: 1;
            pointer-events: all;
            transform: scale(1) translateY(0);
        }
    }
}

.chat-message-user > .chat-message-container {
    align-items: flex-end;
}

.chat-message-avatar {
    position: relative; 
}

.chat-message-status {
    font-size: 12px;
    color: #aaa;
    line-height: 1.5;
    margin-top: 5px;
}

.chat-message-tools {
    font-size: 12px;
    color: #aaa;
    line-height: 1.5;
    margin-top: 5px;
    .chat-message-tool {
        display: flex;
        align-items: flex-end;
        svg {
            margin-left: 5px;
            margin-right: 5px;
        }
    }
}

.chat-message-item-user {
    box-sizing: border-box;
    max-width: 100%;
    margin-top: 10px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    padding: 9px 18px;
    color: #000;
    font-family: "PingFang SC";
    font-size: 16px;
    font-weight: 400;
    user-select: text;
    word-break: break-word;
    border: var(--border-in-light);
    position: relative;
    transition: all ease 0.3s;
    line-height: 28px;
}

.chat-message-item-ai {
    box-sizing: border-box;
    max-width: 100%;
    padding: 10px;
    padding-top: 4px;
    font-size: 14px;
    user-select: text;
    word-break: break-word;
    position: relative;
    transition: all ease 0.3s;
}

.chat-message-item-image {
    width: 100%;
    margin-top: 10px;
}

.chat-message-item-images {
    width: 100%;
    display: grid;
    justify-content: left;
    grid-gap: 10px;
    grid-template-columns: repeat(var(--image-count), auto);
    margin-top: 10px;
}

.chat-message-item-image-multi {
    object-fit: cover;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.chat-message-item-image,
.chat-message-item-image-multi {
    box-sizing: border-box;
    border-radius: 10px;
    border: rgba($color: #888, $alpha: 0.2) 1px solid;
}

@media only screen and (max-width: 600px) {
    $calc-image-width: calc(100vw / 3 * 2 / var(--image-count));

    .chat-message-item-image-multi {
        width: $calc-image-width;
        height: $calc-image-width;
    }

    .chat-message-item-image {
        max-width: calc(100vw / 3 * 2);
    }
}

@media screen and (min-width: 600px) {
    $max-image-width: calc(calc(1200px - var(--sidebar-width)) / 3 * 2 / var(--image-count));
    $image-width: calc(
        calc(var(--window-width) - var(--sidebar-width)) / 3 * 2 / var(--image-count)
    );

    .chat-message-item-image-multi {
        width: $image-width;
        height: $image-width;
        max-width: $max-image-width;
        max-height: $max-image-width;
    }

    .chat-message-item-image {
        max-width: calc(calc(1200px - var(--sidebar-width)) / 3 * 2);
    }
}

.chat-message-action-date {
    font-size: 12px;
    opacity: 0.2;
    white-space: nowrap;
    transition: all ease 0.6s;
    color: var(--black);
    text-align: right;
    width: 100%;
    box-sizing: border-box;
    padding-right: 10px;
    pointer-events: none;
    z-index: 1;
}

.chat-message-user > .chat-message-container > .chat-message-item {
    background-color: #f5f5f5;

    &:hover {
        min-width: 0;
    }
}

.chat-input-panel {
    position: relative;
    width: 100%;
    padding: 20px;
    padding-top: 10px;
    box-sizing: border-box;
    flex-direction: column;
    // border-top: var(--border-in-light);
    box-shadow: var(--card-shadow);

    .chat-input-actions {
        .chat-input-action {
            margin-bottom: 10px;
        }
    }
}

@mixin single-line {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.prompt-hints {
    min-height: 20px;
    width: 100%;
    max-height: 50vh;
    overflow: auto;
    display: flex;
    flex-direction: column-reverse;

    background-color: var(--white);
    border: var(--border-in-light);
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: var(--shadow);

    .prompt-hint {
        color: var(--black);
        padding: 6px 10px;
        animation: slide-in ease 0.3s;
        cursor: pointer;
        transition: all ease 0.3s;
        border: transparent 1px solid;
        margin: 4px;
        border-radius: 8px;

        &:not(:last-child) {
            margin-top: 0;
        }

        .hint-title {
            font-size: 12px;
            font-weight: bolder;

            @include single-line();
        }

        .hint-content {
            font-size: 12px;

            @include single-line();
        }

        &-selected,
        &:hover {
            border-color: var(--primary);
        }
    }
}

.chat-input-panel-inner {
    cursor: text;
    display: flex;
    flex: 1;
    border-radius: 10px;
    border: var(--border-in-light);
}

.chat-input-panel-inner-attach {
    padding-bottom: 80px;
}

.chat-input-panel-inner:has(.chat-input:focus) {
    border: 1px solid var(--primary);
}

.chat-input {
    height: 100%;
    width: 100%;
    // border: none;
    // box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.03);
    background-color: var(--white);
    color: var(--black);
    font-family: inherit;
    padding: 10px 90px 10px 14px;
    resize: none;
    outline: none;
    box-sizing: border-box;
    min-height: 68px;

    border-radius: 16px;
    border: 1.5px solid var(---ino-color-bg-3, #E8E8E8);
    opacity: 0.99;

}

.chat-input:focus {
}

.chat-input-send {
    background-color: var(--primary);
    color: white;
    position: absolute;
    right: 30px;
    bottom: 32px;
}

@media only screen and (max-width: 600px) {
    .chat-input {
        font-size: 16px;
    }

    .chat-input-send {
        bottom: 30px;
    }
}

.shortcut-key-container {
    padding: 10px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.shortcut-key-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 16px;
}

.shortcut-key-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    padding: 10px;
    background-color: var(--white);
}

.shortcut-key-title {
    font-size: 14px;
    color: var(--black);
}

.shortcut-key-keys {
    display: flex;
    gap: 8px;
}

.shortcut-key {
    display: flex;
    align-items: center;
    justify-content: center;
    border: var(--border-in-light);
    border-radius: 8px;
    padding: 4px;
    background-color: var(--gray);
    min-width: 32px;
}

.shortcut-key span {
    font-size: 12px;
    color: var(--black);
}

.chat-main {
    display: flex;
    height: 100%;
    width: 60%; // 依据ui图算的，略有误差
    position: relative;
    overflow: hidden;
    margin: auto;
    
    .chat-body-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        flex: 1;
        width: 100%;
    }
    .chat-side-panel {
        position: absolute;
        inset: 0;
        background: var(--white);
        overflow: hidden;
        z-index: 10;
        transform: translateX(100%);
        transition: all ease 0.3s;
        &-show {
            transform: translateX(0);
        }
    }
}

.btn-group {
    visibility: hidden;
    opacity: 0;
    gap: 8px;
    align-items: center;
    margin-top: 8px;
    transition: opacity 0.2s ease-in-out, visibility 0s linear 0.2s;

    :global {
        .ant-btn {
            padding: 0 8px;
        }
    }
}

.message-user-content-wrapper:hover .btn-group,
.message-user-ai-wrapper:hover .btn-group {
    visibility: visible;
    opacity: 1;
    transition: opacity 0.2s ease-in-out;
}

.message-user-content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-end; 
}

.message-user-ai-wrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-start; 
}   