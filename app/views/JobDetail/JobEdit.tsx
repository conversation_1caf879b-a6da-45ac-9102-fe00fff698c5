import Header from "@/app/components/Header";
import { JOB_TYPE } from "@/app/constant";
import {
    getAllTagsApi,
    getJobFlowChildrenByIdApi,
    getJobFlowDetailApi,
    saveInterviewFlowApi,
    updateJobFlowApi,
} from "@/app/request/modules/dashboard";
import useJobFlowStore, {
    AddFlowItemExposeHandle,
    Department,
    FlowItemParams,
    Job,
    JobFlowReq,
    TagsItem,
    TagsResp,
} from "@/app/store/modules/dashboard/jobFlow";
import { DictData, RespParams } from "@/app/typing";
import { Button, Collapse, Flex, Layout } from "antd";
import { useEffect, useRef, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import FlowItem from "@/app/views/Dashboard/AddFlow/FlowItem";
import { JobTags } from "@/app/views/Dashboard/constant";
import { cloneDeep } from "lodash-es";
import { getDepartmentList<PERSON><PERSON>, getDictD<PERSON><PERSON>y<PERSON>ode<PERSON><PERSON>, getJob<PERSON><PERSON><PERSON><PERSON> } from "@/app/request/modules/common";
import styles from "./index.module.scss";
import RecruitmentProcess from "../Dashboard/JobManage/Detail/RecruitmentProcess";
import useJobDetailStore from "@/app/store/modules/dashboard/jobDetail";
import { formatHtmlWithLists } from "@/app/utils";
import messageService from "@/app/lib/message";

const JobEdit = ({ showHeader = true }: { showHeader?: boolean }) => {
    const location = useLocation();
    const navigate = useNavigate();

    const [tagsList, setTagsList] = useState<TagsResp[]>([]);
    const [jobId, setJobId] = useState<string>("");
    const [jobInfo, setJobInfo] = useState<JobFlowReq>();
    const [btnLoading, setBtnLoading] = useState<boolean>(false);

    const { setDepartmentList, setEducationList } = useJobFlowStore((state) => ({
        setDepartmentList: state.setDepartmentList,
        setEducationList: state.setEducationList,
    }));
    const { flowDetail, getFinalFlowDetail, getFlowDetail, setDetailInfo, getStageList, getStatusList } =
        useJobDetailStore((state) => ({
            flowDetail: state.flowDetail,
            getFlowDetail: state.getFlowDetail,
            getFinalFlowDetail: state.getFinalFlowDetail,
            setDetailInfo: state.setDetailInfo,
            getStageList: state.getStageList,
            getStatusList: state.getStatusList,
        }));

    const flowItemRef = useRef<AddFlowItemExposeHandle>(null);

    useEffect(() => {
        getAllTagsList();
        getDepartmentList();
        getEducationList();
        getStageList();
        getStatusList();
    }, []);
    useEffect(() => {
        location?.state?.jobId && setJobId(location?.state?.jobId);
    }, [location?.state?.jobId]);
    useEffect(() => {
        if (!tagsList) return;
        if (jobId) {
            getFlowFormData(jobId, JOB_TYPE.jobSpec);
            getFlowDetail(Number(jobId));
        }
    }, [tagsList]);
    useEffect(() => {
        setDetailInfo({ orgId: location.state.orgId });
    }, [location.state.orgId]);

    const getAllTagsList = async () => {
        const res2: RespParams<TagsResp[]> = await (await getAllTagsApi({ layer: "", label: "" })).json();
        if (res2.code === 200) {
            setTagsList(res2.data);
        }
    };

    const getDepartmentList = async () => {
        const res: RespParams<Department> = await (await getDepartmentListApi()).json();
        if (res.code === 200) {
            setDepartmentList([res.data]);
        }
    };
    const getEducationList = async () => {
        const res: RespParams<DictData[]> = await (await getDictDataByCodeApi("education")).json();
        if (res.code === 200) {
            setEducationList(res.data.map((item) => item.key));
        }
    };

    // 获取需求详情
    const getFlowFormData = async (formId: string, flowType: JOB_TYPE) => {
        const res: RespParams<JobFlowReq> = await (
            await (flowType === JOB_TYPE.jobSpec
                ? getJobFlowDetailApi(formId)
                : getJobFlowChildrenByIdApi(Number(formId)))
        ).json();
        if (res.code === 200) {
            let list: JobFlowReq[] = [];
            list.push(res.data);
            list = list.map((item) => {
                item.orgId = Array.isArray(item.orgId) ? item.orgId : item.orgId.split("/");
                item.jobNameInnerPath = Array.isArray(item.jobNameInnerPath)
                    ? item.jobNameInnerPath
                    : item.jobNameInnerPath.split("/");
                item.educational = Array.isArray(item.educational) ? item.educational : item.educational.split("/");
                item.jobLocation = Array.isArray(item.jobLocation) ? item.jobLocation : item.jobLocation.split("/");

                item.jobTags = cloneDeep(JobTags);
                const tagsDetail: TagsResp[] = [];
                item.tags?.forEach((item2: number) => {
                    const tagItem = tagsList.find((item3) => item3.id === item2);
                    if (tagItem) {
                        tagsDetail.push(tagItem);
                    }
                });

                tagsDetail.forEach((item2) => {
                    const groupIndex = item.jobTags?.findIndex((item3: TagsItem) => item3.label === item2.layer) ?? -1;
                    if (groupIndex !== -1) {
                        const tagIndex =
                            item.jobTags?.[groupIndex]?.children?.findIndex(
                                (item3: any) => item3.label === item2.label
                            ) ?? -1;

                        if (tagIndex !== -1) {
                            item.jobTags?.[groupIndex]?.children?.[tagIndex]?.value?.push(item2);
                        }
                    }
                });
                item.editMode = false;
                if (item.jobSpecs && item.jobSpecs.length > 0) {
                    item.jobSpecs.forEach((item2: FlowItemParams) => {
                        item2.assignment = parseInt((item2 as any).proportionValues);
                    });
                }
                return item;
            });

            const promiseList = list.map(async (item: JobFlowReq) => {
                const orgNo = item.orgId?.[item.orgId?.length - 1];
                const res: RespParams<Job[]> = await (await getJobListApi({ orgNo })).json();
                return res;
            });
            Promise.all(promiseList).then((res) => {
                const newList = list.map((item: JobFlowReq, index: number) => {
                    item.jobList = res[index].data;

                    return item;
                });

                setJobInfo(newList?.[0]);
            });
        }
    };

    const handleSave = async () => {
        const res = await flowItemRef?.current?.getForm()?.validateFields();
        if (res) {
            let params: any = flowItemRef.current?.getForm()?.getFieldsValue();
            params = { ...jobInfo, ...params };

            params.jobJD = flowItemRef.current?.richTextRef?.current?.getHTML() ?? params.jobJD;
            params.jobJD = formatHtmlWithLists(params.jobJD);
            if (Array.isArray(params.orgId)) {
                params.orgId = params?.orgId?.join("/");
            }
            if (Array.isArray(params.jobNameInnerPath)) {
                params.jobNameInnerPath = params?.jobNameInnerPath?.join("/");
            }
            if (Array.isArray(params.educational)) {
                params.educational = params?.educational?.join("/");
            }
            if (Array.isArray(params.jobLocation)) {
                params.jobLocation = params?.jobLocation?.join("/");
            }

            // 校验人才比例之后是否为100%
            if (Math.abs(params.topTalents + params.excellentTalents + params.regularTalents - 100) > 0.01) {
                messageService.error(
                    `${params?.orgName ?? "默认组织"}-${params?.jobNameInner ?? "默认岗位"} 人才比例之和必须为100%`
                );
                return false;
            }

            // 检验职位JD是否符合正则校验
            const regex1 = /(工作职责|岗位职责|主要职责)[:：]\s*(?=\S)/g;
            const regex2 = /(职位要求|岗位要求|任职要求)[:：]\s*(?=\S)/g;
            if (!regex1.test(params.jobJD) || !regex2.test(params.jobJD)) {
                messageService.error(
                    <Flex vertical align="start">
                        <div>岗位JD格式错误，格式必须为: </div>
                        <div>工作职责/岗位职责/主要职责: </div>
                        <div>xxxxxxx</div>
                        <div>职位要求/岗位要求/任职要求:</div>
                        <div>xxxxxxx</div>
                    </Flex>
                );
                return false;
            }

            params.tags = [];
            params.jobTags = jobInfo?.jobTags;
            params.jobTags?.forEach((item: TagsItem) => {
                item?.children?.forEach((item: TagsItem) => {
                    params.tags.push(...(item.value?.map((item2: TagsResp) => item2.id) ?? []));
                });
            });
            delete params.jobTags;

            try {
                setBtnLoading(true);
                const finalFlowDetail = getFinalFlowDetail();
                const res = updateJobFlowApi([params]);
                const res2 = saveInterviewFlowApi(finalFlowDetail);

                Promise.all([res, res2]).then(async (result) => {
                    const res: RespParams<any> = await result[0].json();
                    const res2: RespParams<any> = await result[1].json();
                    if (res.code === 200 && res2.code === 200) {
                        setBtnLoading(false);
                        messageService.success("保存成功！");
                        navigate(-1);
                    }
                });
            } finally {
                setBtnLoading(false);
            }
        }
    };

    const collapseItems = [
        {
            key: "1",
            label: <div className={styles["form-title"]}>职位基本信息</div>,
            children: (
                <div className={styles["form-content"]}>
                    {jobInfo && (
                        <FlowItem
                            ref={flowItemRef}
                            item={jobInfo as FlowItemParams}
                            mode="editChildrenJob"
                            allocationMethod={jobInfo?.allocationMethod}
                            handleChangeLoading={(val) => {
                                setBtnLoading(val);
                            }}
                            handleJobNameChange={() => {}}
                            handleOrgChange={() => {}}
                            handleAssignmentChange={() => {}}
                        />
                    )}
                </div>
            ),
        },
        {
            key: "2",
            label: <div className={styles["form-title"]}>面试流程信息</div>,
            children: (
                <div className={styles["form-content"]}>
                    <RecruitmentProcess flowList={flowDetail?.stage} />
                </div>
            ),
        },
    ];

    return (
        <Layout className={styles["job-edit-wrapper"]}>
            {showHeader && (
                <Header disabled={btnLoading} title="编辑职位">
                    <Flex gap={12} align="center">
                        <Button
                            loading={btnLoading}
                            onClick={() => {
                                navigate(-1);
                            }}
                        >
                            取消
                        </Button>
                        <Button loading={btnLoading} type="primary" onClick={handleSave}>
                            确认
                        </Button>
                    </Flex>
                </Header>
            )}
            <Layout.Content>
                <Collapse defaultActiveKey={["1", "2"]} ghost items={collapseItems} />
            </Layout.Content>
        </Layout>
    );
};

export default JobEdit;
