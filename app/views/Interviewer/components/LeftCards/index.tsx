import styles from "./index.module.scss";
import dayjs, { type Dayjs } from "dayjs";
import weekday from "dayjs/plugin/weekday";
import React, { useState, useMemo } from "react";
import { <PERSON><PERSON>, DatePicker, Empty } from "antd";
import InterviewPassRateIcon from "@/app/icons/interview/interview-pass-rate.svg";
import InterviewSessionCountIcon from "@/app/icons/interview/interview-session-count.svg";
import OfferIssuedIcon from "@/app/icons/interview/offer-issued-count.svg";
import PendingOnboardingIcon from "@/app/icons/interview/pending-onboarding-count.svg";
import ResumeApprovedIcon from "@/app/icons/interview/resume-approved-count.svg";
import ResumeScreeningIcon from "@/app/icons/interview/resume-screening-count.svg";
import TimeSettingIcon from "@/app/icons/interview/time-setting.svg";
import { CaretDownOutlined } from "@ant-design/icons";
import { FILTER_TYPE, Path } from "@/app/constant";
import { useNavigate } from "react-router-dom";
import "dayjs/locale/zh-cn";
import useUserInfoStore from "@/app/store/userInfo";
import type { InterviewerItem, StatisticItem } from "@/app/store/modules/interviewer";
import useCandidateDetailStore from "@/app/store/modules/candidate/detail";

dayjs.locale("zh-cn");
dayjs.extend(weekday);

interface LeftCardsProps {
    onDateChange: (gmtStart?: string, gmtEnd?: string) => void;
    tableDate: InterviewerItem[];
    countRecord?: StatisticItem;
}

// 事件数据接口
interface EventItem {
    stageId: string;
    time: string;
    stageName: string;
    title: string;
    jobName: string;
    jobId: string;
    candidateId: string;
}

// 获取统计数据的函数
const getMiddleStatsData = (countRecord?: StatisticItem) => [
    {
        value: countRecord?.resumeScreeningCnt ?? 0,
        title: "简历筛选数量",
        icon: <ResumeScreeningIcon />,
        eventType: "resumeScreeningCnt",
    },
    {
        value: countRecord?.resumeScreeningOkCnt ?? 0,
        title: "简历筛选通过数量",
        icon: <ResumeApprovedIcon />,
        eventType: "resumeScreeningOkCnt",
    },
    {
        value: countRecord?.interviewSessionCnt ?? 0,
        title: "面试场次",
        icon: <InterviewSessionCountIcon />,
        eventType: "interviewSessionCnt",
    },
    { value: countRecord?.offerCnt ?? 0, title: "offer人数", icon: <OfferIssuedIcon />, eventType: "offerCnt" },
    {
        value: countRecord?.awaitJoinEmployeeCnt ?? 0,
        title: "待入职人数",
        icon: <PendingOnboardingIcon />,
        eventType: "awaitJoinEmployeeCnt",
    },
    //round-结果四舍五入为整数
    {
        value: `${Math.round((countRecord?.interviewPassRate ?? 0) * 100)}%`,
        title: "面试通过率",
        icon: <InterviewPassRateIcon />,
        eventType: "interviewPassRate",
    },
];

// 周事日历卡片
function WeeklyCalender({ onDateChange, tableDate }: Pick<LeftCardsProps, "onDateChange" | "tableDate">) {
    const [selectedDate, setselectedDate] = useState(dayjs());
    const navigate = useNavigate();
    const weekDays = ["S", "M", "T", "W", "T", "F", "S"];
    // 把周天放第一位
    const startOfWeek = selectedDate.startOf("week").day(0);
    const datesOfWeek = Array.from({ length: 7 }).map((_, i) => {
        return startOfWeek.add(i, "day");
    });

    const events = useMemo(() => {
        return tableDate.map((item) => dayjs(item.gmtStart).date());
    }, [tableDate]);

    const handleDateClick = (date: Dayjs) => {
        setselectedDate(date);
        const startOfDay = date.startOf("day");
        const endOfDay = date.endOf("day");
        onDateChange(startOfDay.format("YYYY-MM-DD HH:mm:ss"), endOfDay.format("YYYY-MM-DD HH:mm:ss"));
    };
    // TODO:目前是先共用函数。pm可能要改
    const handleDatePickerChange = (date: Dayjs) => {
        if (date) {
            handleDateClick(date);
        }
    };

    return (
        <div className={styles["weekly-calendar-container"]}>
            <div className={styles["calder-header"]}>
                <DatePicker
                    picker="date"
                    allowClear={false}
                    variant="borderless"
                    value={selectedDate}
                    format={"YYYY.MM"}
                    onChange={(date) => {
                        handleDatePickerChange(date);
                    }}
                    className={styles["calender-title"]}
                    suffixIcon={<CaretDownOutlined />}
                />
                <div className={styles["set-interview-time-btn"]}>
                    <Button
                        onClick={() => {
                            navigate(Path.InterviewTime);
                        }}
                        type="text"
                        style={{ display: "flex", alignItems: "center", gap: "4px" }}
                    >
                        <TimeSettingIcon />
                        设置面试时间
                    </Button>
                </div>
            </div>
            <div className={styles["week-days"]}>
                {weekDays.map((day, index) => (
                    <div key={index} className={styles["week-day"]}>
                        {day}
                    </div>
                ))}
            </div>
            <div className={styles["week-dates"]}>
                {datesOfWeek.map((date, index) => {
                    const isToday = date.isSame(dayjs(), "day");
                    const isSelected = date.isSame(selectedDate, "day");
                    const hasEvent = events.includes(date.date());

                    const dateCeilClasses = [styles["date-ceil"]];
                    if (isSelected) {
                        dateCeilClasses.push(styles["selected"]);
                    } else if (isToday) {
                        dateCeilClasses.push(styles["today"]);
                    }

                    return (
                        <div key={index} className={dateCeilClasses.join(" ")} onClick={() => handleDateClick(date)}>
                            <span className={styles["date-number"]}>{date.date()}</span>
                            {hasEvent && (
                                <div
                                    className={`${styles["event-dot"]} ${
                                        date.isBefore(dayjs(), "day") ? styles["past-event"] : ""
                                    }`}
                                ></div>
                            )}
                        </div>
                    );
                })}
            </div>
        </div>
    );
}

// 周事件内容卡片
function WeeklyEventContent({
    time,
    stageName,
    title,
    event,
}: {
    time: string;
    stageName: string;
    title: string;
    event: EventItem;
}) {
    const navigate = useNavigate();
    const clear = useCandidateDetailStore((state) => state.clear);

    const handleResumeDetail = (event: EventItem) => {
        clear();
        navigate(Path.ResumeDetail, {
            state: {
                jobId: event.jobId,
                jobName: event.jobName,
                applicantId: event.candidateId,
                mode: "Interviewer",
                isManualDistribution: false,
            },
        });
    };
    return (
        <div className={styles["weekly-event-content-container"]} onClick={() => handleResumeDetail(event)}>
            <div className={styles["weekly-event-content-item"]}>
                <div className={styles["weekly-event-content-time"]}>{time}</div>
                <div className={styles["weekly-event-content-title"]}>{stageName}</div>
            </div>
            <div className={styles["weekly-event-content-item-name"]}>{title}</div>
        </div>
    );
}

export default function LeftCards({ onDateChange, tableDate, countRecord }: LeftCardsProps) {
    const navigate = useNavigate();
    const { user } = useUserInfoStore((state) => ({
        user: state.user,
    }));

    // 将 tableDate 转换为 eventList
    const eventList = useMemo(() => {
        return tableDate.map((item): EventItem => {
            // 提取时间部分，去除年月日
            const startTime = dayjs(item.gmtStart).format("HH:mm");
            const endTime = dayjs(item.gmtEnd).format("HH:mm");

            return {
                stageId: item.metadata.stageId,
                time: `${startTime} - ${endTime}`,
                stageName: item.metadata.stageName,
                title: item.title,
                jobId: item.jobId,
                jobName: item.jobName,
                candidateId: item.candidateId,
            };
        });
    }, [tableDate]);

    const handleIconEvnet = (eventType: any) => {
        console.log(eventType);
        switch (eventType) {
            case "resumeScreeningCnt":
                navigate(Path.MyInterview, {
                    state: {
                        tab: FILTER_TYPE.ResumeScreened,
                    },
                });
                break;

            default:
                break;
        }
    };

    return (
        <div className={styles["container-left"]}>
            {/* 左侧顶部 */}
            <div className={styles["container-left-item-top"]}>
                <div className={styles["container-left-item-top-title"]}>你好！{user.emp_name}</div>
                <div className={styles["left-item-middle-bottom"]}>
                    {getMiddleStatsData(countRecord).map((stat, index) => (
                        <div
                            key={index}
                            className={`${styles["left-item-middle-bottom-item"]} ${stat.eventType === "resumeScreeningCnt" ? styles["event-type"] : ""}`}
                            onClick={() => {
                                stat.eventType && handleIconEvnet(stat.eventType);
                            }}
                        >
                            <div className={styles["left-item-middle-bottom-item-icon"]}>{stat.icon}</div>
                            <div className={styles["container-left-item-top-num-item"]}>
                                <div className={styles["container-left-item-top-num-item-num"]}>{stat.value}</div>
                                <div className={styles["container-left-item-top-num-item-title"]}>{stat.title}</div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* 左侧底部 */}
            <div className={styles["container-left-item-bottom"]}>
                {/* 固定的日历组件 */}
                <div className={styles["calendar-fixed-container"]}>
                    <WeeklyCalender onDateChange={onDateChange} tableDate={tableDate} />
                </div>

                {/* 可滚动的事件列表容器 */}
                <div className={styles["events-scrollable-container"]}>
                    {eventList.length > 0 ? (
                        eventList.map((event) => (
                            <WeeklyEventContent
                                key={event.stageId}
                                time={event.time}
                                stageName={event.stageName}
                                title={event.title}
                                event={event}
                            />
                        ))
                    ) : (
                        <div className={styles["empty-container"]}>
                            <Empty description="暂无面试安排" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
