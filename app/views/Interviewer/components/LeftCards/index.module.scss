// 左侧容器
.container-left {
    width: 27.2%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    justify-content: space-between;

    // 左侧顶部卡片
    .container-left-item-top {
        width: 100%;
        flex-shrink: 0;
        padding: 20px;
        box-sizing: border-box;
        border-radius: 12px;
        background: rgba(255, 255, 255, 1);
        display: flex;
        flex-direction: column;
        gap: 16px;

        .container-left-item-top-title {
            height: 29px;
            color: rgba(0, 0, 0, 0.9);
            font-family: "PingFang SC";
            font-weight: 500;
            font-size: 21px;
            line-height: 29px;
        }
    }

    .left-item-middle-top {
        color: rgba(0, 0, 0, 0.9);
        font-family: "PingFang SC";
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
    }

    .left-item-middle-bottom {
        padding: 8px;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: 1fr 1fr;
        align-items: center;
        justify-items: start;
        gap: 20px 12px;
        .event-type {
            cursor: pointer;
        }
        .left-item-middle-bottom-item {
            display: flex;
            align-items: center;

            .left-item-middle-bottom-item-icon {
                width: 44px;
                height: 44px;
                border-radius: 12px;
                margin-right: 12px;
                flex-shrink: 0;
            }

            .container-left-item-top-num-item {
                display: flex;
                flex-direction: column;
                gap: 4px;
            }
        }
    }

    // 左侧底部卡片
    .container-left-item-bottom {
        width: 100%;
        flex: 1;
        background: rgba(255, 255, 255, 1);
        padding: 16px;
        box-sizing: border-box;
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        gap: 16px;
        overflow: hidden;

        &::-webkit-scrollbar {
            width: 4px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        &::-webkit-scrollbar-thumb {
            background: transparent;
            border-radius: 2px;
        }

        &:hover {
            &::-webkit-scrollbar-thumb {
                background: rgba(0, 0, 0, 0.2);
            }
        }

        // 固定的日历容器
        .calendar-fixed-container {
            flex-shrink: 0;
        }

        // 可滚动的事件列表容器
        .events-scrollable-container {
            flex: 1;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 8px;

            &::-webkit-scrollbar {
                width: 4px;
            }

            &::-webkit-scrollbar-track {
                background: transparent;
            }

            &::-webkit-scrollbar-thumb {
                background: transparent;
                border-radius: 2px;
            }

            &:hover {
                &::-webkit-scrollbar-thumb {
                    background: rgba(0, 0, 0, 0.2);
                }
            }

            // Empty组件容器
            .empty-container {
                display: flex;
                justify-content: center;
                align-items: center;
                flex: 1;
                min-height: 100px;
            }
        }
    }
}

// 数字统计项样式
.container-left-item-top-num-item-num {
    color: rgba(0, 0, 0, 0.86);
    font-family: "Kingsoft_Cloud_Font";
    font-size: 24px;
    line-height: 34px;
}

.container-left-item-top-num-item-title {
    color: rgba(0, 0, 0, 0.6);
    font-family: "PingFang SC";
    font-size: 14px;
    line-height: 20px;
}

// 左侧底部卡片的日历板块
.weekly-calendar-container {
    display: flex;
    flex-direction: column;

    .calder-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .calender-title {
            input {
                color: rgba(21, 31, 40, 1);
                font-family: "Roboto";
                font-weight: 500;
                font-size: 21px;
                line-height: 25px;
                width: 90px;
            }
        }
        .set-interview-time-btn {
            color: rgba(0, 0, 0, 0.6);
            font-family: "Alibaba PuHuiTi";
            font-weight: 400;
            font-size: 14px;
            line-height: 19px;
            display: flex;
            align-items: center;
            cursor: pointer;
        }
    }

    .week-days {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        text-align: center;
        color: rgba(0, 0, 0, 0.4);
        font-family: "Roboto";
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
        margin: 28px 0 8px;
    }

    .week-dates {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        text-align: center;

        .date-ceil {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            cursor: pointer;
            color: rgba(0, 0, 0, 0.86);
            font-family: "Lato";
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
        }
        .date-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .date-ceil.today .date-number {
            border: 1px solid #ff4d4f;
            box-sizing: border-box;
        }

        .date-ceil.selected .date-number {
            background: rgba(0, 153, 242, 1);
            color: #fff;
        }

        .event-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: rgba(0, 153, 242, 1);
            position: absolute;
            bottom: -12px;
        }

        .event-dot.past-event {
            background: rgba(0, 0, 0, 0.26);
        }
    }
}

// 周事件内容卡片
.weekly-event-content-container {
    display: flex;
    flex-direction: column;
    padding: 12px;
    box-sizing: border-box;
    gap: 8px;
    border-radius: 8px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    cursor: pointer;

    .weekly-event-content-item {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        gap: 4px;

        .weekly-event-content-time {
            color: rgba(0, 0, 0, 0.6);
            font-family: "PingFang SC";
            line-height: 22px;
        }

        .weekly-event-content-title {
            color: rgba(0, 0, 0, 0.86);
            font-family: "PingFang SC";
            line-height: 22px;
            padding: 1px 8px;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.04);
        }
    }
    .weekly-event-content-item-name {
        color: rgba(0, 0, 0, 0.92);
        font-family: "PingFang SC";
        line-height: 20px;
        font-size: 14px;
        font-weight: 400;
    }
}
