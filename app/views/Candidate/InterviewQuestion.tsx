import { forwardRef, useEffect, useImperative<PERSON>andle, useState } from "react";
import type { InterviewDimension, InterviewQuestion, InterviewQuestionExpose } from "@/app/store/modules/candidate";
import styles from "./index.module.scss";
import { Checkbox, Form, Radio } from "antd";
import TextArea from "antd/es/input/TextArea";
import JobTagsPage from "../Dashboard/AddFlow/JobTags";
import { JobTags } from "../Dashboard/constant";
import { cloneDeep } from "lodash-es";
import { TagsItem } from "@/app/store/modules/dashboard/jobFlow";

const InterviewQuestion = forwardRef<InterviewQuestionExpose, { detailList: InterviewDimension[]; showTags: boolean }>(
    (props, ref) => {
        const { detailList, showTags } = props;
        const [form] = Form.useForm();

        const [tags, setTags] = useState<TagsItem[]>([]);
        const [initialValues, setInitialValues] = useState<string | undefined>();

        const statusList = ["通过", "淘汰"];

        useImperativeHandle(ref, () => {
            return {
                init: (initValue) => {
                    setTags(cloneDeep(JobTags));
                    form.resetFields();
                    setInitialValues(initValue);
                    if (initValue) {
                        const conclusion = detailList.find((item) => item.isConclusion);
                        const question = conclusion?.questions?.find((item) =>
                            item.options?.find((item2) => statusList.includes(item2.value))
                        );

                        if (question) {
                            form.setFieldsValue({
                                [question.questionId]: initValue,
                            });
                        }
                    }
                },
                formInstance: form,
            };
        });

        const renderFormItem = (question: InterviewQuestion) => {
            switch (question.type) {
                case "1":
                    return (
                        <Radio.Group
                            style={{ display: "flex", flexDirection: "column", gap: "8px", marginLeft: "16px" }}
                            disabled={Boolean(
                                question.options?.find((item) => statusList.includes(item.value)) && initialValues
                            )}
                            options={question.options?.map((option) => ({
                                label: option.value,
                                value: option.value,
                            }))}
                        ></Radio.Group>
                    );
                case "2":
                    return (
                        <Checkbox.Group
                            style={{ display: "flex", flexDirection: "column", gap: "8px", marginLeft: "16px" }}
                            options={question.options?.map((option) => option.value)}
                        />
                    );
                case "3":
                    return (
                        <TextArea
                            style={{ marginLeft: "16px" }}
                            placeholder="请输入"
                            allowClear
                            showCount
                            maxLength={5000}
                            rows={4}
                        />
                    );
                default:
                    return <div>未知类型</div>;
            }
        };

        return (
            <div className={styles["interview-question-wrapper"]}>
                {detailList.map((item) => {
                    return (
                        <div className={styles["interview-dimension"]} key={item.dimensionId}>
                            <div className={styles["title"]}>{item.dimensionId}</div>
                            <div className={styles["desc"]}>{item.description}</div>
                            <div className={styles["question-wrapper"]}>
                                <Form layout="vertical" form={form}>
                                    {item.questions.map((question) => {
                                        return (
                                            <Form.Item
                                                label={question.questionId}
                                                key={question.questionId}
                                                name={question.questionId}
                                                rules={[{ required: question.isRequired }]}
                                            >
                                                <div
                                                    style={{
                                                        color: "var(--sub-text-color)",
                                                        fontSize: "12px",
                                                        marginBottom: "8px",
                                                        marginLeft: "16px",
                                                        whiteSpace: "pre-wrap",
                                                    }}
                                                >
                                                    {question.description}
                                                </div>
                                                <Form.Item name={question.questionId} noStyle>
                                                    {renderFormItem(question)}
                                                </Form.Item>
                                            </Form.Item>
                                        );
                                    })}
                                </Form>
                            </div>
                        </div>
                    );
                })}
                {showTags && (
                    <JobTagsPage
                        tagsList={tags}
                        handleChangeTags={(val) => {
                            setTags(val);
                        }}
                    />
                )}
            </div>
        );
    }
);

InterviewQuestion.displayName = "InterviewQuestion";
export default InterviewQuestion;
