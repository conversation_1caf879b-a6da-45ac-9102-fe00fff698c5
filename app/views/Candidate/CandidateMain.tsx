import { Button, Checkbox, Empty, Flex, Pagination, Skeleton } from "antd";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { IconButton } from "../../components/Button";
import ZoomIcon from "@/app/icons/talentTool/zoom.svg";
import ZoomOutIcon from "@/app/icons/talentTool/zoomout.svg";
import styles from "./index.module.scss";
import TalentCard from "../TalentPoolNew/MainWrapper/TalentCard";
import FilterBar, { FilterOption, SortOption } from "@/app/components/FilterBar";
import { FilterState, useFilterState } from "../../hooks/useFilterState";
import { useNavigate } from "react-router-dom";
import { EVALUATE_TYPE, Path } from "@/app/constant";
import ScheduleInterview from "./ScheduleInterview";
import Assign from "./Assign";
import { PaginationInfo, RespPaginationParams, RespParams } from "@/app/typing";
import messageService from "@/app/lib/message";
import { getAllTagsApi } from "@/app/request/modules/dashboard";
import BottomMultiple from "@/app/components/BottomMultiple";
import UseCandidateStore, {
    CandidateAssignExpose,
    CandidateFilterResp,
    CandidateListReq,
    CandidateListResp,
    InterviewEvaluationExpose,
    InterviewFlowResp,
    JobResp,
    ScheduleInterviewExpose,
    TrackStageStatistic,
} from "@/app/store/modules/candidate";
import {
    getCandidateFilterListApi,
    getCandidateListByJobIdApi,
    getInterviewFlowApi,
    getParentCandidateListByJobIdApi,
} from "@/app/request/modules/candidate";
import { CandidateProps } from ".";
import useUserInfoStore from "@/app/store/userInfo";
import InterviewEvaluation from "./InterviewEvaluation";
import { cloneDeep, debounce } from "lodash-es";
import useFullScreenStore from "@/app/store/modules/fullscreen";
import InterviewStage from "@/app/components/InterviewStage";
import useCandidateDetailStore from "@/app/store/modules/candidate/detail";

interface SearchParamsOptions {
    pagination: PaginationInfo;
    currentStage?: TrackStageStatistic;
    filters: Partial<FilterState>;
    sortOptions: SortOption[];
}

const CandidateMain = ({ mode, onJobListRefresh }: CandidateProps & { onJobListRefresh?: () => void }) => {
    const navigate = useNavigate();

    const {
        statusList,
        currentStage,
        candidateList,
        selectedCandidate,
        currentJob,
        allTags,
        currentCandidateType,
        currentPagination,
        setStatusList,
        setCurrentStage,
        setSelectedCandidate,
        setCandidateList,
        setAllTags,
        setCurrentCandidateType,
        setCurrentPagination,
    } = UseCandidateStore((state) => ({
        statusList: state.statusList,
        currentStage: state.currentStage,
        candidateList: state.candidateList,
        selectedCandidate: state.selectedCandidate,
        currentJob: state.currentJob,
        allTags: state.allTags,
        currentCandidateType: state.currentCandidateType,
        currentPagination: state.currentPagination,
        setStatusList: state.setStatusList,
        setCurrentStage: state.setCurrentStage,
        setSelectedCandidate: state.setSelectedCandidate,
        setCandidateList: state.setCandidateList,
        setAllTags: state.setAllTags,
        setCurrentCandidateType: state.setCurrentCandidateType,
        setCurrentPagination: state.setCurrentPagination,
    }));
    const { isFullscreen, setIsFullScreen, toggleFullScreen } = useFullScreenStore((state) => ({
        isFullscreen: state.isFullScreen,
        setIsFullScreen: state.setIsFullScreen,
        toggleFullScreen: state.toggleFullScreen,
    }));
    const user = useUserInfoStore((state) => state.user);
    const clear = useCandidateDetailStore((state) => state.clear);

    const [filterOptions, setFilterOptions] = useState<FilterOption[]>([]);
    const [sortOptions, setSortOptions] = useState<SortOption[]>([]);
    const [loading, setLoading] = useState(false);
    const [contentHeight, setContentHeight] = useState(0);
    const [candidateSearchForm, setCandidateSearchForm] = useState<CandidateListReq>();

    const mainContentRef = useRef<HTMLDivElement>(null);
    const headerRef = useRef<HTMLDivElement>(null);
    const scheduleInterviewRef = useRef<ScheduleInterviewExpose>(null);
    const assignRef = useRef<CandidateAssignExpose>(null);
    const interviewEvaluationRef = useRef<InterviewEvaluationExpose>(null);
    const prevHeightRef = useRef(0);

    // 底部多选框全部选中
    const checkAll = useMemo(() => {
        if (candidateList.length === 0) return false;
        return candidateList.every((candidate) =>
            selectedCandidate.find((selected) => selected.profile.applicantId === candidate.profile.applicantId)
        );
    }, [selectedCandidate, candidateList]);
    // 底部多选框部分选中
    const indeterminate = useMemo(() => {
        const filterList = candidateList.filter((candidate) =>
            selectedCandidate.find((selected) => selected.profile.applicantId === candidate.profile.applicantId)
        );

        if (filterList.length === 0) return false;

        return filterList.length < candidateList.length;
    }, [selectedCandidate, candidateList]);
    // 简历筛选状态
    const filterStatus = useMemo(() => currentCandidateType === 1, [currentCandidateType]);
    // 分配简历状态
    const assignStatus = useMemo(() => currentCandidateType === 2, [currentCandidateType]);
    // 是否为简历初筛阶段
    const isResumeFirstStage = useMemo(() => {
        const stage = statusList.find((item) => item.stageId === currentStage?.stageId);
        return stage?.stageCode.includes("S0001");
    }, [statusList, currentStage]);

    // 使用增强的筛选状态Hook
    const { filters, pagination, updateFilter, updateFilters, resetFilters, goToPage, changePageSize, hasFilters } =
        useFilterState();
    const [allSearchParams, setAllSearchParams] = useState<SearchParamsOptions>({
        pagination,
        currentStage,
        filters,
        sortOptions,
    });

    /**
     * 处理主内容区搜索
     * @param keyword 搜索关键词
     */
    const handleSearch = useCallback(
        (keyword: string) => {
            updateFilter("keyword", keyword);
            goToPage(0);
            setAllSearchParams((prev) => {
                return { ...prev, filters: { ...prev.filters, keyword } };
            });
        },
        [updateFilter]
    );

    /** 处理筛选变化 */
    const handleMainFilterChange = useCallback(
        (newFilters: Partial<FilterState>) => {
            updateFilters(newFilters);
            setAllSearchParams((prev) => ({ ...prev, filters: newFilters }));
        },
        [updateFilters]
    );

    /**
     * 处理筛选条件配置变化
     */
    const handleFilterOptionsChange = (newFilterOptions: FilterOption[]) => {
        setFilterOptions(newFilterOptions);
    };

    /**
     * 处理排序变化
     */
    const handleSortChange = (newSortOptions: SortOption[]) => {
        setSortOptions(newSortOptions);
        goToPage(0);
        setAllSearchParams((prev) => ({ ...prev, sortOptions: newSortOptions }));
    };

    useEffect(() => {
        setCurrentPagination(pagination);
    }, [pagination]);

    /** 处理分页变化 */
    const handlePageChange = useCallback(
        (page: number, pageSize: number) => {
            if (pageSize !== pagination.pageSize) {
                changePageSize(pageSize);
            } else {
                goToPage(page - 1);
            }
            setAllSearchParams((prev) => ({ ...prev, pagination: { ...pagination, pageSize, pageNum: page - 1 } }));
        },

        [pagination.pageSize, changePageSize, goToPage]
    );

    /**
     * 处理分页大小变化
     * @param pageSize 每页大小
     */
    const handlePageSizeChange = (pageSize: number) => {
        handlePageChange(0, pageSize);
    };

    // 定义筛选条件配置
    const initializeFilterOptions = (
        educationList: { label: string; value: string }[],
        workExperienceList: { label: string; value: string }[],
        skillsList: { label: string; value: string }[]
    ) => {
        const allFilterOptions: FilterOption[] = [
            {
                key: "education",
                label: "学历",
                type: "select",
                enabled: true,
                width: 120,
                options: educationList,
            },
            {
                key: "workExperience",
                label: "工作年限",
                type: "select",
                enabled: true,
                width: 130,
                options: workExperienceList,
            },
            {
                key: "company",
                label: "公司",
                type: "select",
                enabled: true,
                width: 120,
                options: [
                    { value: "字节跳动", label: "字节跳动" },
                    { value: "阿里巴巴", label: "阿里巴巴" },
                    { value: "腾讯", label: "腾讯" },
                    { value: "百度", label: "百度" },
                ],
            },
            {
                key: "position",
                label: "任职职位",
                type: "select",
                enabled: true,
                width: 130,
                options: [
                    { value: "前端工程师", label: "前端工程师" },
                    { value: "后端工程师", label: "后端工程师" },
                    { value: "算法工程师", label: "算法工程师" },
                    { value: "产品经理", label: "产品经理" },
                ],
            },
            {
                key: "school",
                label: "学校",
                type: "select",
                enabled: true,
                width: 120,
                options: [
                    { value: "清华大学", label: "清华大学" },
                    { value: "北京大学", label: "北京大学" },
                    { value: "复旦大学", label: "复旦大学" },
                    { value: "上海交通大学", label: "上海交通大学" },
                ],
            },
            {
                key: "skills",
                label: "技能标签",
                type: "multiple",
                enabled: true,
                width: 180,
                options: skillsList,
            },
            {
                key: "graduationDate",
                label: "毕业时间",
                type: "date",
                enabled: true,
                width: 200,
            },
        ];
        setFilterOptions(allFilterOptions);
    };

    // 定义排序选项
    const initializeSortOptions = (sortOptions: SortOption[]) => {
        setSortOptions(sortOptions);
    };

    // 获取筛选条件数据
    const getFilterList = async () => {
        if (currentJob?.id) {
            const res: RespParams<CandidateFilterResp> = await (
                await getCandidateFilterListApi(currentJob.id, currentCandidateType)
            ).json();
            if (res.code === 200) {
                initializeFilterOptions(
                    res?.data?.educationLevelIds.map((item) => ({ label: item.key, value: item.value })),
                    res?.data?.workExperienceYears.map((item) => ({ label: item.key, value: item.value })),
                    res?.data?.techTags.map((item) => ({ label: item.key, value: item.value }))
                );
                initializeSortOptions(
                    res?.data?.sortFields.map((item) => ({ key: item.value, label: item.key, direction: null }))
                );
                resetFilters(); // 重置筛选条件
            }
        }
    };

    useEffect(() => {
        // 监听全屏状态变化
        const handleFullscreenChange = () => {
            setIsFullScreen(!!document.fullscreenElement);
        };
        document.addEventListener("fullscreenchange", handleFullscreenChange);
        getAllTags();

        if (mode === "Interviewer") {
            setCurrentCandidateType(1);
        }
        if (currentPagination) {
            pagination.pageNum = currentPagination.pageNum;
            pagination.pageSize = currentPagination.pageSize;
        }

        return () => {
            document.removeEventListener("fullscreenchange", handleFullscreenChange);

            // 切换页面，清空选中数据
            setSelectedCandidate([]);
        };
    }, []);

    // 创建一个稳定的防抖更新函数
    const debouncedUpdate = useCallback(
        debounce((newHeight) => {
            if (Math.abs(newHeight - prevHeightRef.current) > 1) {
                prevHeightRef.current = newHeight;
                setContentHeight(newHeight);
            }
        }, 50),
        []
    );
    useEffect(() => {
        const observe = new ResizeObserver((entries) => {
            for (const entry of entries) {
                const { height } = entry.target.getBoundingClientRect();
                const elementRect = entry.target.getBoundingClientRect();
                const parentElement = entry.target.parentElement;
                const parentRect = parentElement?.getBoundingClientRect();
                const offsetFromParentPrecise = Math.ceil(elementRect?.top - (parentRect?.top ?? 0));
                debouncedUpdate(offsetFromParentPrecise + height);
            }
        });

        if (headerRef.current) {
            observe.observe(headerRef.current);
        }

        return () => {
            observe.disconnect();
            debouncedUpdate.cancel();
        };
    }, [debouncedUpdate]);

    useEffect(() => {
        if (currentJob) {
            if (assignStatus) {
                getParentCandidateList();
            }
            if (filterStatus) {
                getInterviewFlowList();
            }
            getFilterList();
        }
    }, [currentJob?.id]);

    const jobName = useMemo(
        () => currentJob?.jobNameInner?.split("/")?.[currentJob?.jobNameInner?.split("/")?.length - 1],
        [currentJob]
    );

    const getAllTags = async () => {
        const res: any = await (await getAllTagsApi({ label: "", layer: "" })).json();
        if (res.code === 200) {
            setAllTags(res.data);
        }
    };

    const getInterviewFlowList = async () => {
        try {
            const res: RespParams<InterviewFlowResp> = await (await getInterviewFlowApi(currentJob?.id ?? -1)).json();
            if (res.code === 200) {
                const list: TrackStageStatistic[] = [
                    {
                        stageId: "all",
                        stageName: "全部",
                        stageCode: "all",
                        total: res.data.total,
                        trackStateStatistics: [],
                    },
                    {
                        stageId: "discard",
                        stageName: "已终止",
                        stageCode: "discard",
                        total: res.data.totalOut,
                        trackStateStatistics: [],
                    },
                    ...res.data.trackStageStatistics.map((item) => {
                        item.trackStateStatistics.unshift({
                            stateId: "",
                            stateName: "全部",
                            stateCode: "all",
                            total: item.total,
                        });
                        item.currentState = item.trackStateStatistics?.[0];
                        return item;
                    }),
                ];

                // 当存在缓存时，优先加载缓存数据
                const current = list.find((item) => item.stageId === currentStage?.stageId);
                if (current) {
                    current.currentState = current?.trackStateStatistics.find(
                        (item) => item.stateId === currentStage?.currentState?.stateId
                    );
                }
                setStatusList(list);
                const exists = list.find((item) => item.stageId === currentStage?.stageId);
                const stage = list.find((item) => item.stageCode.includes("S0001"));

                if (mode === "Interviewer" && stage) {
                    stage.currentState = stage.trackStateStatistics.find((item) => item.stateCode.includes("U0007"));
                }
                if (!exists || mode === "Interviewer") {
                    setCurrentStage(stage);
                    setAllSearchParams((prev) => ({ ...prev, currentStage: stage ? { ...stage } : undefined }));
                } else {
                    setCurrentStage(currentStage);
                    setAllSearchParams((prev) => ({
                        ...prev,
                        currentStage: currentStage ? { ...currentStage } : undefined,
                    }));
                }
            }
        } catch (error) {
            setStatusList([]);
            setCandidateList([]);
        }
    };

    useEffect(() => {
        setSelectedCandidate([]);
    }, [currentStage]);

    useEffect(() => {
        if (currentJob && assignStatus) {
            getParentCandidateList();
        }
    }, [allSearchParams.pagination]);

    const getCandidateList = useCallback(async () => {
        try {
            setLoading(true);
            const params: CandidateListReq = {
                pageNum: pagination.pageNum,
                pageSize: pagination.pageSize,
                jobId: currentJob?.id ?? -1,
                trackStageId: currentStage?.stageId ?? "",
                trackStatusId:
                    currentStage?.currentState?.stateId === "" ? undefined : currentStage?.currentState?.stateId,
                trackCurrentStatus: undefined,
                currentHandler: mode === "Interviewer" ? user.emp_id : null,
            };

            if (filters["graduationDate"]) {
                const startTime = filters["graduationDate"]?.[0]?.format("YYYY-MM-DD");
                const endTime = filters["graduationDate"]?.[1]?.format("YYYY-MM-DD");
                params.graduationStartDate = startTime;
                params.graduationEndDate = endTime;
            }
            if (filters["education"]) {
                params.educationLevelId = filters["education"];
            }
            if (filters["workExperience"]) {
                params.workExperienceYears = filters["workExperience"];
            }
            if (filters["skills"]) {
                params.techTags = filters["skills"];
            }
            if (filters["keyword"]) {
                params.keyword = filters["keyword"];
            }
            const sortList = allSearchParams["sortOptions"].filter((item) => item.direction);
            params.orderBy = sortList.map((item) => ({
                field: item.key,
                direction: item.direction === "asc" ? 1 : -1,
            }));

            // 筛选全部状态
            if (currentStage?.stageId === "all") {
                params.trackCurrentStatus = undefined;
                params.trackStageId = undefined;
                params.trackStatusId = undefined;
            }
            // 筛选已终止状态
            if (currentStage?.stageId === "discard") {
                params.trackCurrentStatus = 3;
                params.trackStageId = undefined;
                params.trackStatusId = undefined;
            }

            setCandidateSearchForm(params);
            const res: RespPaginationParams<CandidateListResp> = await (
                await getCandidateListByJobIdApi(params)
            ).json();
            if (res.code === 200) {
                setCandidateList(
                    res?.data?.records?.map((item) => {
                        const hardRequirements = item.application.portraitAnalysis.abilityLayerScore.hardRequirements
                            .slice(0, 5)
                            .map((item) => {
                                const currentTags = allTags.find((tag) => tag.id === item.tag);
                                return { ...currentTags, isMatched: item.isMatched };
                            });
                        const capabilityFit = item.application.portraitAnalysis.abilityLayerScore.capabilityFit
                            .slice(0, 5)
                            .map((item) => {
                                const currentTags = allTags.find((tag) => tag.id === item.tag);
                                return { ...currentTags, isMatched: item.isMatched };
                            });
                        const potentialForecast = item.application.portraitAnalysis.abilityLayerScore.potentialForecast
                            .slice(0, 5)
                            .map((item) => {
                                const currentTags = allTags.find((tag) => tag.id === item.tag);
                                return { ...currentTags, isMatched: item.isMatched };
                            });

                        item.allTags = [...hardRequirements, ...capabilityFit, ...potentialForecast].filter(
                            (item) => item.isMatched
                        );

                        item.application.portraitAnalysis.abilityLayerScore.hardRequirementsTags = hardRequirements;
                        item.application.portraitAnalysis.abilityLayerScore.capabilityFitTags = capabilityFit;
                        item.application.portraitAnalysis.abilityLayerScore.potentialForecastTags = potentialForecast;
                        return item;
                    }) ?? []
                );
                pagination.total = res?.data.total ?? 0;
                setLoading(false);
            }
        } catch (error) {
            setCandidateList([]);
            setLoading(false);
        }
    }, [allSearchParams]);

    useEffect(() => {
        if (currentStage && currentJob) {
            getCandidateList();
        }
    }, [getCandidateList]);

    const getParentCandidateList = async () => {
        try {
            setLoading(true);
            const params: CandidateListReq = {
                pageNum: pagination.pageNum,
                pageSize: pagination.pageSize,
                jobId: currentJob?.id ?? -1,
                trackStageId: currentStage?.stageId ?? "",
                trackStatusId:
                    currentStage?.currentState?.stateId === ""
                        ? undefined
                        : (currentStage?.currentState?.stateId ?? ""),
                trackCurrentStatus: undefined,
                currentHandler: "",
            };

            if (filters["graduationDate"]) {
                const startTime = filters["graduationDate"]?.[0]?.format("YYYY-MM-DD");
                const endTime = filters["graduationDate"]?.[1]?.format("YYYY-MM-DD");
                params.graduationStartDate = startTime;
                params.graduationEndDate = endTime;
            }
            if (filters["education"]) {
                params.educationLevelId = filters["education"];
            }
            if (filters["workExperience"]) {
                params.workExperienceYears = filters["workExperience"];
            }
            if (filters["skills"]) {
                params.techTags = filters["skills"];
            }
            if (filters["keyword"]) {
                params.keyword = filters["keyword"];
            }
            setCandidateSearchForm(params);
            const res: RespPaginationParams<CandidateListResp> = await (
                await getParentCandidateListByJobIdApi(params)
            ).json();

            if (res.code === 200) {
                setCandidateList(
                    res?.data?.records?.map((item) => {
                        const hardRequirements = item.application.portraitAnalysis.abilityLayerScore.hardRequirements
                            .slice(0, 5)
                            .map((item) => {
                                const currentTags = allTags.find((tag) => tag.id === item.tag);
                                return { ...currentTags, isMatched: item.isMatched };
                            });
                        const capabilityFit = item.application.portraitAnalysis.abilityLayerScore.capabilityFit
                            .slice(0, 5)
                            .map((item) => {
                                const currentTags = allTags.find((tag) => tag.id === item.tag);
                                return { ...currentTags, isMatched: item.isMatched };
                            });
                        const potentialForecast = item.application.portraitAnalysis.abilityLayerScore.potentialForecast
                            .slice(0, 5)
                            .map((item) => {
                                const currentTags = allTags.find((tag) => tag.id === item.tag);
                                return { ...currentTags, isMatched: item.isMatched };
                            });

                        item.allTags = [...hardRequirements, ...capabilityFit, ...potentialForecast].filter(
                            (item) => item.isMatched
                        );

                        item.application.portraitAnalysis.abilityLayerScore.hardRequirementsTags = hardRequirements;
                        item.application.portraitAnalysis.abilityLayerScore.capabilityFitTags = capabilityFit;
                        item.application.portraitAnalysis.abilityLayerScore.potentialForecastTags = potentialForecast;
                        return item;
                    }) ?? []
                );
                pagination.total = res?.data.total ?? 0;
                setLoading(false);
            }
        } catch (error) {
            setCandidateList([]);
            setLoading(false);
        }
    };

    const handleAssign = () => {
        if (currentJob?.allocationMethod === 2) {
            assignRef.current?.showDialog();

            return;
        }

        messageService.warning("只有选择了分配方式为“手动分配”的职位才能进行分配操作!");
        return;
    };

    return (
        <div
            ref={mainContentRef}
            id="candidateMainId"
            className={`${styles["candidate-main-wrapper"]} ${isFullscreen ? "page-fullscreen" : ""}`}
        >
            <div ref={headerRef} className={styles["candidate-main__header"]}>
                <div className={styles["header"]}>
                    <div>{currentJob?.jobNameOuter}</div>
                    <div className={styles["header-right"]}>
                        <IconButton
                            icon={isFullscreen ? <ZoomOutIcon /> : <ZoomIcon />}
                            className={styles["more-button"]}
                            onClick={() => toggleFullScreen(mainContentRef.current, "candidateMainId")}
                            title={isFullscreen ? "退出全屏" : "进入全屏"}
                        />
                    </div>
                </div>
                {/* 页面为默认状态，且存在岗位面试流程数据后才会展示 */}
                {mode === "HR" && statusList.length > 0 && (
                    <InterviewStage
                        statusList={statusList}
                        currentStage={currentStage}
                        handleStatusClick={(item) => {
                            setCurrentStage(item);
                            goToPage(0);
                            setAllSearchParams((prev) => ({
                                ...prev,
                                item,
                                pagination: { ...pagination, pageNum: 0 },
                            }));
                        }}
                    />
                )}
                <FilterBar
                    filters={filters}
                    onSearch={handleSearch}
                    onFilterChange={handleMainFilterChange}
                    onResetFilters={() => {
                        resetFilters();
                        setAllSearchParams((prev) => ({ ...prev, filters: filters }));
                    }}
                    hasFilters={hasFilters}
                    filterOptions={filterOptions}
                    onFilterOptionsChange={handleFilterOptionsChange}
                    sortOptions={sortOptions}
                    onSortChange={handleSortChange}
                />
            </div>
            <div
                style={{ height: `calc(100% - ${contentHeight + 52}px)` }}
                className={styles["candidate-main__content"]}
            >
                {loading ? (
                    <Skeleton paragraph={{ rows: 10 }} active />
                ) : candidateList.length > 0 ? (
                    candidateList.map((talent) => {
                        const statusListCopy = cloneDeep(statusList);
                        const current = statusListCopy.find((item) => item.stageId === talent.application.trackStageId);
                        if (current) {
                            current.currentState = current?.trackStateStatistics.find(
                                (item) => item.stateId === talent.application.trackStatusId
                            );
                        }

                        return (
                            <TalentCard
                                key={talent.application.applicantId}
                                talent={talent}
                                jobName={jobName}
                                currentStage={current}
                                onClick={(talent) => {
                                    clear();
                                    navigate(Path.ResumeDetail, {
                                        state: {
                                            jobId: talent.application.jobId,
                                            jobName: jobName,
                                            applicantId: talent.application.applicantId,
                                            mode: mode,
                                            currentStage: current,
                                            // 是否是手动分配，适用于分配简历页面
                                            isManualDistribution: currentCandidateType === 1 ? false : true,
                                            searchForm: candidateSearchForm,
                                        },
                                    });
                                }}
                                isSelect={
                                    selectedCandidate.find(
                                        (item) => item.profile.applicantId === talent.profile.applicantId
                                    )
                                        ? true
                                        : false
                                }
                                handleCardSelect={(talent) => {
                                    const exists = selectedCandidate.find(
                                        (item) => item.profile.applicantId === talent.profile.applicantId
                                    );
                                    if (exists) {
                                        setSelectedCandidate(
                                            selectedCandidate.filter(
                                                (item) => item.profile.applicantId !== talent.profile.applicantId
                                            )
                                        );
                                    } else {
                                        setSelectedCandidate([...selectedCandidate, talent]);
                                    }
                                }}
                                renderOperationBtn={
                                    current?.stageCode.includes("S0001") &&
                                    current.currentState?.stateCode.includes("U0007") && (
                                        <Flex gap={12}>
                                            <Button
                                                size="small"
                                                color="danger"
                                                variant="outlined"
                                                onClick={(e) => {
                                                    e.stopPropagation();

                                                    const type =
                                                        currentStage?.stageCode === "S0001"
                                                            ? EVALUATE_TYPE.first
                                                            : EVALUATE_TYPE.other;
                                                    interviewEvaluationRef.current?.showDrawer(
                                                        type,
                                                        currentStage?.reviewTemplate ?? "",
                                                        talent.application.applicantId,
                                                        currentStage?.stageId ?? "",
                                                        talent.application.jobId,
                                                        "淘汰"
                                                    );
                                                }}
                                            >
                                                淘汰
                                            </Button>
                                            <Button
                                                size="small"
                                                color="green"
                                                variant="outlined"
                                                onClick={(e) => {
                                                    e.stopPropagation();

                                                    const type =
                                                        currentStage?.stageCode === "S0001"
                                                            ? EVALUATE_TYPE.first
                                                            : EVALUATE_TYPE.other;
                                                    interviewEvaluationRef.current?.showDrawer(
                                                        type,
                                                        currentStage?.reviewTemplate ?? "",
                                                        talent.application.applicantId,
                                                        currentStage?.stageId ?? "",
                                                        talent.application.jobId,
                                                        "通过"
                                                    );
                                                }}
                                            >
                                                通过
                                            </Button>
                                        </Flex>
                                    )
                                }
                            />
                        );
                    })
                ) : (
                    <Flex style={{ width: "100%", height: "100%" }} justify="center" align="center">
                        <Empty />
                    </Flex>
                )}
            </div>

            <Flex justify="space-between" align="center">
                <Checkbox
                    indeterminate={indeterminate}
                    onChange={() => {
                        if (checkAll) {
                            // 当前状态为全选，点击后取消当前页选中数据
                            setSelectedCandidate(
                                selectedCandidate.filter(
                                    (item) =>
                                        !candidateList.find(
                                            (item2) => item2.profile.applicantId === item.profile.applicantId
                                        )
                                )
                            );
                        } else {
                            // 点击后选中当前页所有数据，如果当前页数据与选中数据有重复，则去重
                            setSelectedCandidate(Array.from(new Set([...selectedCandidate, ...candidateList])));
                        }
                    }}
                    checked={checkAll}
                >
                    全选
                </Checkbox>
                <Pagination
                    size="small"
                    current={pagination.pageNum + 1}
                    pageSize={pagination.pageSize}
                    total={pagination.total}
                    showSizeChanger
                    showTotal={(total) => `共 ${total} 条`}
                    onChange={handlePageChange}
                    onShowSizeChange={handlePageSizeChange}
                />
            </Flex>
            <BottomMultiple selectedNum={selectedCandidate.length} handleClose={() => setSelectedCandidate([])}>
                {filterStatus && (
                    <Flex gap={12}>
                        {isResumeFirstStage && (
                            <span
                                style={{ cursor: "pointer" }}
                                onClick={() => {
                                    const validateRes = selectedCandidate.every((item) => {
                                        const currentState = currentStage?.trackStateStatistics.find(
                                            (item2) => item2.stateId === item?.application?.trackStatusId
                                        );

                                        return currentState?.stateCode.includes("U0007");
                                    });

                                    if (!validateRes) {
                                        messageService.warning("只有进行中状态才能批量淘汰");
                                        return;
                                    }

                                    const type =
                                        currentStage?.stageCode === "S0001" ? EVALUATE_TYPE.first : EVALUATE_TYPE.other;
                                    interviewEvaluationRef.current?.showDrawer(
                                        type,
                                        currentStage?.reviewTemplate ?? "",
                                        selectedCandidate.map((item) => item.profile.applicantId),
                                        currentStage?.stageId ?? "",
                                        selectedCandidate?.[0]?.application?.jobId,
                                        "淘汰"
                                    );
                                }}
                            >
                                批量淘汰
                            </span>
                        )}
                        {isResumeFirstStage && (
                            <span
                                style={{ cursor: "pointer" }}
                                onClick={() => {
                                    const validateRes = selectedCandidate.every((item) => {
                                        const currentState = currentStage?.trackStateStatistics.find(
                                            (item2) => item2.stateId === item?.application?.trackStatusId
                                        );

                                        return currentState?.stateCode.includes("U0007");
                                    });

                                    if (!validateRes) {
                                        messageService.warning("只有进行中状态才能批量通过");
                                        return;
                                    }

                                    const type =
                                        currentStage?.stageCode === "S0001" ? EVALUATE_TYPE.first : EVALUATE_TYPE.other;
                                    interviewEvaluationRef.current?.showDrawer(
                                        type,
                                        currentStage?.reviewTemplate ?? "",
                                        selectedCandidate.map((item) => item.profile.applicantId),
                                        currentStage?.stageId ?? "",
                                        selectedCandidate?.[0]?.application?.jobId,
                                        "通过"
                                    );
                                }}
                            >
                                批量通过
                            </span>
                        )}
                        {!isResumeFirstStage && (
                            <span
                                style={{ cursor: "pointer" }}
                                onClick={() => {
                                    const validateRes = selectedCandidate.every((item) => {
                                        const currentState = currentStage?.trackStateStatistics.find(
                                            (item2) => item2.stateId === item?.application?.trackStatusId
                                        );

                                        return (
                                            currentState?.stateCode.includes("U0001") ||
                                            currentState?.stateCode.includes("U0013")
                                        );
                                    });

                                    if (!validateRes) {
                                        messageService.warning("只有待安排/已拒绝状态才能安排面试");
                                        return;
                                    }

                                    if (currentStage && selectedCandidate && currentJob) {
                                        scheduleInterviewRef.current?.showDialog(
                                            currentStage,
                                            selectedCandidate,
                                            currentJob
                                        );
                                    }
                                }}
                            >
                                安排面试
                            </span>
                        )}
                    </Flex>
                )}
                {assignStatus && (
                    <span style={{ cursor: "pointer" }} onClick={handleAssign}>
                        简历分配
                    </span>
                )}
            </BottomMultiple>
            <ScheduleInterview
                ref={scheduleInterviewRef}
                refresh={() => {
                    if (filterStatus) {
                        getInterviewFlowList();
                    }
                    getCandidateList();
                    setSelectedCandidate([]);
                }}
            />
            <Assign
                ref={assignRef}
                selectedCandidate={selectedCandidate}
                currentJob={currentJob}
                onSuccess={onJobListRefresh}
            />
            <InterviewEvaluation
                ref={interviewEvaluationRef}
                refresh={() => {
                    getInterviewFlowList();
                    resetFilters();
                    setSelectedCandidate([]);
                    onJobListRefresh?.();
                }}
            />
        </div>
    );
};

export default CandidateMain;
