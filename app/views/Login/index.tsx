import { Button } from "antd";
import styles from "./index.module.scss";
import Image from "next/image";
import useUserInfoStore from "@/app/store/userInfo";
import { useNavigate } from "react-router-dom";
import { Path } from "@/app/constant";
import { useEffect } from "react";

const appName = process.env.NEXT_PUBLIC_APP_NAME;

export default function Login() {
    const { setState, setLogging } = useUserInfoStore();
    const navigate = useNavigate();

    useEffect(() => {
        // 跳转登录页，取消登录状态，避免重复跳转auth页面
        setLogging(false);
    }, []);

    const handleClick = (type: string) => () => {
        setState(type);
        if (type === "feishu") {
            setLogging(true);
            window.location.href = `${process.env.NEXT_PUBLIC_FETCH_BASE_API}auth/oauth/feishu/access-code`;
            return;
        }
        if (type === "cas") {
            setLogging(true);
            navigate(Path.Auth);
            return;
        }
    };

    return (
        <div className={styles["login-wrapper"]}>
            <div className={styles["login-form"]}>
                <div className={styles["login-logo"]}>
                    <Image src={"/favicon.ico"} alt="logo" width={40} height={40} />
                    <span className={styles["login-title"]}>{appName}</span>
                </div>
                <div className={styles["btn-wrapper"]}>
                    <Button size="large" type="primary" onClick={handleClick("feishu")}>
                        飞书登录
                    </Button>
                    <Button size="large" type="primary" onClick={handleClick("cas")}>
                        CAS登录
                    </Button>
                </div>
            </div>
        </div>
    );
}
