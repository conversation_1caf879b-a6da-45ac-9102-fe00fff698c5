export type Updater<T> = (updater: (value: T) => void) => void;

export const ROLES = ["system", "user", "assistant"] as const;
export type MessageRole = (typeof ROLES)[number];

export interface RequestMessage {
    role: MessageRole;
    content: string;
}

export type DalleSize = "1024x1024" | "1792x1024" | "1024x1792";
export type DalleQuality = "standard" | "hd";
export type DalleStyle = "vivid" | "natural";

export type ModelSize =
    | "1024x1024"
    | "1792x1024"
    | "1024x1792"
    | "768x1344"
    | "864x1152"
    | "1344x768"
    | "1152x864"
    | "1440x720"
    | "720x1440";

export type DefaultParams = Record<string, any>;

export interface ReqPaginationParams {
    page: number;
    size: number;
}

export interface RespParams<T> {
    code: number;
    msg: string | null;
    data: T;
}

export interface RespSpringPaginationParams<T> {
    code: number;
    msg: string | null;
    data: {
        records: T[];
        pageable: {
            sort: {
                empty: boolean;
                sorted: boolean;
                unsorted: boolean;
            };
            offset: number;
            pageNumber: number;
            pageSize: number;
            paged: boolean;
            unpaged: boolean;
        };
        last: boolean;
        totalPages: number;
        total: number;
        size: number;
        number: number;
        sort: {
            empty: boolean;
            sorted: boolean;
            unsorted: boolean;
        };
        first: boolean;
        numberOfElements: number;
        empty: boolean;
    };
}

// 流式返回信息
export interface StreamMessage {
    id: string;
    type: string;
    content: string;
}

// 字典返回数据类型
export interface DictData {
    key: string;
    value: string;
}

export interface PaginationInfo {
    pageNum: number;
    pageSize: number;
    total?: number;
}

export interface RespPaginationParams<T> {
    code: number;
    msg: string | null;
    data: {
        records: T[];
        pageNum: number;
        pageSize: number;
        total: number;
    };
}
