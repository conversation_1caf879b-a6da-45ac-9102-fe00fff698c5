import { useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import Locale from "./locales";

/**
 * 命令函数类型定义，接收一个字符串参数并执行操作
 */
type Command = (param: string) => void;

/**
 * 命令集合接口，定义了可用的命令类型
 * 包括填充、提交、掩码、代码和设置等命令
 */
interface Commands {
    fill?: Command;
    submit?: Command;
    mask?: Command;
    code?: Command;
    settings?: Command;
}

/**
 * URL 命令处理 Hook
 * 用于从 URL 搜索参数中解析并执行命令
 *
 * @param commands 可用命令的对象映射
 *
 * 工作原理：
 * 1. 检查 URL 参数中是否包含命令名称
 * 2. 如果找到匹配的命令，执行它并从 URL 中移除该参数
 * 3. 更新 URL，移除已处理的命令参数
 */
export function useCommand(commands: Commands = {}) {
    const [searchParams, setSearchParams] = useSearchParams();

    useEffect(() => {
        let shouldUpdate = false;
        searchParams.forEach((param, name) => {
            const commandName = name as keyof Commands;
            if (typeof commands[commandName] === "function") {
                commands[commandName]!(param);
                searchParams.delete(name);
                shouldUpdate = true;
            }
        });

        if (shouldUpdate) {
            setSearchParams(searchParams);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [searchParams, commands]);
}

/**
 * 聊天命令接口，定义了聊天相关的命令类型
 * 包括新建、下一个、上一个、清除、分支和删除等操作
 */
interface ChatCommands {
    new?: Command;
    newm?: Command;
    next?: Command;
    prev?: Command;
    clear?: Command;
    fork?: Command;
    del?: Command;
}

/**
 * 聊天命令前缀正则表达式
 * 兼容英文冒号":"和中文冒号"："
 */
export const ChatCommandPrefix = /^[:：]/;

/**
 * 聊天命令处理 Hook
 * 用于处理聊天输入框中的命令
 *
 * @param commands 可用聊天命令的对象映射
 *
 * 提供两个主要功能：
 * 1. 匹配命令并执行 - match()
 * 2. 搜索可用命令提示 - search()
 */
export function useChatCommand(commands: ChatCommands = {}) {
    /**
     * 提取命令名称（去除前缀冒号）
     *
     * @param userInput 用户输入的文本
     * @returns 去除冒号前缀的命令名称
     */
    function extract(userInput: string) {
        const match = userInput.match(ChatCommandPrefix);
        if (match) {
            return userInput.slice(1) as keyof ChatCommands;
        }
        return userInput as keyof ChatCommands;
    }

    /**
     * 搜索匹配的命令，用于提供自动完成建议
     *
     * @param userInput 用户输入的文本
     * @returns 匹配的命令列表及其描述
     */
    function search(userInput: string) {
        const input = extract(userInput);
        const desc = Locale.Chat.Commands;
        return Object.keys(commands)
            .filter((c) => c.startsWith(input))
            .map((c) => ({
                title: desc[c as keyof ChatCommands],
                content: ":" + c,
            }));
    }

    /**
     * 匹配命令并提供执行函数
     *
     * @param userInput 用户输入的文本
     * @returns 包含匹配状态和执行函数的对象
     */
    function match(userInput: string) {
        const command = extract(userInput);
        const matched = typeof commands[command] === "function";

        return {
            matched,
            invoke: () => matched && commands[command]!(userInput),
        };
    }

    return { match, search };
}
