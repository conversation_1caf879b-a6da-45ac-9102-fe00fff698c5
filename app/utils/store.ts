import { create } from "zustand";
import { combine, persist, createJSONStorage } from "zustand/middleware";
import { Updater } from "../typing";
import { deepClone } from "./clone";
import { indexedDBStorage } from "@/app/utils/indexedDB-storage";

type SecondParam<T> = T extends (_f: infer _F, _s: infer S, ...args: infer _U) => any ? S : never;

type MakeUpdater<T> = {
    lastUpdateTime: number;
    _hasHydrated: boolean;

    markUpdate: () => void;
    update: Updater<T>;
    setHasHydrated: (state: boolean) => void;
};

type SetStoreState<T> = (
    partial: T | Partial<T> | ((state: T) => T | Partial<T>),
    replace?: boolean | undefined
) => void;

// 导出一个函数 createPersistStore，用于创建一个持久化存储
export function createPersistStore<T extends object, M>(
    // 初始状态 state，类型为 T
    state: T,
    // 方法 methods，接受两个参数 set 和 get，返回类型为 M
    methods: (set: SetStoreState<T & MakeUpdater<T>>, get: () => T & MakeUpdater<T>) => M,
    // 持久化选项 persistOptions，类型为 SecondParam<typeof persist<T & M & MakeUpdater<T>>>
    persistOptions: SecondParam<typeof persist<T & M & MakeUpdater<T>>>
) {
    // 设置持久化选项的存储方式为 JSON 格式的 indexedDB
    persistOptions.storage = createJSONStorage(() => indexedDBStorage);
    // 保存旧的 onRehydrateStorage 回调函数
    const oldOonRehydrateStorage = persistOptions?.onRehydrateStorage;
    // 重写 onRehydrateStorage 回调函数
    persistOptions.onRehydrateStorage = (state) => {
        // 调用旧的 onRehydrateStorage 回调函数
        oldOonRehydrateStorage?.(state);
        // 返回一个函数，用于在重新水合后标记为已水合
        return () => state.setHasHydrated(true);
    };

    // 创建并返回一个持久化存储
    return create(
        // 使用 persist 函数包装 combine 函数返回的状态
        persist(
            combine(
                // 合并初始状态和额外的字段
                {
                    ...state,
                    lastUpdateTime: 0, // 最后更新时间
                    _hasHydrated: false, // 是否已水合
                },
                // 返回一个对象，包含 methods 中的方法和额外的更新方法
                (set, get) => {
                    return {
                        ...methods(set, get as any),

                        // 标记更新时间
                        markUpdate() {
                            set({ lastUpdateTime: Date.now() } as Partial<T & M & MakeUpdater<T>>);
                        },
                        // 更新状态并标记更新时间
                        update(updater) {
                            const state = deepClone(get());
                            updater(state);
                            set({
                                ...state,
                                lastUpdateTime: Date.now(),
                            });
                        },
                        // 设置是否已水合
                        setHasHydrated: (state: boolean) => {
                            set({ _hasHydrated: state } as Partial<T & M & MakeUpdater<T>>);
                        },
                    } as M & MakeUpdater<T>;
                }
            ),
            // 将持久化选项作为参数传递给 persist 函数
            persistOptions as any
        )
    );
}
