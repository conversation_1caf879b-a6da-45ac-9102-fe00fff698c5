import { message } from "antd";
import { MessageInstance } from "antd/es/message/interface";
import { ReactNode } from "react";

// 确保在客户端环境中执行
const isClient = typeof window !== "undefined";

class MessageService {
    private messageInstance: MessageInstance;

    constructor() {
        message.config({
            duration: 3,
            maxCount: 1,
        });

        this.messageInstance = message;
    }

    public error(content: string | ReactNode) {
        if (isClient) {
            this.messageInstance.open({
                type: "error",
                content,
            });
        }
    }

    public success(content: string | ReactNode) {
        if (isClient) {
            this.messageInstance.open({
                type: "success",
                content,
            });
        }
    }

    public warning(content: string | ReactNode) {
        if (isClient) {
            this.messageInstance.open({
                type: "warning",
                content,
            });
        }
    }

    public info(content: string | ReactNode) {
        if (isClient) {
            this.messageInstance.open({
                type: "info",
                content,
            });
        }
    }
}

const messageService = new MessageService();

export default messageService;
