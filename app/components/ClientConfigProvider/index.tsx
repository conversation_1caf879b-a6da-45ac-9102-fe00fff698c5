"use client";

import useFullScreenStore from "@/app/store/modules/fullscreen";
import { ConfigProvider } from "antd";
import zhCN from "antd/locale/zh_CN";
import { ReactNode } from "react";
import <PERSON><PERSON>he<PERSON> from "../VersionChecker";

const ClientConfigProvider = ({ children }: { children: ReactNode }) => {
    const { fullScreenContainerId } = useFullScreenStore((state) => ({
        fullScreenContainerId: state.fullScreenContainerId,
    }));

    return (
        <ConfigProvider
            theme={{
                token: {
                    colorPrimary: "#0099f2",
                    colorPrimaryBg: "#e6f5fe",
                },
                components: {
                    Tooltip: { colorBgSpotlight: "#fff", colorTextLightSolid: "rgba(0, 0, 0, 0.88)" },
                    Layout: { siderBg: "#fff", bodyBg: "#fff", headerBg: "#fff" },
                    Tree: {
                        indentSize: 24,
                        directoryNodeSelectedBg: "#f5f5f5",
                        nodeSelectedBg: "#f5f5f5",
                        titleHeight: 28,
                    },
                },
            }}
            locale={zhCN}
            form={{ validateMessages: { required: "${label} 不能为空" } }}
            getPopupContainer={(node) => {
                if (!document) return null as any;
                return document.getElementById(fullScreenContainerId) ?? document.body;
            }}
        >
            {children}
            <VersionChecker />
        </ConfigProvider>
    );
};

export default ClientConfigProvider;
