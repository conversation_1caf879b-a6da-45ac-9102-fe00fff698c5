.card {
  display: flex;
  border-radius: 8px;
  background-color: transparent;
  transition: all 0.3s ease;
  cursor: pointer;
  
//   &:hover {
//     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
//     transform: translateY(-2px);
//   }
}

.imageContainer {
  flex-shrink: 0;
  margin-right: 16px;
  width: 76px;
  height: 56px;
  overflow: hidden;
  border-radius: 4px;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0;
  gap: 4px;
}

.title {
  margin: 0 0 4px;
  font-weight: 600;
  font-size: 14px;
  color: rgba(0,0,0,0.92);
}

.description {
  margin: 0 0 4px;
  font-weight: 400;
  font-size: 12px;
  color: rgba(0,0,0,0.68);
}

.time {
  @extend .description;
  margin: 0;
}
