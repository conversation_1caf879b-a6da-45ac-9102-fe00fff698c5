import { ActionType } from "@ant-design/pro-table/es/typing";

export interface ReqUserList {
    empName?: string;
    deptCodePath?: string;
    pageNum: number;
    pageSize: number;
}

export interface RespUser {
    empId: string;
    oprId: string;
    empName: string;
    deptCode: string;
    deptName: string;
    avatar?: string;
    spare_date?: string[];
}

export interface UserSelectExpose {
    tableRef: ActionType | null;
    getSelectedUser: RespUser[];
    setSelectedUser: (user: any[]) => void;
}

export interface PreviewFileExpose {
    showDialog: (fileName: string, blob?: Blob) => void;
}
