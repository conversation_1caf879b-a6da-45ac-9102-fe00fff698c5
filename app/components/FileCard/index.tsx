"use client";
import React from "react";
import { TextView } from "../TextView";
import styles from "./index.module.scss";

export interface FileCardProps {
    /** 要显示的文件名 */
    title: string;
    // 文件大小
    size: string;
    // 文件类型
    type: string;
    // 如果是图片, 此为图片的 Data URL; 否则为文件类型标识符 (例如 'pdf', 'word')
    icon: string;
}

// // 辅助函数或对象映射，用于将文件类型标识符映射到图标组件或SVG路径 (暂时移除)
// const fileIconMap: Record<string, React.FC<React.SVGProps<SVGSVGElement>> | string> = {
//     // pdf: PdfIcon,
//     // word: WordIcon,
//     // generic: GenericFileIcon,
//     // text: SomeTextFileIcon,
//     // excel: SomeExcelIcon,
//     // powerpoint: SomePowerPointIcon,
//     // default: GenericFileIcon,
// };

// 文件类型简化函数
function getSimplifiedFileType(fileType: string): string {
    const fileTypeMap: Record<string, string> = {
        "application/pdf": "PDF",
        "application/msword": "Word",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "Word",
        "application/vnd.ms-excel": "Excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "Excel",
        "application/vnd.ms-powerpoint": "PowerPoint",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation": "PowerPoint",
        "text/": "文本文件",
        "image/": "图片",
        "audio/": "音频",
        "video/": "视频",
    };

    for (const key in fileTypeMap) {
        if (fileType.startsWith(key)) {
            return fileTypeMap[key];
        }
    }

    return "未知文件";
}

export function FileCard({ title, size, type, icon }: FileCardProps) {
    const isImage = type.startsWith("image/");
    const simplifiedType = getSimplifiedFileType(type);

    const renderFileIcon = () => {
        if (isImage) {
            return <img src={icon} alt={title} className={styles["file-preview-image"]} />;
        }

        // 根据 icon 标识符 (例如 'pdf', 'word') 获取对应的图标
        // const IconComponent = fileIconMap[icon] || fileIconMap.default;
        // if (typeof IconComponent === 'string') {
        //   return <img src={IconComponent} alt={type} className={styles['file-type-icon']} />;
        // } else if (IconComponent) {
        //   return <IconComponent className={styles['file-type-icon']} />;
        // }
        // return null; // 或者返回一个默认的通用文件图标

        // 当前的占位符实现:
        return <span className={styles["file-type-placeholder"]}>{icon.toUpperCase()}</span>;
    };

    return (
        <div className={styles["card-wrapper"]}>
            <div className={styles["card-icon"]}>{renderFileIcon()}</div>
            <div className={styles["card-content"]}>
                <div className={styles["card-name"]}>
                    <TextView text={title}></TextView>
                </div>
                <div className={styles["card-size"]}>
                    <div>
                        <TextView text={simplifiedType}></TextView>
                    </div>
                    <div>{size}</div>
                </div>
            </div>
        </div>
    );
}
