import React from "react";
import clsx from "clsx";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Upload } from "antd";
// import { UploadChangeParam, UploadFile } from "antd/es/upload/interface";
import Icon from "@ant-design/icons";
import DeleteIcon from "../../icons/clear.svg";
import styles from "./index.module.scss";
import UploadIcon from "../../icons/chat/upload.svg";
import ScreenshotIcon from "../../icons/chat/screenshot.svg";
import VoiceIcon from "../../icons/chat/voice.svg";
import EnterIcon from "../../icons/chat/enter.svg";
import { FileCard } from "../FileCard";
import { formatFileSize } from "../../utils/format";

// 定义附加文件的接口
export interface AttachedFile {
    name: string;
    size: string; // 文件大小已经格式化
    type: string;
    url: string; // 始终是 data URL 或服务器 URL
}

export interface Cha<PERSON>InputProps {
    inputRef: React.RefObject<HTMLTextAreaElement>;
    userInput: string;
    onUserInput: (text: string) => void;
    onKeyDown: (event: React.KeyboardEvent<HTMLTextAreaElement>) => void;
    onPaste: (event: React.ClipboardEvent<HTMLTextAreaElement>) => void;
    onSubmit: () => void;
    attachImages: AttachedFile[];
    setAttachImages: (images: AttachedFile[]) => void;
    inputRows: number;
    placeholder: string;
    sendButtonText: string;
    fontSize: string | number;
    fontFamily: string;
    autoFocus?: boolean;
    isLoading?: boolean;
    scrollToBottom?: () => void;
}

// Local DeleteImageButton component
interface DeleteImageButtonProps {
    deleteImage: () => void;
}

interface BottomBtnProps {
    onSubmit: () => void;
    userInput: string;
    setAttachImages: (images: AttachedFile[]) => void;
    attachImages: AttachedFile[];
}

function DeleteImageButton({ deleteImage }: DeleteImageButtonProps) {
    return (
        <div className={styles["delete-btn"]} onClick={deleteImage}>
            <DeleteIcon />
        </div>
    );
}

// 获取简化的文件类型名称
function getSimplifiedFileType(fileType: string): string {
    const fileTypeMap: Record<string, string> = {
        "application/pdf": "PDF",
        "application/msword": "Word",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "Word",
        "application/vnd.ms-excel": "Excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "Excel",
        "application/vnd.ms-powerpoint": "PowerPoint",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation": "PowerPoint",
        "text/": "文本文件",
        "image/": "图片",
        "audio/": "音频",
        "video/": "视频",
        // 可以根据需要添加更多文件类型
    };

    for (const key in fileTypeMap) {
        if (fileType.startsWith(key)) {
            return fileTypeMap[key];
        }
    }

    return "未知文件"; // 默认文件类型名称
}

function BottomBtn({ onSubmit, userInput, setAttachImages, attachImages }: BottomBtnProps) {
    const hasContent = userInput && userInput.trim().length > 0;

    const handleFileUpload = (file: File) => {
        const reader = new FileReader();
        reader.onload = (e) => {
            if (e.target?.result) {
                const fileDataUrl = e.target.result as string;
                const newFile: AttachedFile = {
                    name: file.name,
                    size: formatFileSize(file.size),
                    type: file.type,
                    url: fileDataUrl, // url 字段依然保存原始 DataURL
                };
                setAttachImages([...attachImages, newFile]);
            }
        };
        reader.readAsDataURL(file);
        return false; // 阻止默认上传行为
    };

    const fileChange = (file?: File) => {
        console.log("file", file);
    };

    return (
        <div className={styles["bottom-box"]}>
            <Tooltip title="上传文件">
                <Upload
                    beforeUpload={(file) => handleFileUpload(file)}
                    onChange={(info: any) => {
                        fileChange(info.file);
                    }}
                    showUploadList={false}
                >
                    <Button icon={<UploadIcon />} />
                </Upload>
            </Tooltip>

            <div className={styles["content-right"]}>
                <Tooltip title="截图提问">
                    <Button icon={<ScreenshotIcon />} type="text"></Button>
                </Tooltip>
                <Tooltip title="语音输入">
                    <Button icon={<VoiceIcon />} type="text"></Button>
                </Tooltip>

                <Divider type="vertical"></Divider>
                <Tooltip title="发送">
                    <div
                        className={clsx(styles["enter-icon"], {
                            [styles["enter-icon-active"]]: hasContent,
                        })}
                        onClick={hasContent ? onSubmit : undefined}
                    >
                        <Icon component={EnterIcon}></Icon>
                    </div>
                </Tooltip>
            </div>
        </div>
    );
}

export function ChatInput({
    inputRef,
    userInput,
    onUserInput,
    onKeyDown,
    onPaste,
    onSubmit,
    attachImages,
    setAttachImages,
    inputRows,
    placeholder,
    // sendButtonText,
    fontSize,
    fontFamily,
    autoFocus,
    scrollToBottom, // isLoading,
}: ChatInputProps) {
    // console.log("isLoading", isLoading);

    return (
        // 用label可以实现双缓冲
        <label
            className={clsx(styles["chat-input-panel-inner"], {
                [styles["chat-input-panel-inner-attach"]]: attachImages.length !== 0,
            })}
            htmlFor="chat-input"
        >
            {attachImages.length !== 0 && (
                <div className={styles["attach-images"]}>
                    {attachImages.map((file, index) => (
                        <div key={index} className={styles["attached-file-item-wrapper"]}>
                            <FileCard
                                title={file.name}
                                size={file.size}
                                type={file.type}
                                icon={file.type.startsWith("image/") ? file.url : getSimplifiedFileType(file.type)}
                            />

                            <DeleteImageButton
                                deleteImage={() => {
                                    setAttachImages(attachImages.filter((_, i) => i !== index));
                                }}
                            />
                        </div>
                    ))}
                </div>
            )}
            <textarea
                id="chat-input"
                ref={inputRef}
                className={styles["chat-input"]}
                placeholder={placeholder}
                onInput={(e) => onUserInput(e.currentTarget.value)}
                value={userInput}
                onKeyDown={onKeyDown}
                onFocus={scrollToBottom}
                onClick={scrollToBottom}
                onPaste={onPaste}
                rows={inputRows}
                autoFocus={autoFocus}
                style={{
                    fontSize: fontSize,
                    fontFamily: fontFamily,
                }}
            />

            <BottomBtn
                userInput={userInput}
                onSubmit={onSubmit}
                setAttachImages={setAttachImages}
                attachImages={attachImages}
            />
            {/* <IconButton
                icon={<SendWhiteIcon />}
                text={sendButtonText}
                className={styles["chat-input-send"]}
                type="primary"
                onClick={onSubmit}
            /> */}
        </label>
    );
}
