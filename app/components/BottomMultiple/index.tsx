import { Divider, Flex } from "antd";
import styles from "./index.module.scss";
import { CloseOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";

const BottomMultiple = ({
    selectedNum,
    children,
    handleClose,
}: {
    selectedNum: number;
    children: React.ReactNode;
    handleClose?: () => void;
}) => {
    const [showContent, setShowContent] = useState(false);

    useEffect(() => {
        setShowContent(selectedNum > 0);
    }, [selectedNum]);

    return (
        showContent && (
            <div className={styles["select-wrapper"]}>
                <div className={styles["select-content"]}>
                    <CloseOutlined
                        className={styles["close-btn"]}
                        onClick={() => {
                            setShowContent(false);
                            handleClose?.();
                        }}
                    />

                    <span>已选中{selectedNum}条数据</span>
                    <Divider
                        style={{
                            marginLeft: 16,
                            marginRight: 28,
                            backgroundColor: "#FFFFFF66",
                            width: "1.5px",
                        }}
                        type="vertical"
                    />
                    <Flex align="center" gap={28}>
                        {children}
                    </Flex>
                </div>
            </div>
        )
    );
};

export default BottomMultiple;
