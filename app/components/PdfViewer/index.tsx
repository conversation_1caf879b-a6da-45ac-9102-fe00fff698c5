"use client";

import React, { useEffect, useRef, useState } from "react";
import type { PDFDocumentProxy } from "pdfjs-dist/types/src/display/api";
import { message, Button } from "antd";
import styles from "./index.module.scss";

// 分页/连续
export type ViewMode = "paginated" | "continuous";

interface PDFViewerProps {
    pdfUrl: string;
    fileName?: string;
    viewMode?: ViewMode; // 显示模式：分页或连续滚动
}

export function PDFViewer({ pdfUrl, fileName, viewMode = "paginated" }: PDFViewerProps) {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const [numPages, setNumPages] = useState<number>(0);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [pdfDocument, setPdfDocument] = useState<PDFDocumentProxy | null>(null);

    // 加载PDF文档
    useEffect(() => {
        let isMounted = true;

        async function loadPDF() {
            try {
                setIsLoading(true);
                setError(null); // 清除之前的错误状态

                // 动态导入pdfjs-dist包
                const pdfjsLib = await import("pdfjs-dist");

                pdfjsLib.GlobalWorkerOptions.workerSrc = "/pdf.worker.js";

                // 配置PDF.js以确保浏览器兼容性
                const loadingTask = pdfjsLib.getDocument({
                    url: pdfUrl,
                });
                const pdf = await loadingTask.promise;

                if (isMounted) {
                    setPdfDocument(pdf);
                    setNumPages(pdf.numPages);
                    // 重置当前页面到第1页，防止新PDF页数少于当前页数时出错
                    setCurrentPage(1);
                    setIsLoading(false);
                }
            } catch (err) {
                console.error("加载PDF失败:", err);
                if (isMounted) {
                    // 提供更详细的错误信息
                    const errorMessage = "无法加载PDF文件，请稍后重试。";
                    setError(errorMessage);
                    setIsLoading(false);
                }
            }
        }

        loadPDF();

        return () => {
            isMounted = false;
        };
    }, [pdfUrl]);

    // 渲染单个PDF页面（分页模式）
    useEffect(() => {
        if (viewMode !== "paginated" || !pdfDocument || !canvasRef.current || numPages === 0) return;

        async function renderPage() {
            try {
                // 确保 pdfDocument 非空且当前页面在有效范围内
                if (!pdfDocument || currentPage < 1 || currentPage > numPages) return;

                const page = await pdfDocument.getPage(currentPage);
                const canvas = canvasRef.current;

                if (!canvas) return;

                const viewport = page.getViewport({ scale: 1.5 });
                canvas.height = viewport.height;
                canvas.width = viewport.width;

                const renderContext = {
                    canvasContext: canvas.getContext("2d")!,
                    viewport: viewport,
                };

                await page.render(renderContext).promise;
            } catch (err) {
                console.error("渲染PDF页面失败:", err);
                setError("无法渲染PDF页面。");
            }
        }

        renderPage();
    }, [pdfDocument, currentPage, numPages, viewMode]);

    // 渲染所有PDF页面（连续模式）
    useEffect(() => {
        if (viewMode !== "continuous" || !pdfDocument || !containerRef.current || numPages === 0) return;

        async function renderAllPages() {
            try {
                const container = containerRef.current;
                if (!container) return;

                // 清空容器
                container.innerHTML = "";
                const newRenderedPages = new Map<number, HTMLCanvasElement>();

                for (let pageNum = 1; pageNum <= numPages; pageNum++) {
                    if (!pdfDocument) {
                        return;
                    }
                    const page = await pdfDocument.getPage(pageNum);
                    const canvas = document.createElement("canvas");
                    const viewport = page.getViewport({ scale: 1.5 });

                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    const renderContext = {
                        canvasContext: canvas.getContext("2d")!,
                        viewport: viewport,
                    };

                    await page.render(renderContext).promise;

                    // 直接添加canvas，无页码标签和间距
                    container.appendChild(canvas);
                    newRenderedPages.set(pageNum, canvas);
                }
            } catch (err) {
                console.error("渲染PDF页面失败:", err);
                setError("无法渲染PDF页面。");
            }
        }

        renderAllPages();
    }, [pdfDocument, numPages, viewMode]);

    // 当PDF页数变化时，确保当前页面在有效范围内
    useEffect(() => {
        if (numPages > 0 && currentPage > numPages) {
            setCurrentPage(1);
        }
    }, [numPages, currentPage]);

    // 页面切换函数
    const goToNextPage = () => currentPage < numPages && setCurrentPage(currentPage + 1);
    const goToPrevPage = () => currentPage > 1 && setCurrentPage(currentPage - 1);

    // 下载PDF文档
    const handleDownload = () => {
        // 创建一个临时链接
        const link = document.createElement("a");
        link.href = pdfUrl;
        // 从URL中提取文件名，如果没有文件名则使用默认名称
        const loadFileName = fileName || pdfUrl.split("/").pop();
        // 确保文件名以.pdf结尾
        const fileNameWithExtension =
            loadFileName && loadFileName.endsWith(".pdf") ? loadFileName : `${loadFileName}.pdf`;

        link.download = fileNameWithExtension;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        message.success("下载成功");
    };

    // 打印PDF文档
    const handlePrint = () => {
        if (!pdfUrl) {
            message.error("没有可用的PDF文件。");
            return;
        }

        const iframe = document.createElement("iframe");
        iframe.style.display = "none";
        iframe.src = pdfUrl;

        const cleanup = () => {
            if (iframe.parentNode) {
                iframe.parentNode.removeChild(iframe);
            }
            window.removeEventListener("afterprint", cleanup);
        };

        iframe.onload = () => {
            try {
                if (iframe.contentWindow) {
                    window.addEventListener("afterprint", cleanup);
                    iframe.contentWindow.focus();
                    iframe.contentWindow.print();
                } else {
                    throw new Error("无法访问iframe内容。");
                }
            } catch (e) {
                console.error("打印失败:", e);
                alert("无法打开打印对话框，请尝试在浏览器中打开并手动打印。");
                cleanup(); // 确保在出错时也进行清理
            }
        };

        iframe.onerror = () => {
            alert("加载PDF失败，无法打印。");
            cleanup();
        };

        document.body.appendChild(iframe);
    };

    if (isLoading) {
        return (
            <div style={{ textAlign: "center", padding: "20px" }}>
                <div>加载中...</div>
            </div>
        );
    }

    if (error) {
        return (
            <div style={{ textAlign: "center", padding: "20px", color: "#ff4d4f" }}>
                <div>{error}</div>
                <div style={{ marginTop: "10px" }}>
                    <a href={pdfUrl} target="_blank" rel="noopener noreferrer">
                        在浏览器中打开
                    </a>
                </div>
            </div>
        );
    }

    return (
        <div style={{ display: "flex", flexDirection: "column", alignItems: "center" }}>
            {viewMode === "paginated" ? (
                // 分页模式
                <>
                    <div style={{ overflowX: "auto", width: "100%" }}>
                        <canvas ref={canvasRef} style={{ display: "block", margin: "0 auto" }} />
                    </div>

                    <div className={styles["btn-group"]}>
                        <Button onClick={goToPrevPage} disabled={currentPage <= 1}>
                            上一页
                        </Button>
                        {numPages > 0 && (
                            <span style={{ margin: "0 10px" }}>
                                {currentPage} / {numPages}
                            </span>
                        )}
                        <Button onClick={goToNextPage} disabled={currentPage >= numPages}>
                            下一页
                        </Button>
                        <Button onClick={handlePrint}>打印</Button>
                        <Button onClick={handleDownload}>下载</Button>
                    </div>
                </>
            ) : (
                // 连续滚动模式
                <>
                    <div ref={containerRef} className={styles["continuous-container"]} />

                    <div className={styles["btn-group"]}>
                        <Button onClick={handlePrint}>打印</Button>
                        <Button onClick={handleDownload}>下载</Button>
                    </div>
                </>
            )}
        </div>
    );
}
