import React, { forwardRef, useEffect, useImperativeHandle, useLayoutEffect, useRef, useState } from "react";
import styles from "./index.module.scss";
import Quill from "quill";
import "quill/dist/quill.snow.css";

// 工具栏高度常量
const TOOLBAR_HEIGHT = 42; // 工具栏的实际高度

const parseHeight = (height: string | number): number => {
    if (typeof height === "number") return height;
    if (height.endsWith("px")) return parseInt(height);
    if (height.endsWith("rem")) return parseInt(height) * 16;
    if (height.endsWith("em")) return parseInt(height) * 16;
    if (height.endsWith("%")) return 200; // 默认值
    return parseInt(height) || 200;
};

// 定义编辑器的工具栏配置
const defaultModules = {
    toolbar: [
        [{ header: [1, 2, 3, 4, 5, 6, false] }],
        ["bold", "italic", "underline", "strike"],
        [{ color: [] }, { background: [] }],
        [{ align: [] }],
        [{ list: "ordered" }, { list: "bullet" }],
        ["link", "image"],
        ["clean"],
    ],
    clipboard: {
        matchVisual: false,
    },
};

// 定义组件Props类型
export interface RichTextEditorProps {
    id: string;
    placeholder?: string;
    readOnly?: boolean;
    modules?: any;
    formats?: string[];
    theme?: string;
    className?: string;
    style?: React.CSSProperties;
    height?: string | number;
}

// 定义组件Ref类型
export interface RichTextEditorRef {
    getHTML: () => string;
    setHTML: (HTML: string) => void;
    getText: () => string;
    setText: (text: string) => void;
    clear: () => void;
    focus: () => void;
    blur: () => void;
}

const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>((props, ref) => {
    const {
        id,
        placeholder = "请输入内容...",
        readOnly = false,
        modules = defaultModules,
        formats,
        theme = "snow",
        className = "",
        style = {},
        height = "200px",
    } = props;

    useEffect(() => {
        if (document.getElementById(id)) {
            quillRef.current = new Quill(document.getElementById(id)!, {
                placeholder: placeholder,
                readOnly: readOnly,
                theme: theme,
                modules: modules,
                formats: formats,
            });
        }

        return () => {
            quillRef.current = null;
        };
    }, []);

    // 创建编辑器引用
    const quillRef = useRef<any>(null);
    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
        getHTML: () => {
            return quillRef.current.getSemanticHTML() ?? "";
        },
        setHTML: (HTML: string) => {
            if (quillRef.current) {
                const htmlDelta = quillRef.current.clipboard.convert({ html: HTML });
                quillRef.current.setContents(htmlDelta);
            }
        },
        getText: () => {
            return quillRef.current?.getText() ?? "";
        },
        setText: (text: string) => {
            if (quillRef.current) {
                quillRef.current.setText(text);
            }
        },
        clear: () => {
            if (quillRef.current) {
                quillRef.current.setText("");
            }
        },
        focus: () => {
            if (quillRef.current) {
                quillRef.current.focus();
            }
        },
        blur: () => {
            if (quillRef.current) {
                quillRef.current.blur();
            }
        },
    }));

    // 计算编辑器容器的样式
    const getContainerStyle = () => {
        try {
            const heightValue =
                typeof height === "number" ? `${height}px` : height.includes("%") ? height : `${parseHeight(height)}px`;

            return {
                ...style,
                height: heightValue,
                minHeight: "100px",
            };
        } catch (e) {
            console.warn("Invalid height value:", height);
            return {
                ...style,
                height: "200px",
                minHeight: "100px",
            };
        }
    };

    // 计算编辑器的样式（减去工具栏高度）
    const getEditorStyle = () => {
        try {
            const totalHeight = parseHeight(height);
            const editorHeight = Math.max(totalHeight - TOOLBAR_HEIGHT, 100);

            return {
                height: `${editorHeight}px`,
                minHeight: "100px",
            };
        } catch (e) {
            console.warn("Error calculating editor height:", e);
            return {
                height: "158px", // 200px - 42px
                minHeight: "100px",
            };
        }
    };

    return (
        <div className={`${styles["rich-text-editor"]} ${className}`} style={getContainerStyle()}>
            <div id={id} style={getEditorStyle()}></div>
        </div>
    );
});

RichTextEditor.displayName = "RichTextEditor";

export default RichTextEditor;
