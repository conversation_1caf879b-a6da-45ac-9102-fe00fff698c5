import { Modal } from "antd";

interface ChatDialogProps {
    open: boolean;
    title?: string;
    onOk?: () => void;
    onCancel?: () => void;
    children: React.ReactNode;
    okText?: string;
    cancelText?: string;
    confirmLoading: boolean;
    width?: string | number;
    className?: string;
    footer?: React.ReactNode;
}

export default function ChatDialog(props: ChatDialogProps) {
    return (
        <Modal
            open={props.open}
            title={props.title ?? "标题"}
            okText={props?.okText ?? "确认"}
            cancelText={props?.cancelText ?? "取消"}
            onOk={props?.onOk}
            onCancel={props.onCancel}
            keyboard={false}
            maskClosable={false}
            getContainer={document.fullscreenElement ? false : document.body}
            confirmLoading={props.confirmLoading}
            footer={props?.footer}
            width={props?.width ?? 600}
            classNames={{
                body: props?.className ?? "chat-dialog-body",
                content: "chat-dialog-content",
                header: "chat-dialog-header",
                footer: "chat-dialog-footer",
            }}
        >
            {props.children}
        </Modal>
    );
}
