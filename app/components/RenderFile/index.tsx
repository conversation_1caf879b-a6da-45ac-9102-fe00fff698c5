import { But<PERSON>, Empty, Flex, Image } from "antd";
import { useCallback, useEffect, useRef, useState } from "react";
import { PDFViewer } from "../PdfViewer";
import { init } from "pptx-preview";
import { renderAsync } from "docx-preview";
import PreviewOrDownload from "../PreviewOrDownload";
import ViewMode from "../PdfViewer/index";

const RenderFile = ({
    blob,
    fileName,
    showPreview = false,
    showDownload = true,
    mode,
}: {
    blob?: Blob;
    fileName: string;
    showPreview?: boolean;
    showDownload?: boolean;
    mode?: ViewMode;
}) => {
    const [arrayBuffer, setArrayBuffer] = useState<ArrayBuffer | null>(null);
    const [fileType, setFileType] = useState<string>("");
    const [filePath, setFilePath] = useState<string>("");

    const fileTypeMap: { [key in string]: string } = {
        "application/pdf": "pdf",
        "text/html": "html",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation": "pptx",
        "image/jpeg": "png",
        "image/png": "png",
        "image/bmp": "png",
        "image/tiff": "png",
    };

    const docxWrapperRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!blob) return;

        const url = window.URL.createObjectURL(blob);
        setFilePath(url);

        const currentFileType = fileTypeMap[blob.type];
        setFileType(currentFileType);

        const reader = new FileReader();
        reader.onload = (event) => {
            setArrayBuffer(event.target?.result as ArrayBuffer);
        };
        reader.readAsArrayBuffer(blob);

        if (currentFileType === "docx") {
            renderAsync(blob, docxWrapperRef.current!, undefined, { debug: true }).catch((err) => {
                console.error("Error rendering docx file:", err);
                setFileType("error");
            });
        }

        if (currentFileType === "pptx") {
            init(docxWrapperRef.current!, {
                width: 500,
                height: 540,
            })
                .preview(arrayBuffer!)
                .catch((err) => {
                    console.error("Error rendering pptx file:", err);
                    setFileType("error");
                });
        }
    }, [blob]);

    const renderContent = useCallback(() => {
        if (fileType === "html") {
            return <iframe style={{ border: "none", width: "100%", minHeight: "60vh" }} src={filePath} />;
        }

        if (fileType === "pdf") {
            return <PDFViewer pdfUrl={filePath} fileName={fileName} viewMode={mode} />;
        }
        if (fileType === "docx" || fileType === "pptx") {
            return <div ref={docxWrapperRef} style={{ width: "80%" }}></div>;
        }
        if (fileType === "png") {
            return (
                <Flex justify="center">
                    <Image src={filePath} alt="preview" preview={false} width="80%" />
                </Flex>
            );
        }

        return <Empty description="暂不支持该文件类型" />;
    }, [fileType, filePath, fileName]);

    return (
        <Flex vertical>
            {(showPreview || showDownload) && (
                <PreviewOrDownload
                    fileBlob={blob}
                    fileName={fileName}
                    showPreview={showPreview}
                    showDownload={showDownload}
                />
            )}
            {renderContent()}
        </Flex>
    );
};

export default RenderFile;
