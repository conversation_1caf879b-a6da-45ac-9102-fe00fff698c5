import React, { useState } from "react";
import { <PERSON><PERSON>, Toolt<PERSON> } from "antd";
import { UpOutlined, DownOutlined, ClearOutlined } from "@ant-design/icons";
import SmartSearchBar from "./SmartSearchBar";
import FilterSelect from "./FilterSelect";
import FilterDatePicker from "./FilterDatePicker";
import { type FilterState } from "@/app/hooks/useFilterState";
import FilterIcon from "@/app/icons/talentTool/filter-icon.svg";
import styles from "./index.module.scss";
import DropdownButton from "./DropdownButton";
import SaveIcon from "@/app/icons/save.svg";
import SortIcon from "@/app/icons/sort.svg";
import DispositionIcon from "@/app/icons/disposition.svg";
import ViewToggleIcon from "@/app/icons/view-toggle.svg";

export interface SortOption {
    // 选项的键值
    key: string;
    // 选项的内容
    label: string;
    // 排序方向asc:升序 desc降序 null不排序
    direction?: "asc" | "desc" | null;
}

/** 筛选条件选项接口 */
export interface FilterOption {
    /** 筛选条件的键值 */
    key: string;
    /** 筛选条件的显示标签 */
    label: string;
    /** 筛选条件的类型 */
    type: "select" | "multiple" | "date" | "input";
    /** 筛选条件的选项（select 和 multiple 类型使用） */
    options?: { value: string; label: string }[];
    /** 是否启用该筛选条件 */
    enabled: boolean;
    /** 筛选条件的宽度 */
    width?: number;
}

/** 筛选区域组件属性接口 */
export interface FilterBarProps {
    /** 筛选条件 */
    filters?: FilterState;
    /** 是否展开状态 */
    expanded?: boolean;
    /** 筛选条件变化回调 */
    onFilterChange?: (filters: Partial<FilterState>) => void;
    /** 展开/收起回调 */
    onToggleExpand?: (expanded: boolean) => void;
    /** 搜索回调 */
    onSearch?: (keyword: string) => void;
    /** 重置筛选回调 */
    onResetFilters?: () => void;
    /** 是否有筛选条件 */
    hasFilters?: boolean;

    /** 排序选项 */
    sortOptions?: SortOption[];
    /** 排序变化回调 */
    onSortChange?: (sortOptions: SortOption[]) => void;
    /** 搜索框宽度 */
    searchBarWidth?: number;
    /** 是否显示保存按钮 */
    showSaveButton?: boolean;
    /** 是否显示视图切换按钮 */
    showViewToggle?: boolean;
    /** 是否显示配置按钮 */
    showDisposition?: boolean;
    /** 筛选条件配置 */
    filterOptions?: FilterOption[];
    /** 筛选条件配置变化回调 */
    onFilterOptionsChange?: (filterOptions: FilterOption[]) => void;
}

/**
 * 筛选区域主容器组件
 * 管理筛选区域的整体布局和状态
 */
const FilterBar: React.FC<FilterBarProps> = ({
    filters = {},
    expanded = true,
    onFilterChange,
    onToggleExpand,
    onSearch,
    onResetFilters,
    hasFilters = false,

    sortOptions = [],
    onSortChange,
    searchBarWidth = 360,
    showSaveButton = true,
    showViewToggle = true,
    showDisposition = true,
    filterOptions = [],
    onFilterOptionsChange,
}) => {
    const [isExpanded, setIsExpanded] = useState(expanded);

    /**
     * 处理展开/收起
     */
    const handleToggleExpand = () => {
        const newExpanded = !isExpanded;
        setIsExpanded(newExpanded);
        onToggleExpand?.(newExpanded);
    };

    /**
     * 处理筛选条件变化
     */
    const handleFilterChange = (key: keyof FilterState, value: any) => {
        onFilterChange?.({ [key]: value });
    };

    /**
     * 处理重置筛选
     */
    const handleResetFilters = () => {
        onResetFilters?.();
    };

    // 处理筛选条件配置
    const handleFilterOptionsChange = (newFilterOptions: FilterOption[]) => {
        onFilterOptionsChange?.(newFilterOptions);
    };

    // 渲染筛选组件
    const renderFilterComponent = (option: FilterOption) => {
        if (!option.enabled) return null;

        const { key, label, type, width, options } = option;

        switch (type) {
            case "select":
                return (
                    <FilterSelect
                        key={key}
                        label={label}
                        width={width}
                        value={filters[key as keyof FilterState] as string}
                        onChange={(value) => handleFilterChange(key as keyof FilterState, value)}
                        options={options || []}
                    />
                );
            case "multiple":
                return (
                    <FilterSelect
                        key={key}
                        label={label}
                        width={width}
                        mode="multiple"
                        value={filters[key as keyof FilterState] as string[]}
                        onChange={(value) => handleFilterChange(key as keyof FilterState, value)}
                        options={options || []}
                    />
                );
            case "date":
                return (
                    <FilterDatePicker
                        key={key}
                        label={label}
                        separator="至"
                        value={filters[key as keyof FilterState] as any}
                        onChange={(dates) => handleFilterChange(key as keyof FilterState, dates)}
                    />
                );
            default:
                return null;
        }
    };

    return (
        <div className={styles["filter-section"]}>
            <div className={styles["filter-row"]}>
                <div className={styles["filter-left"]}>
                    <DropdownButton
                        icon={<FilterIcon />}
                        text="筛选"
                        styleType="filter"
                        filterOptions={filterOptions}
                        onChange={(value) => {
                            if (
                                Array.isArray(value) &&
                                value.length > 0 &&
                                typeof value[0] === "object" &&
                                value[0] !== null &&
                                "key" in value[0] &&
                                "label" in value[0] &&
                                "enabled" in value[0]
                            ) {
                                handleFilterOptionsChange(value as FilterOption[]);
                            }
                        }}
                    ></DropdownButton>
                    <Button type="text" className={styles["collapse-button"]} onClick={handleToggleExpand}>
                        {isExpanded ? "收起筛选" : "展开筛选"}
                        {isExpanded ? <UpOutlined /> : <DownOutlined />}
                    </Button>
                    {showSaveButton && (
                        <Tooltip title="保存筛选条件">
                            <Button
                                type="text"
                                icon={<SaveIcon />}
                                className={styles["auto-save-button"]}
                                size="middle"
                            />
                        </Tooltip>
                    )}
                    {hasFilters && (
                        <Button
                            type="text"
                            icon={<ClearOutlined />}
                            className={styles["reset-button"]}
                            onClick={handleResetFilters}
                        >
                            清空筛选
                        </Button>
                    )}
                </div>
                <div className={styles["filter-right"]}>
                    <SmartSearchBar width={searchBarWidth} value={filters.keyword} onSearch={onSearch} />
                    <DropdownButton
                        icon={<SortIcon />}
                        text="排序"
                        styleType="sort"
                        sortOptions={sortOptions}
                        onChange={(value) => {
                            if (Array.isArray(value) && value.length > 0) {
                                onSortChange?.(value as SortOption[]);
                            }
                        }}
                    />
                    {showViewToggle && <Button icon={<ViewToggleIcon />} type="text" size="middle"></Button>}
                    {showDisposition && <Button icon={<DispositionIcon />} type="text" size="middle"></Button>}
                </div>
            </div>

            {isExpanded && <div className={styles["search-row"]}>{filterOptions.map(renderFilterComponent)}</div>}
        </div>
    );
};

export default FilterBar;
