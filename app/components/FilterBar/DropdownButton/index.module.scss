.filter-button {
    background: #e6f5fe;
    border-color: #e6f5fe;
    color: rgba(0, 0, 0, 0.86);
    font-family: "PingFang SC";
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    border-radius: 6px;
    height: 32px;
    padding: 0 12px;
    display: flex;
    align-items: center;
    gap: 8px;

    .filter-count {
        display: inline-block;
        background: #2f99f2;
        border-radius: 4px;
        padding: 0 6px;
        font-size: 12px;
        width: 18px;
        height: 18px;
        line-height: 18px;
        color: #fff;
        box-sizing: border-box;
    }
}

.sort-button {
    background: rgba(0, 0, 0, 0.04);
    border-color: #e6f5fe;
    border: 0;
    border-radius: 6px;
    height: 32px;
    padding: 0 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(0, 0, 0, 0.86);
    font-family: "PingFang SC";
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;  

    .sort-count {
        display: inline-block;
        padding: 0 6px;
        font-size: 12px;
        width: 18px;
        height: 18px;
        line-height: 18px;
        color: rgba(0, 0, 0, 0.86);
        border-radius: 4px;
        background: rgba(0, 0, 0, 0.08);
        vertical-align: middle;
        text-align: center;
        box-sizing: border-box;
    }
}

// 下拉箭头样式
.dropdown-arrow {
    font-size: 12px;
    margin-left: 4px;
    transition: transform 0.2s ease;
}

.sort-dropContent {
    display:flex;
    flex-direction: column;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    
    min-width: 200px;
    max-height: 300px;
    overflow-y: auto;

    .sort-drop-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 8px 0;
        font-weight: 500;
    }
}


// 排序菜单项样式
.sort-menu-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
        background-color: #f5f5f5;
    }

    .sort-label {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.86);
        font-family: "PingFang SC";
        font-weight: 400;
        flex: 1;
    }

    .sort-buttons {
        display: flex;
        gap: 4px;
        align-items: center;

        :global(.ant-btn) {
            width: 24px;
            height: 24px;
            min-width: unset;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;

            &[ant-click-animating-without-extra-node="true"]::before {
                display: none;
            }

            :global(.anticon) {
                font-size: 10px;
            }
        }
    }
}

// 筛选下拉菜单样式（待实现）
.filter-dropdown {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    padding: 16px;
    min-width: 200px;
    text-align: center;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
}

// 筛选下拉内容容器
.filter-dropContent {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    min-width: 260px;
    max-height: 400px;
    overflow-y: auto;

    .filter-drop-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 8px 0;
        font-weight: 500;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.86);
    }

    .filter-content {
        padding: 8px 12px;
        max-height: 320px;
        overflow-y: auto;
    }
}

// 筛选菜单项样式
.filter-menu-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
        background-color: #f5f5f5;
        border-radius: 4px;
        margin: 0 -8px;
        padding: 8px 8px;
    }

    :global(.ant-checkbox-wrapper) {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.86);
        font-family: "PingFang SC";
        font-weight: 400;
        flex: 1;
        
        &:hover {
            color: rgba(0, 0, 0, 0.86);
        }
    }

    .filter-type-badge {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        background: rgba(0, 0, 0, 0.06);
        padding: 2px 6px;
        border-radius: 2px;
        margin-left: 8px;
        min-width: 40px;
        text-align: center;
    }
}