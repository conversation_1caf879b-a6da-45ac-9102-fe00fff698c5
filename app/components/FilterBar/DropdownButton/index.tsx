import React, { ReactNode, useState } from "react";
import { But<PERSON>, Dropdown, Divider, Checkbox, Space } from "antd";
import { DownOutlined, UpOutlined, CloseOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import { SortOption, FilterOption } from "../index";

/** 筛选选择器组件属性接口 */
interface DropdownButtonProps {
    /** 图标 */
    icon?: ReactNode;
    /** 按钮文本 */
    text?: string;
    /** 按钮类型 */
    type?: "primary" | "default" | "link" | "text" | "dashed";
    /** 样式类型 */
    styleType?: "sort" | "filter";
    /** 按钮类名 */
    className?: string;
    // 排序选项列表 ,sort的时候使用
    sortOptions?: SortOption[];
    /** 筛选条件列表（filter 类型使用） */
    filterOptions?: FilterOption[];
    /** 值变化回调 */
    onChange?: (value: string | string[] | SortOption[] | FilterOption[]) => void;
}

/**
 * 筛选选择器组件
 * 可复用的带标签的选择器
 */
const DropdownButton: React.FC<DropdownButtonProps> = (props) => {
    const { icon, text, type = "text", styleType = "filter", sortOptions = [], filterOptions = [], onChange } = props;

    const [isOpen, setIsOpen] = useState(false);

    // 计算选中的数量
    const getSlectedCount = () => {
        if (styleType === "sort") {
            return sortOptions.filter((option) => option.direction !== null && option.direction !== undefined).length;
        }
        if (styleType === "filter") {
            return filterOptions.filter((option) => option.enabled).length;
        }
        return 0;
    };

    // 处理排序选项点击
    const handleSortOptionClick = (optionKey: string, direction: "asc" | "desc") => {
        const newOptions = sortOptions.map((option) => {
            if (option.key === optionKey) {
                // 双击取消
                const newDirection = option.direction === direction ? null : direction;
                return { ...option, direction: newDirection };
            }
            return option;
        });
        onChange?.(newOptions);
    };

    // 处理筛选条件的启用/停用
    const handleFilterOptionChange = (optionKey: string, enabled: boolean) => {
        const newOptions = filterOptions.map((option) => {
            if (option.key === optionKey) {
                return { ...option, enabled };
            }
            return option;
        });
        onChange?.(newOptions);
    };

    // 处理全选/全不选
    const handleToggleSelectAll = (e: CheckboxChangeEvent) => {
        const newOptions = filterOptions.map((option) => ({
            ...option,
            enabled: e.target.checked,
        }));
        onChange?.(newOptions);
    };

    // 弹出的内容item项
    const renderSortMenuItem = (option: SortOption) => (
        <div key={option.key} className={styles["sort-menu-item"]}>
            <span className={styles["sort-label"]}>{option.label}</span>
            <div className={styles["sort-buttons"]}>
                <Button
                    type={option.direction === "asc" ? "primary" : "text"}
                    size="small"
                    icon={<UpOutlined />}
                    onClick={(e) => {
                        e.stopPropagation();
                        handleSortOptionClick(option.key, "asc");
                    }}
                ></Button>
                <Button
                    type={option.direction === "desc" ? "primary" : "text"}
                    size="small"
                    icon={<DownOutlined />}
                    onClick={(e) => {
                        e.stopPropagation();
                        handleSortOptionClick(option.key, "desc");
                    }}
                ></Button>
            </div>
        </div>
    );

    // 排序的下拉框关闭
    const handleCloseDropdown = () => {
        setIsOpen(false);
    };

    // 筛选条件的总项
    const renderFilterMenuItem = (option: FilterOption) => {
        return (
            <div key={option.key} className={styles["filter-menu-item"]}>
                <Checkbox
                    checked={option.enabled}
                    onChange={(e) => handleFilterOptionChange(option.key, e.target.checked)}
                >
                    {option.label}
                </Checkbox>
                <span className={styles["filter-type-badge"]}>{option.type}</span>
            </div>
        );
    };

    // 排序内容总项
    const getDropdownMenu = () => {
        if (styleType == "sort") {
            return (
                <div className={styles["sort-dropContent"]}>
                    <div className={styles["sort-drop-header"]}>
                        <div>表格字段排序</div>
                        <Button
                            type="text"
                            size="small"
                            icon={<CloseOutlined />}
                            onClick={handleCloseDropdown}
                        ></Button>
                    </div>
                    <Divider style={{ margin: "4px 0" }}></Divider>
                    <div className={styles["sort-dropdown"]}>{sortOptions.map(renderSortMenuItem)}</div>
                </div>
            );
        }
        if (styleType == "filter") {
            const allSelected = filterOptions.every((option) => option.enabled);
            const someSelected = filterOptions.some((option) => option.enabled);
            const indeterminate = !allSelected && someSelected;

            return (
                <div className={styles["filter-dropContent"]}>
                    <div className={styles["filter-drop-header"]}>
                        <div>筛选条件配置</div>
                        <Button
                            type="text"
                            size="small"
                            icon={<CloseOutlined />}
                            onClick={handleCloseDropdown}
                        ></Button>
                    </div>
                    <Divider style={{ margin: "4px 0" }}></Divider>
                    <div style={{ padding: "0px 12px 4px 12px" }}>
                        <Checkbox indeterminate={indeterminate} checked={allSelected} onChange={handleToggleSelectAll}>
                            全选
                        </Checkbox>
                    </div>
                    <div className={styles["filter-content"]}>
                        <Space direction="vertical" style={{ width: "100%" }}>
                            {filterOptions.map(renderFilterMenuItem)}
                        </Space>
                    </div>
                </div>
            );
        }
    };

    const selectedCount = getSlectedCount();

    return (
        <div className={styles["filter-button-wrapper"]}>
            <Dropdown
                open={isOpen}
                onOpenChange={setIsOpen}
                popupRender={() => getDropdownMenu()}
                trigger={["click"]}
                placement="bottomLeft"
            >
                <Button type={type} className={styles[`${styleType}-button`]} icon={icon}>
                    {text}
                    {selectedCount > 0 && <span className={styles[`${styleType}-count`]}>{selectedCount}</span>}
                    {!selectedCount && <DownOutlined className={styles["dropdown-arrow"]} />}
                </Button>
            </Dropdown>
        </div>
    );
};

export default DropdownButton;
