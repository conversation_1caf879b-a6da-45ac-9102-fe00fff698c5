.filter-select-wrapper {
    height: 32px;
    :global(.ant-select) {
        height: 100%;
    }

    :global(.ant-select-selector) {
        border: none !important;
        background-color: #f5f5f5 !important;
        box-shadow: none !important;
    }

    :global(.ant-select-prefix) {
        color: rgba(0, 0, 0, 0.42) !important;
    }

    // 确保选择器在聚焦状态下也保持样式
    :global(.ant-select-focused .ant-select-selector) {
        border: none !important;
        background-color: #f5f5f5 !important;
        // box-shadow: none !important;
    }

    // 悬停状态样式
    :global(.ant-select:hover .ant-select-selector) {
        border: none !important;
        background-color: #e8e8e8 !important;
    }
}
