import { forwardRef, useEffect, useState, useImperativeHandle } from "react";
import { Layout, Tree, Avatar, Flex, Empty, Skeleton, Checkbox } from "antd";
import { GetCurrentPermissionDepartmentTreeResponse, QueryAvailableInterviewerDataResponse } from "./store";
import ResizableContainer from "../ResizableContainer";
import { TextView } from "../TextView";
import styles from "./index.module.scss";
import useInterviewerSelectStore from "./store";

export interface InterviewerSelectExpose {
    getSelectedUser: QueryAvailableInterviewerDataResponse[];
    setSelectedUser: (users: QueryAvailableInterviewerDataResponse[]) => void;
}

const InterviewerSelect = forwardRef<InterviewerSelectExpose>((props, ref) => {
    const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
    const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
    const [jobList, setJobList] = useState<GetCurrentPermissionDepartmentTreeResponse[]>([]);
    const [interviewerList, setInterviewerList] = useState<QueryAvailableInterviewerDataResponse[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [selectedInterviewers, setSelectedInterviewers] = useState<QueryAvailableInterviewerDataResponse[]>([]);

    const { queryCurrentPermissionDepartmentTree, queryAvailableInterviewerData } = useInterviewerSelectStore();

    useImperativeHandle(ref, () => ({
        getSelectedUser: selectedInterviewers,
        setSelectedUser: (users: QueryAvailableInterviewerDataResponse[]) => {
            setSelectedInterviewers(users);
        },
    }));

    useEffect(() => {
        queryCurrentPermissionDepartmentTree().then((res) => {
            if (res.length > 0) {
                setJobList(res);
                setSelectedKeys([res[0].dept_code_path]);
                setExpandedKeys([res[0].dept_code_path]);
            }
        });
    }, []);

    useEffect(() => {
        if (selectedKeys.length === 0) return;
        setLoading(true);
        queryAvailableInterviewerData({
            deptCodePath: selectedKeys[0],
        })
            .then((res) => {
                setInterviewerList(res);
            })
            .finally(() => {
                setLoading(false);
            });
    }, [selectedKeys, queryAvailableInterviewerData]);

    const handleInterviewerSelect = (interviewer: QueryAvailableInterviewerDataResponse, checked: boolean) => {
        if (checked) {
            setSelectedInterviewers((prev) => [...prev, interviewer]);
        } else {
            setSelectedInterviewers((prev) => prev.filter((item) => item.emp_id !== interviewer.emp_id));
        }
    };

    const isInterviewerSelected = (empId: string) => {
        return selectedInterviewers.some((item) => item.emp_id === empId);
    };

    return (
        <div className={styles["user-select-wrapper"]}>
            <Layout>
                <ResizableContainer
                    resizable={{ right: true }}
                    minWidth="20%"
                    maxWidth="26%"
                    initialWidth="20%"
                    initialHeight="600"
                >
                    <Layout.Sider width="100%">
                        <Tree
                            blockNode
                            selectedKeys={selectedKeys}
                            expandedKeys={expandedKeys}
                            treeData={jobList as any[]}
                            fieldNames={{ key: "dept_code_path", title: "dept_name" }}
                            onExpand={(expandKeys) => {
                                setExpandedKeys(expandKeys as string[]);
                            }}
                            onSelect={(selectedKeys) => {
                                if (selectedKeys.length > 0) {
                                    setSelectedKeys(selectedKeys as string[]);
                                }
                            }}
                            titleRender={(node) => {
                                return <TextView text={node.dept_name} />;
                            }}
                        />
                    </Layout.Sider>
                </ResizableContainer>
                <Layout.Content className={styles["interviewer-content"]}>
                    {loading ? (
                        <div className={styles["interviewer-skeleton"]}>
                            {Array.from({ length: 8 }).map((_, index) => (
                                <div key={index} className={styles["skeleton-item"]}>
                                    <Skeleton.Avatar active size="default" />
                                    <div className={styles["skeleton-content"]}>
                                        <Skeleton.Input active size="small" style={{ width: 120, marginBottom: 8 }} />
                                        <Skeleton.Input active size="small" style={{ width: 80 }} />
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : interviewerList.length > 0 ? (
                        <div className={styles["interviewer-list"]}>
                            {interviewerList.map((item) => (
                                <div
                                    key={item.emp_id}
                                    className={styles["interviewer-item"]}
                                    onClick={() => handleInterviewerSelect(item, !isInterviewerSelected(item.emp_id))}
                                >
                                    <Checkbox
                                        checked={isInterviewerSelected(item.emp_id)}
                                        style={{ marginRight: 12 }}
                                    />
                                    <Avatar src={item.avatar} alt="用户头像" style={{ flexShrink: 0 }} />
                                    <Flex vertical style={{ marginLeft: 12 }}>
                                        <div className={styles["name"]}>
                                            {item.emp_id}-{item.name}
                                        </div>
                                        <div className={styles["time"]}>
                                            {/* <TextView text={item.spare_date.join(",")} /> */}
                                            {item.spare_date.join(",")}
                                        </div>
                                    </Flex>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <Empty description="暂无有空闲日程的面试官数据" />
                    )}
                </Layout.Content>
            </Layout>
        </div>
    );
});
InterviewerSelect.displayName = "InterviewerSelect";

export default InterviewerSelect;
