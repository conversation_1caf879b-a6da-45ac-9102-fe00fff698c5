import { create } from "zustand";
import http from "@/app/request";
import { RespParams } from "@/app/typing";

// // 获取当前权限部门树的请体
// export interface GetCurrentPermissionDepartmentTreeParams {
//     page: number;
//     pageSize: number;
// }

// 获取当前权限部门树的响应
export interface GetCurrentPermissionDepartmentTreeResponse {
    new: boolean;
    id: string;
    parent_dept_code: string;
    dept_code: string;
    dept_name: string;
    dept_level: number;
    supv_emp_id: string;
    supv_opr_id: string;
    supv_name: string;
    supv_pinyin: string;
    leader_emp_id: string;
    leader_opr_id: string;
    leader_name: string;
    leader_pinyin: string;
    dept_code_path: string;
    dept_name_path: string;
    dept_type: string;
    create_user: string;
    update_user: string;
    is_deleted: boolean;
    gmt_create: string;
    gmt_modified: string;
}

// 查询可用面试官的数据请求体
export interface QueryAvailableInterviewerDataParams {
    deptCodePath: string;
}

// 查询可用面试官的数据响应
export interface QueryAvailableInterviewerDataResponse {
    emp_id: string;
    opr_id: string;
    name: string;
    avatar: string;
    spare_date: string[]; // 可面试时间
}

const useInterviewerSelectStore = create<InterviewerSelectStore>((set) => ({
    id: "", // id 的初始状态
    queryCurrentPermissionDepartmentTree: async () => {
        try {
            const response = await http.get("schedule/verbose/tree");
            const result = (await response.json()) as RespParams<GetCurrentPermissionDepartmentTreeResponse[]>;
            return result.data;
        } catch (error) {
            console.error("查询当前权限部门树报错:", error);
            throw error;
        }
    },
    queryAvailableInterviewerData: async (data: QueryAvailableInterviewerDataParams) => {
        try {
            const response = await http.get("schedule/verbose/members", data);
            const result = (await response.json()) as RespParams<QueryAvailableInterviewerDataResponse[]>;
            return result.data;
        } catch (error) {
            console.error("查询可用面试官数据报错:", error);
            throw error;
        }
    },
}));

export interface InterviewerSelectStore {
    id: string;
    queryCurrentPermissionDepartmentTree: () => Promise<GetCurrentPermissionDepartmentTreeResponse[]>;
    queryAvailableInterviewerData: (
        data: QueryAvailableInterviewerDataParams
    ) => Promise<QueryAvailableInterviewerDataResponse[]>;
}

export default useInterviewerSelectStore;
