.resizable-container {
    position: relative;
    display: flex;
    transition: width 0.3s ease;

    .resizer {
        opacity: 0;
        visibility: hidden;
    }

    .triggerBtn {
        opacity: 0;
        visibility: hidden;
    }

    &:hover {
        .resizer {
            opacity: 1;
            visibility: visible;
            background-color: #0099f2;
        }

        .triggerBtn {
            opacity: 1;
            visibility: visible;
            display: flex;
        }
    }
    &.collapsed {
        overflow: hidden;
        min-width: unset !important;
        // transform: translateX(-50%);

        :global {
            * {
                min-width: 0 !important;
            }
        }

        > *:not(.restoreBtn) {
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
            width: 0 !important;
            min-width: 0 !important;
            max-width: 0 !important;
            overflow: hidden;

            :global {
                .ant-layout-sider {
                    width: 0 !important;
                    min-width: 0 !important;
                    max-width: 0 !important;
                    flex: 0 0 0 !important;
                }
            }
        }

        .restoreBtn {
            opacity: 1;
            visibility: visible;
            left: 50%;
            transform: translateX(-50%);
        }
    }
}

.resizer {
    position: absolute;
    z-index: 10;
    cursor: ew-resize;
    transition: all ease 0.3s, opacity 0.2s ease, visibility 0.2s ease;

    &:hover,
    &:active {
        background-color: #0099f2;
    }
}

.top,
.bottom {
    height: 2px;
    left: 0;
    right: 0;
    cursor: ns-resize;
}

.left,
.right {
    width: 2px;
    top: 0;
    bottom: 0;
    cursor: ew-resize;
}

.top {
    top: -1px;
}

.bottom {
    bottom: -1px;
}

.left {
    left: -1px;
}

.right {
    right: -1px;
}

.triggerBtn {
    align-items: center;
    background-color: #fff;
    border-radius: 50%;
    color:#0099f2;
    cursor: pointer;
    display: none;
    height: 24px;
    width: 24px;
    justify-content: center;
    left: 50%;
    margin-left: -12px;
    margin-top: -12px;
    position: absolute;
    top: 50%;
    z-index: 1;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    box-shadow: 0 0 0 1px rgba(0,0,0,.05),0 9px 9px 0 rgba(0,0,0,.02),0 3px 3px 0 rgba(0,0,0,.02),0 1px 1px 0 rgba(0,0,0,.02);

    &:hover {
        border: 1px solid #0099f2;
    }
}

.restoreBtn {
    align-items: center;
    background-color: #fff;
    border-radius: 50%;
    color: #0099f2;
    cursor: pointer;
    display: flex;
    height: 24px;
    width: 24px;
    justify-content: center;
    position: absolute;
    top: 50%;
    left: 0;
    margin-top: -12px;
    z-index: 10;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease, transform 0.3s ease;
    transform: translateX(-50%);
    box-shadow: 0 0 0 1px rgba(0,0,0,.05),0 9px 9px 0 rgba(0,0,0,.02),0 3px 3px 0 rgba(0,0,0,.02),0 1px 1px 0 rgba(0,0,0,.02);
}