.resizable-container {
    position: relative;
    display: flex;
}

.resizer {
    position: absolute;
    z-index: 10;
    cursor: ew-resize;
    transition: all ease 0.3s;

    &:hover,
    &:active {
        background-color: #0099f2;
    }
}

.top,
.bottom {
    height: 2px;
    left: 0;
    right: 0;
    cursor: ns-resize;
}

.left,
.right {
    width: 2px;
    top: 0;
    bottom: 0;
    cursor: ew-resize;
}

.top {
    top: -1px;
}

.bottom {
    bottom: -1px;
}

.left {
    left: -1px;
}

.right {
    right: -1px;
}

.triggerBtn {
    align-items: center;
    background-color: #fff;
    border-radius: 50%;
    color:#0099f2;
    cursor: pointer;
    display: none;
    height: 24px;
    width: 24px;
    justify-content: center;
    left: 50%;
    margin-left: -12px;
    margin-top: -12px;
    position: absolute;
    top: 50%;
    z-index: 1;

    &:hover {
        display: flex;
    }
}