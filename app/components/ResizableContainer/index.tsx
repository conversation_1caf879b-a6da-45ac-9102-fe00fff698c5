import React, { useState, useRef, useEffect, useCallback } from "react";
import clsx from "clsx";
import styles from "./index.module.scss";
import Icon, { LeftOutlined, RightOutlined } from "@ant-design/icons";

type ResizableSide = "top" | "right" | "bottom" | "left";

interface ResizableProps {
    top?: boolean;
    right?: boolean;
    bottom?: boolean;
    left?: boolean;
}

export interface ResizableContainerProps {
    children: React.ReactNode;
    className?: string;
    resizable?: ResizableProps;
    minWidth?: number | string;
    maxWidth?: number | string;
    minHeight?: number | string;
    maxHeight?: number | string;
    initialWidth?: number | string;
    initialHeight?: number | string;
    collapsedWidth?: number | string;
    onCollapseChange?: (collapsed: boolean) => void;
}

const convertToPixels = (value: string | number, parentDimension: number): number => {
    if (typeof value === "number") {
        return value;
    }
    if (typeof value === "string") {
        if (value.endsWith("%")) {
            const percentage = parseFloat(value);
            if (!isNaN(percentage)) {
                return (parentDimension * percentage) / 100;
            }
        }
        if (value.endsWith("px")) {
            return parseFloat(value);
        }
    }
    const num = parseFloat(value as string);
    return isNaN(num) ? 0 : num;
};

function useResizable({
    initialWidth,
    initialHeight,
    minWidth,
    maxWidth,
    minHeight,
    maxHeight,
    containerRef,
    collapsedWidth,
    onCollapseChange,
}: {
    initialWidth?: number | string;
    initialHeight?: number | string;
    minWidth?: number | string;
    maxWidth?: number | string;
    minHeight?: number | string;
    maxHeight?: number | string;
    containerRef: React.RefObject<HTMLDivElement>;
    collapsedWidth?: number | string;
    onCollapseChange?: (collapsed: boolean) => void;
}) {
    const [dimensions, setDimensions] = useState({
        width: initialWidth ?? "auto",
        height: initialHeight ?? "auto",
    });

    const [isCollapsed, setIsCollapsed] = useState(false);
    const [previousWidth, setPreviousWidth] = useState<number | string>(initialWidth ?? "auto");

    const startDrag = useRef({
        startX: 0,
        startY: 0,
        startWidth: 0,
        startHeight: 0,
        side: "" as ResizableSide | "",
    });

    const getParentDimensions = useCallback(() => {
        if (containerRef.current?.parentElement) {
            const parentRect = containerRef.current.parentElement.getBoundingClientRect();
            return { parentWidth: parentRect.width, parentHeight: parentRect.height };
        }
        return { parentWidth: window.innerWidth, parentHeight: window.innerHeight };
    }, [containerRef]);

    useEffect(() => {
        const { parentWidth, parentHeight } = getParentDimensions();
        const newWidth = initialWidth !== undefined ? convertToPixels(initialWidth, parentWidth) : "auto";
        const newHeight = initialHeight !== undefined ? convertToPixels(initialHeight, parentHeight) : "auto";

        setDimensions({
            width: newWidth,
            height: newHeight,
        });
    }, [initialWidth, initialHeight, getParentDimensions]);

    const handleMouseDown = useCallback(
        (side: ResizableSide) => (e: React.MouseEvent<HTMLDivElement>) => {
            e.preventDefault();
            e.stopPropagation();

            const { parentWidth, parentHeight } = getParentDimensions();
            if (!containerRef.current) return;

            startDrag.current = {
                startX: e.clientX,
                startY: e.clientY,
                startWidth: containerRef.current.offsetWidth,
                startHeight: containerRef.current.offsetHeight,
                side: side,
            };

            const handleMouseMove = (event: MouseEvent) => {
                event.preventDefault();
                event.stopPropagation();

                let newWidth = startDrag.current.startWidth;
                let newHeight = startDrag.current.startHeight;

                const deltaX = event.clientX - startDrag.current.startX;
                const deltaY = event.clientY - startDrag.current.startY;

                switch (startDrag.current.side) {
                    case "right":
                        newWidth += deltaX;
                        break;
                    case "left":
                        newWidth -= deltaX;
                        break;

                    case "bottom":
                        newHeight += deltaY;
                        break;
                    case "top":
                        newHeight -= deltaY;
                        break;
                }

                const minW = minWidth !== undefined ? convertToPixels(minWidth, parentWidth) : 0;
                const maxW = maxWidth !== undefined ? convertToPixels(maxWidth, parentWidth) : Infinity;
                const minH = minHeight !== undefined ? convertToPixels(minHeight, parentHeight) : 0;
                const maxH = maxHeight !== undefined ? convertToPixels(maxHeight, parentHeight) : Infinity;

                newWidth = Math.max(minW, Math.min(newWidth, maxW));
                newHeight = Math.max(minH, Math.min(newHeight, maxH));

                requestAnimationFrame(() => {
                    setDimensions({ width: newWidth, height: newHeight });
                });
            };

            const handleMouseUp = () => {
                window.removeEventListener("mousemove", handleMouseMove);
                window.removeEventListener("mouseup", handleMouseUp);
            };

            window.addEventListener("mousemove", handleMouseMove);
            window.addEventListener("mouseup", handleMouseUp);
        },
        [getParentDimensions, minWidth, maxWidth, minHeight, maxHeight, containerRef]
    );

    const handleToggleCollapse = useCallback(() => {
        const { parentWidth } = getParentDimensions();

        if (isCollapsed) {
            // Expand: restore previous width
            setDimensions((prev) => ({
                ...prev,
                width: previousWidth,
            }));
            setIsCollapsed(false);
        } else {
            // Collapse: save current width and set to collapsed width
            setPreviousWidth(dimensions.width);
            const collapsedWidthPx = collapsedWidth ? convertToPixels(collapsedWidth, parentWidth) : 40;
            setDimensions((prev) => ({
                ...prev,
                width: collapsedWidthPx,
            }));
            setIsCollapsed(true);
        }

        onCollapseChange?.(!isCollapsed);
    }, [isCollapsed, previousWidth, dimensions.width, collapsedWidth, getParentDimensions, onCollapseChange]);

    return { dimensions, handleMouseDown, isCollapsed, handleToggleCollapse };
}

export default function ResizableContainer({
    children,
    className,
    resizable = { top: true, right: true, bottom: true, left: true },
    minWidth = "10%",
    maxWidth = "100%",
    minHeight = "10%",
    maxHeight = "100%",
    initialWidth = "100%",
    initialHeight = "100%",
    collapsedWidth = "40px",
    onCollapseChange,
}: ResizableContainerProps) {
    const containerRef = useRef<HTMLDivElement>(null);
    const { dimensions, handleMouseDown, isCollapsed, handleToggleCollapse } = useResizable({
        initialWidth,
        initialHeight,
        minWidth,
        maxWidth,
        minHeight,
        maxHeight,
        containerRef,
        collapsedWidth,
        onCollapseChange,
    });

    const style: React.CSSProperties = {
        position: "relative",
    };
    if (dimensions.width !== "auto") {
        style.width = `${dimensions.width}px`;
        // In collapsed state, override min-width to allow smaller sizes
        if (isCollapsed) {
            style.minWidth = `${dimensions.width}px`;
        }
    }
    if (dimensions.height !== "auto") style.height = `${dimensions.height}px`;

    const handleTriggerClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        handleToggleCollapse();
    };

    return (
        <div
            ref={containerRef}
            className={clsx(styles["resizable-container"], { [styles.collapsed]: isCollapsed }, className)}
            style={style}
        >
            {children}
            {resizable.top && (
                <div className={clsx(styles.resizer, styles.top)} onMouseDown={handleMouseDown("top")}></div>
            )}
            {resizable.right && (
                <div className={clsx(styles.resizer, styles.right)} onMouseDown={handleMouseDown("right")}>
                    <div className={styles["triggerBtn"]} onClick={handleTriggerClick}>
                        <Icon component={() => (isCollapsed ? <RightOutlined /> : <LeftOutlined />)}></Icon>
                    </div>
                </div>
            )}
            {resizable.bottom && (
                <div className={clsx(styles.resizer, styles.bottom)} onMouseDown={handleMouseDown("bottom")}></div>
            )}
            {resizable.left && (
                <div className={clsx(styles.resizer, styles.left)} onMouseDown={handleMouseDown("left")}></div>
            )}
            {/* {isCollapsed && (
                <div className={styles["restoreBtn"]} onClick={handleTriggerClick}>
                    <Icon component={() => <RightOutlined />}></Icon>
                </div>
            )} */}
        </div>
    );
}
