### 本地开发

1. 安装 nodejs 18 和 yarn，
2. 执行 `yarn install && yarn dev` 即可。
3. 如果你想本地部署，请使用 `yarn install && yarn build && yarn start` 命令，你可以配合 pm2 来守护进程，防止被杀死

4. **app/** - 主应用程序目录

    - `api/`: 所有后端API路由和集成
    - `components/`: React UI组件
    - `store/`: 状态管理逻辑
    - `utils/`: 通用工具函数

5. **components/** - 重要组件说明

    - `chat.tsx`: 核心聊天界面
    - `auth.tsx`: 用户认证组件
    - `settings.tsx`: 应用设置
    - `model-config.tsx`: AI模型配置

6. **store/** - 状态管理

    - `chat.ts`: 聊天相关状态
    - `mask.ts`: AI角色状态
    - `plugin.ts`: 插件系统状态
    - `sync.ts`: 数据同步状态

7. **utils/** - 工具函数

    - `cloud/`: 云服务集成
    - `audio.ts`: 音频处理
    - `chat.ts`: 聊天功能
    - `token.ts`: 令牌管理

8. **public/** - 静态资源

    - 图标和图片资源
    - 插件和提示词配置

9. **src-tauri/** - 桌面客户端
    - Rust实现的原生功能
    - 应用打包配置

### 特殊文件说明

- `.husky/`: Git hooks配置,用于代码提交前的格式化和测试
- `docker-compose.yml`: Docker部署配置
- `next.config.mjs`: Next.js框架配置
- `vercel.json`: Vercel部署配置

目前1.2版本用到的是利用 zustand 的中间件和 indexedDBStorage 进行的数据存储，详见indexedDB-storage.ts 存储数据截止到4.16的，数据格式导出在keyval-store_exported_data.json（后期迭代删除）

## Next.js App Router 当前不支持直接使用 . 引入的子组件，如 <Select.Option />、<Typography.Text /> 等，需要从路径引入这些子组件来避免错误。

```
NextChat
├── Dockerfile
├── LICENSE
├── README.md
├── app
│   ├── api
│   │   ├── [provider]
│   │   │   └── [...path]
│   │   ├── artifacts
│   │   │   └── route.ts
│   │   ├── auth.ts
│   │   ├── chat-proxy
│   │   │   └── route.ts
│   │   ├── common.ts
│   │   ├── config
│   │   │   └── route.ts
│   │   ├── deepseek.ts
│   │   ├── proxy.ts
│   │   ├── tencent
│   │   │   └── route.ts
│   │   ├── upstash
│   │   │   └── [action]
│   │   └── webdav
│   │       └── [...path]
│   ├── auth
│   │   └── page.tsx
│   ├── client
│   │   ├── api.ts
│   │   ├── controller.ts
│   │   └── platforms
│   │       ├── deepseek.ts
│   │       └── openai.ts
│   ├── command.ts
│   ├── components
│   │   ├── Button
│   │   │   ├── index.module.scss
│   │   │   └── index.tsx
│   │   ├── ChatDialog
│   │   │   └── index.tsx
│   │   ├── ChatDrawer
│   │   │   └── index.tsx
│   │   ├── ChatInput
│   │   │   ├── index.module.scss
│   │   │   └── index.tsx
│   │   ├── ConsultCard
│   │   │   ├── index.module.scss
│   │   │   └── index.tsx
│   │   ├── Echarts
│   │   │   ├── demo.tsx
│   │   │   └── index.tsx
│   │   ├── FileCard
│   │   │   ├── index.module.scss
│   │   │   └── index.tsx
│   │   ├── FilterBar
│   │   │   ├── DropdownButton
│   │   │   ├── FilterDatePicker
│   │   │   ├── FilterInput
│   │   │   ├── FilterSelect
│   │   │   ├── SmartSearchBar
│   │   │   ├── index.module.scss
│   │   │   └── index.tsx
│   │   ├── Header
│   │   │   ├── index.module.scss
│   │   │   └── index.tsx
│   │   ├── Loading
│   │   │   ├── index.module.scss
│   │   │   └── index.tsx
│   │   ├── PdfViewer
│   │   │   └── index.tsx
│   │   ├── PreviewFile
│   │   │   └── index.tsx
│   │   ├── RichTextEditor
│   │   │   ├── index.module.scss
│   │   │   └── index.tsx
│   │   ├── Tags
│   │   │   ├── index.module.scss
│   │   │   └── index.tsx
│   │   ├── TextView
│   │   │   ├── index.module.scss
│   │   │   └── index.tsx
│   │   ├── TopBanner
│   │   │   ├── index.tsx
│   │   │   └── styles.module.scss
│   │   ├── UserSelect
│   │   │   └── index.tsx
│   │   ├── api
│   │   │   └── index.ts
│   │   └── typing
│   │       └── index.ts
│   ├── config
│   │   ├── build.ts
│   │   ├── client.ts
│   │   └── server.ts
│   ├── constant.ts
│   ├── global.d.ts
│   ├── hooks
│   │   └── useFilterState.ts
│   ├── icons
│   │   ├── Candidate
│   │   │   ├── CandidateIcon.svg
│   │   │   ├── ErrorIcon.svg
│   │   │   ├── InterviewRightIcon.svg
│   │   │   ├── ProgressIcon.svg
│   │   │   └── SuccessIcon.svg
│   │   ├── EllipsisIcon.svg
│   │   ├── SearchIcon.svg
│   │   ├── add.svg
│   │   ├── arrow.svg
│   │   ├── auto.svg
│   │   ├── bot.svg
│   │   ├── cancel.svg
│   │   ├── chat
│   │   │   ├── chat-background.png
│   │   │   ├── chat-logo.svg
│   │   │   ├── copy.svg
│   │   │   ├── dislike.svg
│   │   │   ├── edit.svg
│   │   │   ├── enter.svg
│   │   │   ├── file.svg
│   │   │   ├── like.svg
│   │   │   ├── reset.svg
│   │   │   ├── retry.svg
│   │   │   ├── screenshot.svg
│   │   │   ├── share.svg
│   │   │   ├── star.svg
│   │   │   ├── test-card1.png
│   │   │   ├── test-card2.png
│   │   │   ├── test-card3.png
│   │   │   ├── test-card4.png
│   │   │   ├── title-logo.svg
│   │   │   ├── toBottom.svg
│   │   │   ├── upload.svg
│   │   │   ├── uploading.svg
│   │   │   └── voice.svg
│   │   ├── chatgpt.png
│   │   ├── clear.svg
│   │   ├── close.svg
│   │   ├── cloud-fail.svg
│   │   ├── cloud-success.svg
│   │   ├── config.svg
│   │   ├── confirm.svg
│   │   ├── connection.svg
│   │   ├── copy.svg
│   │   ├── dark.svg
│   │   ├── dashboard
│   │   │   ├── assign_task.svg
│   │   │   ├── collapse_icon.svg
│   │   │   ├── create_meeting.svg
│   │   │   ├── delete_icon.svg
│   │   │   ├── download.svg
│   │   │   ├── drag_icon.svg
│   │   │   ├── expand_icon.svg
│   │   │   ├── flow.svg
│   │   │   ├── flow_history.svg
│   │   │   ├── message.svg
│   │   │   ├── upload_document.svg
│   │   │   └── warning.svg
│   │   ├── delete.svg
│   │   ├── disposition.svg
│   │   ├── down.svg
│   │   ├── download.svg
│   │   ├── edit.svg
│   │   ├── eye-off.svg
│   │   ├── eye.svg
│   │   ├── fire.svg
│   │   ├── github.svg
│   │   ├── hd.svg
│   │   ├── interview
│   │   │   ├── interview-pass-rate.svg
│   │   │   ├── interview-session-count.svg
│   │   │   ├── offer-issued-count.svg
│   │   │   ├── pending-onboarding-count.svg
│   │   │   ├── resume-approved-count.svg
│   │   │   └── resume-screening-count.svg
│   │   ├── light.svg
│   │   ├── llm-icons
│   │   │   ├── chatglm.svg
│   │   │   ├── claude.svg
│   │   │   ├── deepseek.svg
│   │   │   ├── default.svg
│   │   │   ├── doubao.svg
│   │   │   ├── gemini.svg
│   │   │   ├── gemma.svg
│   │   │   ├── grok.svg
│   │   │   ├── hunyuan.svg
│   │   │   ├── meta.svg
│   │   │   ├── mistral.svg
│   │   │   ├── moonshot.svg
│   │   │   ├── openai.svg
│   │   │   ├── qwen.svg
│   │   │   └── wenxin.svg
│   │   ├── loading.svg
│   │   ├── logo.svg
│   │   ├── max.svg
│   │   ├── min.svg
│   │   ├── palette.svg
│   │   ├── pause.svg
│   │   ├── plugin.svg
│   │   ├── power.svg
│   │   ├── reload.svg
│   │   ├── save.svg
│   │   ├── share.svg
│   │   ├── sidebar
│   │   │   ├── account.svg
│   │   │   ├── new-chat.svg
│   │   │   ├── record.svg
│   │   │   ├── resize.svg
│   │   │   ├── sidebar-logo.svg
│   │   │   ├── talent.svg
│   │   │   └── work-space.svg
│   │   ├── size.svg
│   │   ├── sort.svg
│   │   ├── talentTool
│   │   │   ├── age.svg
│   │   │   ├── ai-reason-hint.svg
│   │   │   ├── ai-suggestion-prefix.svg
│   │   │   ├── company-status.svg
│   │   │   ├── detail-bg.png
│   │   │   ├── education-experience.svg
│   │   │   ├── education.svg
│   │   │   ├── export.svg
│   │   │   ├── favorite.svg
│   │   │   ├── filter-icon.svg
│   │   │   ├── filter.svg
│   │   │   ├── gender.svg
│   │   │   ├── grid.svg
│   │   │   ├── light.svg
│   │   │   ├── line.svg
│   │   │   ├── phone.svg
│   │   │   ├── risk.svg
│   │   │   ├── search.svg
│   │   │   ├── share.svg
│   │   │   ├── star-active.svg
│   │   │   ├── star.svg
│   │   │   ├── work-experience.svg
│   │   │   ├── zoom.svg
│   │   │   └── zoomout.svg
│   │   ├── three-dots.svg
│   │   ├── upload.svg
│   │   ├── view-toggle.svg
│   │   ├── voice-off.svg
│   │   ├── voice.svg
│   │   └── zoom.svg
│   ├── layout.tsx
│   ├── lib
│   │   ├── audio.ts
│   │   └── message.ts
│   ├── locales
│   │   ├── cn.ts
│   │   └── index.ts
│   ├── page.tsx
│   ├── polyfill.ts
│   ├── request
│   │   └── index.ts
│   ├── store
│   │   ├── access.ts
│   │   ├── chat.ts
│   │   ├── config.ts
│   │   ├── index.ts
│   │   ├── modules
│   │   │   ├── chat-content
│   │   │   └── interview-time
│   │   ├── plugin.ts
│   │   ├── prompt.ts
│   │   ├── rightSidebar.ts
│   │   ├── sync.ts
│   │   ├── update.ts
│   │   └── userInfo.ts
│   ├── styles
│   │   ├── animation.scss
│   │   ├── format.scss
│   │   ├── globals.scss
│   │   ├── highlight.scss
│   │   ├── markdown.scss
│   │   └── window.scss
│   ├── typing.ts
│   ├── utils
│   │   ├── aes.ts
│   │   ├── audio.ts
│   │   ├── auth-settings-events.ts
│   │   ├── chat.ts
│   │   ├── clone.ts
│   │   ├── cloud
│   │   │   ├── index.ts
│   │   │   ├── upstash.ts
│   │   │   └── webdav.ts
│   │   ├── cloudflare.ts
│   │   ├── format.ts
│   │   ├── hmac.ts
│   │   ├── hooks.ts
│   │   ├── indexedDB-storage.ts
│   │   ├── merge.ts
│   │   ├── model.ts
│   │   ├── ms_edge_tts.ts
│   │   ├── object.ts
│   │   ├── shortcut.ts
│   │   ├── store.ts
│   │   ├── stream.ts
│   │   ├── sync.ts
│   │   ├── tencent.ts
│   │   └── token.ts
│   ├── utils.ts
│   └── views
│       ├── AuthClient
│       │   ├── index.module.scss
│       │   └── index.tsx
│       ├── Candidate
│       │   ├── Assign.tsx
│       │   ├── CandidateAssign.tsx
│       │   ├── CandidateMain.tsx
│       │   ├── CandidateTree.tsx
│       │   ├── InterviewEvaluation.tsx
│       │   ├── InterviewQuestion.tsx
│       │   ├── ScheduleInterview.tsx
│       │   ├── api
│       │   ├── constant.ts
│       │   ├── index.module.scss
│       │   ├── index.tsx
│       │   ├── store
│       │   └── typing
│       ├── Chat
│       │   ├── api
│       │   ├── index.module.scss
│       │   └── index.tsx
│       ├── ChatList
│       │   └── chat-list.tsx
│       ├── Dashboard
│       │   ├── AddFlow
│       │   ├── Flow.tsx
│       │   ├── FlowHistory
│       │   ├── FlowPublish
│       │   ├── JobManage
│       │   ├── api
│       │   ├── board.tsx
│       │   ├── card-content.tsx
│       │   ├── constant.ts
│       │   ├── hooks
│       │   ├── index.module.scss
│       │   ├── index.tsx
│       │   ├── store
│       │   ├── table-content.tsx
│       │   ├── title-content.tsx
│       │   └── typing
│       ├── Emoji
│       │   └── emoji.tsx
│       ├── Error
│       │   └── error.tsx
│       ├── Home
│       │   ├── home.module.scss
│       │   └── home.tsx
│       ├── InterviewTime
│       │   ├── components
│       │   ├── index.module.scss
│       │   └── index.tsx
│       ├── Interviewer
│       │   ├── components
│       │   ├── index.module.scss
│       │   └── index.tsx
│       ├── LeftSidebar
│       │   ├── index.module.scss
│       │   └── index.tsx
│       ├── Login
│       │   ├── index.module.scss
│       │   └── index.tsx
│       ├── Markdown
│       │   ├── MDX-markdown.tsx
│       │   └── markdown.tsx
│       ├── MyInterview
│       │   ├── components
│       │   ├── index.module.scss
│       │   └── index.tsx
│       ├── NewChat
│       │   ├── card-content.tsx
│       │   ├── index.module.scss
│       │   ├── index.tsx
│       │   └── title-content.tsx
│       ├── RealtimeChat
│       │   ├── index.ts
│       │   ├── realtime-chat.module.scss
│       │   ├── realtime-chat.tsx
│       │   └── realtime-config.tsx
│       ├── RightSidebar
│       │   ├── index.module.scss
│       │   └── index.tsx
│       ├── TalentPoolNew
│       │   ├── LeftWrapper
│       │   ├── MainWrapper
│       │   ├── TalentDetail
│       │   ├── api
│       │   ├── components
│       │   ├── index.module.scss
│       │   └── index.tsx
│       ├── VoicePrint
│       │   ├── index.ts
│       │   ├── voice-print.module.scss
│       │   └── voice-print.tsx
│       ├── artifacts.module.scss
│       ├── artifacts.tsx
│       ├── auth.module.scss
│       ├── exporter.module.scss
│       ├── exporter.tsx
│       ├── input-range.module.scss
│       ├── input-range.tsx
│       ├── message-selector.module.scss
│       ├── message-selector.tsx
│       ├── model-config.module.scss
│       ├── model-config.tsx
│       ├── plugin.module.scss
│       ├── plugin.tsx
│       ├── search-chat.tsx
│       ├── settings.module.scss
│       ├── settings.tsx
│       ├── tts-config.tsx
│       ├── tts.module.scss
│       ├── ui-lib.module.scss
│       └── ui-lib.tsx
├── docker-compose.yml
├── docs
│   └── images
│       └── icon.svg
├── index.d.ts
├── jest.config.ts
├── jest.setup.ts
├── next-env.d.ts
├── next.config.mjs
├── package-lock.json
├── package.json
├── project_tree.txt
├── public
│   ├── android-chrome-192x192.png
│   ├── android-chrome-512x512.png
│   ├── apple-touch-icon.png
│   ├── audio-processor.js
│   ├── favicon-16x16.png
│   ├── favicon-32x32.png
│   ├── favicon.ico
│   ├── fonts
│   │   └── Kingsoft_Cloud_Font.ttf
│   ├── plugins.json
│   ├── prompts.json
│   ├── robots.txt
│   ├── serviceWorker.js
│   ├── serviceWorkerRegister.js
│   └── site.webmanifest
├── scripts
│   ├── delete-deployment-preview.sh
│   ├── fetch-prompts.mjs
│   ├── init-proxy.sh
│   ├── proxychains.template.conf
│   └── setup.sh
├── src-tauri
│   ├── Cargo.lock
│   ├── Cargo.toml
│   ├── build.rs
│   ├── src
│   │   ├── main.rs
│   │   └── stream.rs
│   └── tauri.conf.json
├── test
│   ├── model-available.test.ts
│   ├── model-provider.test.ts
│   ├── sum-module.test.ts
│   └── vision-model-checker.test.ts
├── tsconfig.json
├── vercel.json
└── yarn.lock
```
