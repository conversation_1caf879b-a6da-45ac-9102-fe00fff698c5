{"root": true, "extends": ["next/core-web-vitals", "eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json", "ecmaVersion": 2022, "sourceType": "module"}, "plugins": ["@typescript-eslint", "react", "react-hooks"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "@typescript-eslint/no-duplicate-enum-values": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-comment": "warn", "@typescript-eslint/no-unused-vars": "warn"}, "settings": {"react": {"version": "detect"}}, "ignorePatterns": ["node_modules/", "dist/", ".next/", "out/", "next.config.mjs"]}